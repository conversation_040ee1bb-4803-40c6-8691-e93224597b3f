/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.gui.utils.GuiLink;
import edu.memphis.ccrg.lida.framework.gui.utils.GuiUtils;
import edu.memphis.ccrg.lida.framework.gui.utils.NodeIcon;
import edu.memphis.ccrg.lida.framework.gui.utils.NodeStructureGuiAdapter;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.framework.shared.activation.Activatible;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.BroadcastQueue;
import edu.uci.ics.jung.algorithms.layout.FRLayout;
import edu.uci.ics.jung.algorithms.layout.Layout;
import edu.uci.ics.jung.algorithms.layout.util.Relaxer;
import edu.uci.ics.jung.visualization.VisualizationViewer;
import edu.uci.ics.jung.visualization.control.DefaultModalGraphMouse;
import edu.uci.ics.jung.visualization.control.ModalGraphMouse;
import org.apache.commons.collections15.Transformer;

import javax.swing.*;
import java.awt.*;
import java.util.Collection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * A {@link GuiPanel} to display the contents of the Workspace's broadcast queue.
 * <AUTHOR> J. McCall
 */
public class BroadcastQueuePanel extends GuiPanelImpl {

	/**
	 * Creates new form BroadcastQueuePanel
	 */
	public BroadcastQueuePanel() {
		initComponents();
	}

	/**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">                          
    private void initComponents() {

        jToolBar1 = new javax.swing.JToolBar();
        jLabel1 = new javax.swing.JLabel();
        jSeparator2 = new javax.swing.JToolBar.Separator();
        positionCountTextField = new javax.swing.JTextField();
        jSeparator1 = new javax.swing.JToolBar.Separator();
        jLabel2 = new javax.swing.JLabel();
        jSeparator3 = new javax.swing.JToolBar.Separator();
        positionTextField = new javax.swing.JTextField();
        jSeparator4 = new javax.swing.JToolBar.Separator();
        refreshButton = new javax.swing.JButton();
        jSeparator5 = new javax.swing.JToolBar.Separator();
        relaxButton = new javax.swing.JButton();
        jScrollPane1 = new javax.swing.JScrollPane();
        nsPanel = new javax.swing.JPanel();

        jToolBar1.setRollover(true);

        jLabel1.setText("Total queue positions");
        jToolBar1.add(jLabel1);
        jToolBar1.add(jSeparator2);

        positionCountTextField.setText("--");
        jToolBar1.add(positionCountTextField);
        jToolBar1.add(jSeparator1);

        jLabel2.setText("Currently displayed position");
        jToolBar1.add(jLabel2);
        jToolBar1.add(jSeparator3);

        positionTextField.setText("0");
        jToolBar1.add(positionTextField);
        jToolBar1.add(jSeparator4);

        refreshButton.setText("Refresh");
        refreshButton.setFocusable(false);
        refreshButton.setHorizontalTextPosition(javax.swing.SwingConstants.CENTER);
        refreshButton.setVerticalTextPosition(javax.swing.SwingConstants.BOTTOM);
        refreshButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
			public void actionPerformed(java.awt.event.ActionEvent evt) {
                refreshButtonActionPerformed(evt);
            }
        });
        jToolBar1.add(refreshButton);
        jToolBar1.add(jSeparator5);

        relaxButton.setText("Relax");
        relaxButton.setFocusable(false);
        relaxButton.setHorizontalTextPosition(javax.swing.SwingConstants.CENTER);
        relaxButton.setVerticalTextPosition(javax.swing.SwingConstants.BOTTOM);
        relaxButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
			public void actionPerformed(java.awt.event.ActionEvent evt) {
                relaxButtonActionPerformed(evt);
            }
        });
        jToolBar1.add(relaxButton);

        GroupLayout nsPanelLayout = new GroupLayout(nsPanel);
        nsPanel.setLayout(nsPanelLayout);
        nsPanelLayout.setHorizontalGroup(
            nsPanelLayout.createParallelGroup(GroupLayout.Alignment.LEADING)
            .addGap(0, 542, Short.MAX_VALUE)
        );
        nsPanelLayout.setVerticalGroup(
            nsPanelLayout.createParallelGroup(GroupLayout.Alignment.LEADING)
            .addGap(0, 466, Short.MAX_VALUE)
        );

        jScrollPane1.setViewportView(nsPanel);

        GroupLayout layout = new GroupLayout(this);
        this.setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(GroupLayout.Alignment.LEADING)
            .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addComponent(jScrollPane1, GroupLayout.PREFERRED_SIZE, 0, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(GroupLayout.Alignment.LEADING)
            .addGroup(GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 26, GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(jScrollPane1, GroupLayout.PREFERRED_SIZE, 470, GroupLayout.PREFERRED_SIZE))
        );
    }// </editor-fold>                                                   
   
	@Override
	public void initPanel(String[] params) {
		if (params == null || params.length == 0) {
			logger.log(Level.WARNING,
					"Error initializing BroadcastQueuePanel, not enough parameters.",0L);
		}else{
			queue = (BroadcastQueue)GuiUtils.parseFrameworkModule(params[0], agent);
			if (queue != null) {
	            draw();
			}
		}
	}
	private void draw() {
        // The Layout<V, E> is parameterized by the vertex and edge types

        Layout<Linkable, GuiLink> layout = new FRLayout<Linkable, GuiLink>(guiGraph);
        layout.setSize(new Dimension(300, 300)); // Sets initial size of the
        // The BasicVisualizationServer<V,E> is parameterized by the edge types
        vizViewer = new VisualizationViewer<Linkable, GuiLink>(layout);
        vizViewer.setPreferredSize(new Dimension(350, 350)); // Sets viewing area size
        // Show vertex and edge labels
        vizViewer.getRenderContext().setVertexLabelTransformer(new Transformer<Linkable, String>() {

            @Override
            public String transform(final Linkable linkable) {
                if (linkable instanceof Link) {
                    return ((Link) linkable).getCategory().getName();
                }
                return linkable.getTNname();
            }
        });

        // vv.getRenderContext().setEdgeLabelTransformer(new
        // ToStringLabeller<GuiLink>());
        // Create a graph mouse and add it to the visualization component
        DefaultModalGraphMouse<Linkable, GuiLink> gm2 = new DefaultModalGraphMouse<Linkable, GuiLink>();
        gm2.setMode(ModalGraphMouse.Mode.TRANSFORMING);
        vizViewer.getRenderContext().setVertexIconTransformer(new Transformer<Linkable, Icon>() {
            /*
             * Implements the Icon interface to draw an Icon with
             * background color
             */

            @Override
            public Icon transform(final Linkable v) {
                if (v instanceof Node) {
                    return NodeIcon.NODE_ICON;
                }
                else {
                    return NodeIcon.LINK_ICON;
                }
            }
        });
        vizViewer.setVertexToolTipTransformer(new Transformer<Linkable, String>() {

            @Override
            public String transform(final Linkable l) {
                String tip = null;
                if (l instanceof Activatible) {
                    if (l instanceof PamNode) {
                        PamNode pn = (PamNode) l;
                        tip = String.format("<html><b>%s</b><br/>Activation: %06.4f"
                                + "<br /> BaseActivation: %06.4f<br /> Threshold:  %06.4f </html>", pn,
                                pn.getActivation(), pn.getBaseLevelActivation(),
                                PamImpl0.getPerceptThreshold());
                    }
                    else {
                        Activatible n = l;
                        tip = String.format("<html><b>%s</b><br/>Activation: %06.4f</html>", n, n.getActivation());
                    }
                }
                return tip;
            }
        });
        vizViewer.setEdgeToolTipTransformer(new Transformer<GuiLink, String>() {

            @Override
            public String transform(final GuiLink l) {
                String tip = null;
                GuiLink gl = l;
                Link n = gl.getLink();
                tip = String.format("<html><b>%s</b><br/>Activation: %06.4f</html>", n, n.getActivation());
                return tip;
            }
        });

        vizViewer.setGraphMouse(gm2);

        jScrollPane1.setViewportView(vizViewer);
        vizViewer.fireStateChanged();
    }
    private void relax() {
        Relaxer relaxer = vizViewer.getModel().getRelaxer();
        if (relaxer != null) {
            relaxer.stop();
            relaxer.prerelax();
            relaxer.relax();
        }
    }

	@Override
	public synchronized void refresh() {
		Collection<?> positions = (Collection<?>) queue.getModuleContent();
		int positionCount = positions==null? 0: positions.size();
		positionCountTextField.setText(positionCount+"");
		//Display NodeStructure
		NodeStructure ns = queue.getPositionContent(selectedPosition);
		if(ns == null){
			ns = new NodeStructureImpl();
		}
		guiGraph.setNodeStructure(ns);
        Layout<Linkable, GuiLink> layout = vizViewer.getGraphLayout();
        layout.initialize();
	}
	
	private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {  
		String position = positionTextField.getText();
		try{
			selectedPosition = Integer.parseInt(position);
		}catch(NumberFormatException e){
			logger.log(Level.WARNING, "Error parsing queue position: {0}",position);
		}
        refresh();
    }                                             

    private void relaxButtonActionPerformed(java.awt.event.ActionEvent evt) {                                            
        relax();
    }    

	// Non-generated variables.
	private static final Logger logger = Logger.getLogger(BroadcastQueuePanel.class.getCanonicalName());
	private final NodeStructureGuiAdapter guiGraph = new NodeStructureGuiAdapter(new NodeStructureImpl());
    private VisualizationViewer<Linkable, GuiLink> vizViewer;
	private BroadcastQueue queue;
	private int selectedPosition;
	
	// Generated variables - do not modify                     
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JToolBar.Separator jSeparator1;
    private javax.swing.JToolBar.Separator jSeparator2;
    private javax.swing.JToolBar.Separator jSeparator3;
    private javax.swing.JToolBar.Separator jSeparator4;
    private javax.swing.JToolBar.Separator jSeparator5;
    private javax.swing.JToolBar jToolBar1;
    private javax.swing.JPanel nsPanel;
    private javax.swing.JTextField positionCountTextField;
    private javax.swing.JTextField positionTextField;
    private javax.swing.JButton refreshButton;
    private javax.swing.JButton relaxButton;
    // End of variables declaration      
}