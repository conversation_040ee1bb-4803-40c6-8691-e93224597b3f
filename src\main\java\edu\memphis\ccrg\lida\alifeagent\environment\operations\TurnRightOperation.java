/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.alifeagent.environment.operations;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.opreations.WorldOperation;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import java.util.logging.Level;
import java.util.logging.Logger;

public class TurnRightOperation implements WorldOperation {

	private static final Logger logger = Logger.getLogger(TurnRightOperation.class.getCanonicalName());
	private final String agentName = "agent";
	private final String attributeName = "direction";

	private NodeStructure nodeStructure = new NodeStructureImpl();

	@Override
	public Object performOperation(ALifeWorld world, ALifeObject subject,
			ALifeObject[] objects, Object... params) {
		ALifeObject agent = world.getObject(agentName);
		char currentDirection = (Character)agent.getAttribute(attributeName);
		switch(currentDirection){
			case 'N':
				agent.setAttribute(attributeName, 'E');
				break;
			case 'W':
				agent.setAttribute(attributeName, 'N');
				break;
			case 'S':
				agent.setAttribute(attributeName, 'W');
				break;
			case 'E':
				agent.setAttribute(attributeName, 'S');
				break;
		}

		Linkable linkable = AgentStarter.pam.getNode("^turnRight");
		if (linkable == null) {
			linkable = nodeStructure.getNeoNode("^turnRight");
			if (linkable != null) {
				AgentStarter.pam.addDefaultNode((Node) linkable);
			}
		}
//		AgentStarter.pam.receiveExcitation(linkable, 0.8, "feel");

		logger.log(Level.FINE, "agent turns right", TaskManager.getCurrentTick());
		return agent;
	}

}
