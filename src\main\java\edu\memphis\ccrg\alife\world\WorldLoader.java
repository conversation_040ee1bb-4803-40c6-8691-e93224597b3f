package edu.memphis.ccrg.alife.world;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.gui.ALifeWorldRenderer;
import edu.memphis.ccrg.alife.gui.DefaultWorldRenderer;
import edu.memphis.ccrg.alife.opreations.WorldOperation;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

public class WorldLoader {
    private static final Logger logger = Logger.getLogger(WorldLoader.class.getName());

    public static ALifeWorld loadWorld(int x, int y, Properties actionProperties, Properties objectsProperties) {
        if (x <= 0 || y <= 0) {
            return null;
        }
        ALifeWorld world = new ALifeWorldImpl(x, y, 100);
        loadActions(world, actionProperties);
        loadObjects(world, objectsProperties);
        return world;
    }

    public static ALifeWorldRenderer createRenderer(ALifeWorld world, Properties iconsProperties) {
        ALifeWorldRenderer renderer = new DefaultWorldRenderer(world.getWidth(), world.getHeight());
        loadIcons(renderer, iconsProperties);
        return renderer;
    }

    private static void loadActions(ALifeWorld world, Properties properties) {
        for (String name : properties.stringPropertyNames()) {
            WorldOperation action = null;
            try {
                action = (WorldOperation) Class.forName(properties.getProperty(name)).newInstance();
            } catch (InstantiationException ex) {
                logger.log(Level.SEVERE, ex.getMessage(), (Throwable) ex);
            } catch (IllegalAccessException ex2) {
                logger.log(Level.SEVERE, ex2.getMessage(), (Throwable) ex2);
            } catch (ClassNotFoundException ex3) {
                logger.log(Level.SEVERE, ex3.getMessage(), (Throwable) ex3);
            }
            if (action != null) {
                world.addAction(name, action);
            } else {
                logger.log(Level.WARNING, "failed to create action {0}", name);
            }
        }
    }

    private static void loadIcons(ALifeWorldRenderer renderer, Properties properties) {
        for (String name : properties.stringPropertyNames()) {
            String[] vals = properties.getProperty(name).split(",");
            int id = Integer.parseInt(vals[0].trim());
            boolean result = false;
            if (vals[1] != null) {
                result = renderer.addObjectIcon(id, name, vals[1].trim());
            }
            if (!result) {
                logger.log(Level.WARNING, "failed to load icon {0}", name);
            }
        }
    }

    private static void loadObjects(ALifeWorld world, Properties properties) {
        for (String name : properties.stringPropertyNames()) {
            String[] vals = properties.getProperty(name).split(",");
            try {
                int qty = Integer.parseInt(vals[0]);
                int x = Integer.parseInt(vals[1]);
                int y = Integer.parseInt(vals[2]);
                int iconId = Integer.parseInt(vals[3]);
                int size = Integer.parseInt(vals[4]);
                double health = Double.parseDouble(vals[5]);
                String className = vals[6].trim();
                Map<String, Object> attributes = new HashMap<>();
                for (int i = 7; i < vals.length; i++) {
                    String attrstr = vals[i].trim();
                    String attrname = attrstr.substring(0, attrstr.indexOf(40)).trim();
                    String attrType = attrstr.substring(attrstr.indexOf(40) + 1, attrstr.indexOf(58)).trim();
                    String attrValue = attrstr.substring(attrstr.indexOf(58) + 1, attrstr.indexOf(41)).trim();
                    Object value = null;
                    if ("string".equalsIgnoreCase(attrType)) {
                        value = attrValue;
                    } else if ("int".equalsIgnoreCase(attrType)) {
                        try {
                            value = Integer.valueOf(Integer.parseInt(attrValue));
                        } catch (NumberFormatException e) {
                            logger.log(Level.SEVERE, "attribute {0} of object {1} can not be read.", new Object[]{attrname, name});
                        }
                    } else if ("double".equalsIgnoreCase(attrType)) {
                        try {
                            value = Double.valueOf(Double.parseDouble(attrValue));
                        } catch (NumberFormatException e2) {
                            logger.log(Level.SEVERE, "attribute {0} of object {1} can not be read.", new Object[]{attrname, name});
                        }
                    } else if (!"char".equalsIgnoreCase(attrType)) {
                        value = attrValue.trim();
                    } else if (attrValue.length() > 0) {
                        value = Character.valueOf(attrValue.charAt(0));
                    }
                    attributes.put(attrname, value);
                }
                for (int n = 0; n < qty; n++) {
                    ALifeObject object = null;
                    try {
                        object = (ALifeObject) Class.forName(className).newInstance();
                    } catch (InstantiationException ex) {
                        logger.log(Level.SEVERE, ex.getMessage(), (Throwable) ex);
                    } catch (IllegalAccessException ex2) {
                        logger.log(Level.SEVERE, ex2.getMessage(), (Throwable) ex2);
                    } catch (ClassNotFoundException ex3) {
                        logger.log(Level.SEVERE, ex3.getMessage(), (Throwable) ex3);
                    }
                    if (object != null) {
                        object.setSize(size);
                        object.setIconId(iconId);
                        object.setName(name);
                        object.setHealth(health);
                        for (String attrName : attributes.keySet()) {
                            object.setAttribute(attrName, attributes.get(attrName));
                        }
                        for (int j = 0; j < 7; j++) {
                            int xx = x;
                            int yy = y;
                            if (xx == -1) {
                                xx = (int) (Math.random() * ((double) world.getWidth()));
                            }
                            if (yy == -1) {
                                yy = (int) (Math.random() * ((double) world.getHeight()));
                            }
                            if (world.addObject(object, xx, yy)) {
                                break;
                            }
                            if (j == 6) {
                                logger.log(Level.WARNING, "unable to add object {0} after {1} tries)", new Object[]{name, 7});
                            }
                        }
                    } else {
                        logger.log(Level.WARNING, "failed to create object {0}", name);
                    }
                }
            } catch (NumberFormatException e3) {
                logger.log(Level.SEVERE, "Error parsing definition of object {0}. Object not created.", name);
            }
        }
    }
}
