/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.attentioncodelets;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * An {@link AttentionCodelet} that seeks to create {@link Coalition}s from its sought content.
 * The resulting {@link Coalition} includes these nodes and possibly neighbors nodes.
 *
 * 概念提取，查找相近节点，与传统注意力不太一样，传统是筛选信息=聚焦
 */
public class NeighborhoodAttentionCodelet extends DefaultAttentionCodelet {

    private static final Logger logger = Logger.getLogger(NeighborhoodAttentionCodelet.class.getCanonicalName());
    private NodeStructure nodeStructure = new NodeStructureImpl();

    public NeighborhoodAttentionCodelet(String soughtContent){
        super.init();
        bindContent(soughtContent);
    }

    public NeighborhoodAttentionCodelet(){
        super.init();
    }

    /**
     * If this method is overridden, this init() must be called first! i.e. super.init();
	 * Will set parameters with the following names:<br/><br/>
     * 
     * <b>nodes</b> Labels of nodes that comprise this codelet's sought content<br/><br/>
     * If any parameter is not specified its default value will be used.
     * nodes 组成此小码的查找内容的节点的标签 *如果未指定任何参数，则将使用其默认值
     * @see DefaultAttentionCodelet#init()
	 */
    @Override
	public void init() {    // 不能有构造方法？否则init不执行
		super.init();
		super.attentionThreshold = 0.0;  //want nodes regardless of their activation
		String nodeLabels = getParam("nodes", "");
		if (nodeLabels != null && !nodeLabels.equals("")) {
//            GlobalInitializer globalInitializer = GlobalInitializer.getInstance();
            String[] labels = nodeLabels.split(",");
            for (String label : labels) {
                label = label.trim();
                bindContent(label);
            }
        }
    }

    public void bindContent(String label) {
        // todo 自上而下 自下而上 注意 图数据
        Node linkable = AgentStarter.pam.getNode(label);
        if (linkable == null) {
            linkable = nodeStructure.getNeoNode(label);
        }

        if (linkable != null) {
            soughtContent.addDefaultNode(linkable);
//            System.out.println("soughtContent: " + linkable.getName());
        }else{
            logger.log(Level.WARNING, "could not find node with label: {0} in global initializer", label);
        }
    }

    /**
     * Returns true if specified WorkspaceBuffer contains this codelet's sought content.
     * 如果指定的WorkspaceBuffer包含此Codelet的查找内容，则返回true
     * @param buffer the WorkspaceBuffer to be checked for content
     * @return true, if successful
     */
    @Override
    public boolean bufferContainsSoughtContent(WorkspaceBuffer buffer) {
        NodeStructure model = (NodeStructure) buffer.getBufferContent(null);
        // todo soughtContent是自上而下，自下而上靠激活度=信息刺激
        for (Linkable ln : soughtContent.getLinkables()) {
            if (!model.containsLinkable(ln)) {
                return false;
            }
        }
        logger.log(Level.FINEST, "Attn codelet {1} found sought content",
                new Object[]{TaskManager.getCurrentTick(), this});

//        for (Linkable ln : soughtContent.getLinkables()) {
//            System.out.println("当前soughtContent----" + ln.getName() + ln.getExtendedId());
//        }


        return true;
    }
   
}