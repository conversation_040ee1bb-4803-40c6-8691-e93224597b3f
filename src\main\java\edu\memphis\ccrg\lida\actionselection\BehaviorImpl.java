/*******************************************************************************
 * Copyright (c) 2009, 2010 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/**
 * Behavior.java
 *
 * <AUTHOR> 
 */
package edu.memphis.ccrg.lida.actionselection;

import edu.memphis.ccrg.lida.framework.shared.activation.ActivatibleImpl;
import edu.memphis.ccrg.lida.proceduralmemory.Condition;
import edu.memphis.ccrg.lida.proceduralmemory.Scheme;

import java.util.Collection;

/**
 * Default implementation of {@link Behavior}
 * <AUTHOR> J. McCall
 * <AUTHOR> Snaider
 */
public class BehaviorImpl extends ActivatibleImpl implements Behavior{

	/*
	 * Unique identifier
	 */
	private int behaviorId;

	/**
	 * The scheme from which this behavior was instantiated.  从中实例化此行为的方案
	 */
	private Scheme scheme;
	@Override
	public Long getBroadcostId() {
		return broadcostId;
	}
	@Override
	public void setBroadcostId(Long broadcostId) {
		this.broadcostId = broadcostId;
	}

	private Long broadcostId;
	
	/**
	 * Construct a new behavior with default parameters
	 */
	public BehaviorImpl(){
		super();
	}

	//Behavior methods
	@Override
	public void setId(int id) {
		behaviorId = id;
	}

	@Override
	public int getId() {
		return behaviorId;
	}

	@Override
	public String getName() {
		return scheme.getName();
	}

	@Override
	public void setName(String name) {

	}

	@Override
	public Scheme getScheme() {
		return scheme;
	}

	@Override
	public void setScheme(Scheme s) {
		scheme  = s;
	}
	
	//Object method
	@Override
	public String toString(){
		return scheme.getName() + "-" + getId();
	}
	
	@Override
	public Collection<Condition> getContextConditions() {
		return scheme.getContextConditions();
	}

	@Override
	public Collection<Condition> getAddingList() {
		return scheme.getAddingList();
	}

	@Override
	public Collection<Condition> getDeletingList() {
		return scheme.getDeletingList();
	}

	@Override
	public Action getAction() {
		return scheme.getAction();
	}

	@Override
	public String getLabel() {
		return scheme.getName();
	}

	@Override
	public void setLabel(String l) {		
	}
}