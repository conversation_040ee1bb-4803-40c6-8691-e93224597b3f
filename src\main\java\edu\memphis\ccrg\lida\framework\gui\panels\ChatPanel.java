/*
 * Created by <PERSON><PERSON>ormDesigner on Sun Dec 22 12:13:17 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

//import net.miginfocom.swing.MigLayout;

import javax.swing.*;
import java.awt.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ChatPanel extends GuiPanelImpl {
    public ChatPanel() {
        initComponents();
    }
    private static final Logger logger = Logger.getLogger(ChatPanel.class.getCanonicalName());

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jSplitPane1 = new JSplitPane();
        textPane = new JScrollPane();
        textPane1 = new JTextPane();
        panel1 = new JPanel();
        scrollPane1 = new JScrollPane();
        textPane2 = new JTextPane();
        button1 = new JButton();

        //======== this ========
        setPreferredSize(new Dimension(350, 300));

        //======== jSplitPane1 ========
        {
            jSplitPane1.setDividerLocation(255);
            jSplitPane1.setOrientation(JSplitPane.VERTICAL_SPLIT);
            jSplitPane1.setDividerSize(2);

            //======== textPane ========
            {
                textPane.setMinimumSize(new Dimension(150, 100));
                textPane.setPreferredSize(new Dimension(150, 100));
                textPane.setViewportView(textPane1);
            }
            jSplitPane1.setTopComponent(textPane);

            //======== panel1 ========
            {
                panel1.setPreferredSize(new Dimension(400, 44));
                panel1.setMinimumSize(new Dimension(150, 44));

                //======== scrollPane1 ========
                {

                    //---- textPane2 ----
                    textPane2.setMinimumSize(new Dimension(700, 30));
                    scrollPane1.setViewportView(textPane2);
                }

                //---- button1 ----
                button1.setText("text");

                GroupLayout panel1Layout = new GroupLayout(panel1);
                panel1.setLayout(panel1Layout);
                panel1Layout.setHorizontalGroup(
                    panel1Layout.createParallelGroup()
                        .addGroup(panel1Layout.createSequentialGroup()
                            .addGap(7, 7, 7)
                            .addComponent(scrollPane1, GroupLayout.PREFERRED_SIZE, 296, GroupLayout.PREFERRED_SIZE)
                            .addPreferredGap(LayoutStyle.ComponentPlacement.UNRELATED)
                            .addComponent(button1)
                            .addContainerGap())
                );
                panel1Layout.setVerticalGroup(
                    panel1Layout.createParallelGroup()
                        .addGroup(panel1Layout.createSequentialGroup()
                            .addContainerGap(GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                            .addGroup(panel1Layout.createParallelGroup()
                                .addComponent(scrollPane1)
                                .addGroup(panel1Layout.createSequentialGroup()
                                    .addComponent(button1)
                                    .addGap(0, 0, Short.MAX_VALUE)))
                            .addContainerGap(GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                );
            }
            jSplitPane1.setBottomComponent(panel1);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addComponent(jSplitPane1, GroupLayout.Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addGap(0, 0, 0)
                    .addComponent(jSplitPane1, GroupLayout.DEFAULT_SIZE, 300, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JSplitPane jSplitPane1;
    private JScrollPane textPane;
    private JTextPane textPane1;
    private JPanel panel1;
    private JScrollPane scrollPane1;
    private JTextPane textPane2;
    private JButton button1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables


}
