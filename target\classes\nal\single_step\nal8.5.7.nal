'********** [03 + 13 -> 14]:

'If key001 is reachable and the robot pick key001, it will hold key001.
<(&/,<(*,Self,key001) --> reachable>,(^pick,key001)) =/> <(*,Self,key001) --> hold>>. %1.00;0.90% 

1

'If key001 is reachable and the robot pick key001, it may hold key001.
<(&/,<(*,Self,key001) --> reachable>,(^pick,key001)) =/> <(*,Self,key001) --> hold>>. %1.00;0.43% 

1

'If key001 is reachable and the robot pick key001, it will hold key001.
''outputMustContain('<(&/,<(*,Self,key001) --> reachable>,(^pick,key001)) =/> <(*,Self,key001) --> hold>>. %1.00;0.91%')

