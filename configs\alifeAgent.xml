<?xml version="1.0" encoding="UTF-8"?>
<!--
    Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
    This program and the accompanying materials are made available 
    under the terms of the LIDA Software Framework Non-Commercial License v1.0 
    which accompanies this distribution, and is available at
    http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 -->
<lida xmlns="http://ccrg.cs.memphis.edu/LidaXMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://ccrg.cs.memphis.edu/LidaXMLSchema LidaXMLSchema.xsd ">
	<globalparams>
		<param name="ff" type="int">50</param>
	</globalparams>
    <taskmanager>
        <param name="taskManager.tickDuration" type="int">10</param>
        <param name="taskManager.maxNumberOfThreads" type="int"> 100</param>
    </taskmanager>
    <taskspawners>
        <taskspawner name="defaultTS">
            <class>edu.memphis.ccrg.lida.framework.tasks.TaskSpawnerImpl</class>
        </taskspawner>
    </taskspawners>
    <submodules> 
        <module name="Environment">
            <class>edu.memphis.ccrg.lida.alifeagent.environment.ALifeEnvironment</class>
            <param name="environment.healthDecayRate" type="double">0.003</param>
            <param name="environment.width" type="int">10</param>
            <param name="environment.height" type="int">10</param>
            <param name="environment.ticksPerRun" type="int">250</param>
            <param name="environment.operationsConfig">configs/operations.properties</param>
            <param name="environment.objectsConfig">configs/objects.properties</param>
            <param name="environment.agentName">agent</param>            
            <taskspawner>defaultTS</taskspawner>
        </module>

        <module name="SensoryMemory">
            <class>edu.memphis.ccrg.lida.alifeagent.modules.BasicSensoryMemory</class>
            <associatedmodule>Environment</associatedmodule>
            <taskspawner>defaultTS</taskspawner>
            <initialTasks>
                <task name="backgroundTask">
                    <tasktype>SensoryMemoryBackgroundTask</tasktype>
                    <ticksperrun>6</ticksperrun>
                </task>
            </initialTasks>
        </module>
        <!-- 这样没法激活常驻+也没法成大哥=需要接口类？但可激活=调用才进线程池 20220808 -->
<!--        <module name="GoalManager">-->
<!--            <class>edu.memphis.ccrg.lida.pam.tasks.GoalBackgroundTask</class>-->
<!--            <associatedmodule>PAMemory</associatedmodule>-->
<!--            <taskspawner>defaultTS</taskspawner>-->
<!--            <initialTasks>-->
<!--                <task name="GoalTask">-->
<!--                    <tasktype>GoalBackgroundTask</tasktype>-->
<!--                    <ticksperrun>5</ticksperrun>-->
<!--                </task>-->
<!--            </initialTasks>-->
<!--        </module>-->
	
        <module name="PAMemory">
            <class>edu.memphis.ccrg.lida.pam.PamImpl0</class>
            <associatedmodule>Environment</associatedmodule>
            <associatedmodule>GlobalWorkspace</associatedmodule>
            <associatedmodule>Workspace</associatedmodule>
            <param name="pam.Upscale" type="double">0.7</param>
            <param name="pam.Downscale" type="double">0.5</param>
            <param name="pam.perceptThreshold" type="double">0.4</param>
            <param name="pam.excitationTicksPerRun" type="int">1</param>
            <param name="pam.propagationTicksPerRun" type="int">1</param>
            <param name="pam.propagateActivationThreshold" type="double">0.05</param>

            <taskspawner>defaultTS</taskspawner>
            <initialTasks>
                <task name="HealthDetector">
                    <tasktype>HealthDetector</tasktype>
                    <ticksperrun>200</ticksperrun><!--20-->
                    <param name="nodes" type="string">goodHealth,fairHealth,badHealth</param>
                </task>

                <task name="ListenDetector">
                    <tasktype>ListenDetector</tasktype>
                    <ticksperrun>1500</ticksperrun>
                    <param name="nodes" type="string"></param>
                </task>

                <task name="ObjectDetector">
                    <tasktype>ObjectDetector</tasktype>
                    <ticksperrun>20</ticksperrun>
                    <param name="object" type="string"></param>
                </task>

                <task name="VarManagerTask0">
                    <tasktype>VarManagerTask</tasktype>
                    <ticksperrun>10</ticksperrun>
                </task>

                <!-- 目标动机线程做固有必须有大哥带，除非自己成大哥，factory那边是设置参数，两边都需要 -->
                <task name="GoalBackgroundTask">
                    <tasktype>GoalBackgroundTask</tasktype>
                    <ticksperrun>50</ticksperrun>
                </task>

            </initialTasks>
            <initializerclass>edu.memphis.ccrg.lida.pam.BasicPamInitializer</initializerclass>
        </module>

        <module name="Workspace">
            <class>edu.memphis.ccrg.lida.workspace.WorkspaceImpl</class>
            <submodules>
                <module name="EpisodicBuffer">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="PerceptualBuffer">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="CurrentSituationalModel">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="SceneGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="GrammarGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="UnderstandGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="NonGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

<!--                <module name="narmemory">-->
<!--                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>-->
<!--                    <taskspawner>defaultTS</taskspawner>-->
<!--                </module>-->

                <module name="FeelGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <!-- 目标用树图=竞争=非先进先出 -->
                <module name="GoalGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WSBufferImpl_graph</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="ConcentGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="WordGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="SeqGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>


                <module name="VisionGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="ListenGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="TextGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="VVGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="VListenGraph">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>


                <module name="BroadcastQueue">
                    <class>edu.memphis.ccrg.lida.workspace.workspacebuffers.BroadcastQueueImpl</class>
                    <param name="workspace.broadcastQueueCapacity" type="int">30</param>
                    <taskspawner>defaultTS</taskspawner>
                </module>
            </submodules>
            <taskspawner>defaultTS</taskspawner>
            <initialTasks>
                <task name="updateCsmBackground">
                    <tasktype>UpdateCsmBackgroundTask</tasktype>
                    <ticksperrun>5</ticksperrun>
                </task>
            </initialTasks>
        </module>
        <module name="StructureBuildingCodeletModule">
            <class>edu.memphis.ccrg.lida.workspace.structurebuildingcodelets.StructureBuildingCodeletModule</class>
            <associatedmodule>Workspace</associatedmodule>
            <taskspawner>defaultTS</taskspawner>
        </module>
        <module name="GlobalWorkspace">
            <class>edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspaceImpl</class>
            <param name="globalWorkspace.coalitionRemovalThreshold" type="double">0.0</param>
            <param name="globalWorkspace.coalitionDecayStrategy">coalitionDecay</param>
            <param name="globalWorkspace.refractoryPeriod" type="int">15</param>
            <!-- Trigger parameters -->
            <param name="globalWorkspace.delayNoBroadcast" type="int">100</param>
            <param name="globalWorkspace.delayNoNewCoalition" type="int">50</param>
            <param name="globalWorkspace.aggregateActivationThreshold" type="double">0.8</param>
            <param name="globalWorkspace.individualActivationThreshold" type="double">0.5</param>
            <taskspawner>defaultTS</taskspawner>
            <initializerclass>edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspaceInitializer</initializerclass>
        </module>
        <!--非固有线程，这里也可激活，但需要时才纳入线程池-->
        <module name="LanGen">
            <class>edu.memphis.ccrg.lida.nlanguage.LanGenImpl</class>
            <associatedmodule>Workspace</associatedmodule>
            <associatedmodule>PAMemory</associatedmodule>
            <param name="LanGen.ticksPerStep" type="int">14</param>
            <param name="LanGen.conditionDecayStrategy">conditionDecay</param>
            <param name="LanGen.schemeSelectionThreshold" type="double">0.1</param>
            <param name="LanGen.contextWeight" type="double">1.0</param>
            <param name="LanGen.addingListWeight"  type="double">1.0</param>
            <param name="linkCategory">temporal-link</param>
            <taskspawner>defaultTS</taskspawner>

<!--            <initialTasks>-->
<!--                <task name="VarManagerTask">-->
<!--                    <tasktype>VarManagerTask</tasktype>-->
<!--                    <ticksperrun>10</ticksperrun>-->
<!--                </task>-->
<!--            </initialTasks>-->

            <initializerclass>edu.memphis.ccrg.lida.nlanguage.LanGenInitializer</initializerclass>
        </module>

        <module name="ProceduralMemory">
            <class>edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemoryImpl</class>
            <associatedmodule>Workspace</associatedmodule>
            <param name="proceduralMemory.ticksPerStep" type="int">14</param>
           	<param name="proceduralMemory.conditionDecayStrategy">conditionDecay</param>
           	<param name="proceduralMemory.schemeSelectionThreshold" type="double">0.1</param>
           	<param name="proceduralMemory.contextWeight" type="double">1.0</param>
           	<param name="proceduralMemory.addingListWeight"  type="double">1.0</param>
           	<param name="proceduralMemory.schemeClass">edu.memphis.ccrg.lida.proceduralmemory.SchemeImpl</param>

            <param name="linkCategory">temporal-link</param>

            <param name="scheme.0">get|()()|get|190591|()()|0.01</param>

            <param name="scheme.1">left|()()|turnLeft|190653|()()|0.01</param>
            <param name="scheme.2">right|()()|turnRight|190730|()()|0.01</param>
            <param name="scheme.3">around|()()|turnAround|190533|()()|0.01</param>
            <param name="scheme.4">food|()()|moveAgent|190514|()()|0.1</param>
            <param name="scheme.5">origin|()()|eat|190532|()()|0.35</param>
            <param name="scheme.6">front|()()|fleeAgent|2043|()()|0.4</param>

            <taskspawner>defaultTS</taskspawner>                      
            <initializerclass>edu.memphis.ccrg.lida.proceduralmemory.BasicProceduralMemoryInitializer</initializerclass>
        </module>

        <module name="ActionSelection">
            <class>edu.memphis.ccrg.lida.actionselection.BasicActionSelection</class>
<!--            <class>edu.memphis.ccrg.lida.actionselection.BehaviorNetwork</class>-->
            <param name="actionselection.broadcastExcitationFactor" type="double">0.05</param>
            <param name="actionselection.successorExcitationFactor" type="double">0.04</param>
            <param name="actionselection.conflictorExcitationFactor" type="double">0.04</param>
            <param name="actionselection.predecessorExcitationFactor" type="double">0.1</param>
            <param name="actionselection.contextSatisfactionThreshold" type="double"> 0.5</param>
            <param name="actionselection.initialCandidateThreshold" type="double"> 1.0</param>
            <param name="actionselection.candidateThresholdDecayName" type="string">defaultDecay</param>
            <param name="actionselection.behaviorDecayName" type="string">behaviorDecay</param>             
            <param name="actionSelection.ticksPerRun" type="int">10</param>
	    <taskspawner>defaultTS</taskspawner>
        </module>
        <module name="SensoryMotorMemory">
            <class>edu.memphis.ccrg.lida.sensorymotormemory.BasicSensoryMotorMemory</class>
            <associatedmodule>Environment</associatedmodule>
            <param name="smm.processActionTaskTicks" type="int">1</param>
            <param name="smm.mapping.1">turnLeft,turnLeft</param>
            <param name="smm.mapping.2">turnRight,turnRight</param>
            <param name="smm.mapping.3">turnAround,turnAround</param>
            <param name="smm.mapping.4">moveAgent,moveAgent</param>
            <param name="smm.mapping.0">get,get</param>
            <param name="smm.mapping.5">eat,eat</param>
            <param name="smm.mapping.6">fleeAgent,flee</param>
            <taskspawner>defaultTS</taskspawner>
            <initializerclass>edu.memphis.ccrg.lida.sensorymotormemory.BasicSensoryMotorMemoryInitializer</initializerclass>
        </module>
    </submodules>
    <listeners>
        <listener>
			<listenertype>edu.memphis.ccrg.lida.sensorymotormemory.SensoryMotorMemoryListener</listenertype>
			<modulename>SensoryMotorMemory</modulename>
			<listenername>SensoryMemory</listenername>
		</listener>
		<listener>
			<listenertype>edu.memphis.ccrg.lida.sensorymemory.SensoryMemoryListener</listenertype>
			<modulename>SensoryMemory</modulename>
			<listenername>SensoryMotorMemory</listenername>
		</listener>
		<listener>
			<listenertype>edu.memphis.ccrg.lida.pam.PamListener</listenertype>
			<modulename>PAMemory</modulename>
			<listenername>Workspace</listenername>
		</listener>

        <listener>
            <listenertype>edu.memphis.ccrg.lida.pam.PamListener</listenertype>
            <modulename>LanGen</modulename>
            <listenername>Workspace</listenername>
        </listener>

		<listener>
			<listenertype>edu.memphis.ccrg.lida.workspace.WorkspaceListener</listenertype>
			<modulename>Workspace</modulename>
			<listenername>PAMemory</listenername>
		</listener>
		<listener>
			<listenertype>edu.memphis.ccrg.lida.globalworkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>PAMemory</listenername>
		</listener>
		<listener>
			<listenertype>edu.memphis.ccrg.lida.globalworkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>Workspace</listenername>
		</listener>
		<listener>
			<listenertype>edu.memphis.ccrg.lida.globalworkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>TransientEpisodicMemory</listenername>
		</listener>
		<listener>
			<listenertype>edu.memphis.ccrg.lida.globalworkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>ProceduralMemory</listenername>
		</listener>

		<listener>
			<listenertype>edu.memphis.ccrg.lida.globalworkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>LanGen</listenername>
		</listener>

		<listener>
			<listenertype>edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemoryListener</listenertype>
			<modulename>ProceduralMemory</modulename>
			<listenername>ActionSelection</listenername>
		</listener>
		<listener>
			<listenertype>edu.memphis.ccrg.lida.episodicmemory.LocalAssociationListener</listenertype>
			<modulename>TransientEpisodicMemory</modulename>
			<listenername>Workspace</listenername>
		</listener>
		<listener>
            <listenertype>edu.memphis.ccrg.lida.actionselection.ActionSelectionListener</listenertype>
            <modulename>ActionSelection</modulename>
            <listenername>SensoryMotorMemory</listenername>
        </listener>
	</listeners>
</lida>