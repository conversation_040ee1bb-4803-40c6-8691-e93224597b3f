package edu.memphis.ccrg.alife.example;

import edu.memphis.ccrg.alife.gui.ALifePanel;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import edu.memphis.ccrg.alife.world.WorldLoader;
import java.awt.EventQueue;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.FileReader;
import java.io.IOException;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.swing.GroupLayout;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JToolBar;
import javax.swing.LayoutStyle;

public class WorldFrame extends JFrame {
    private ALifePanel aLifePanel1;
    private JButton jButton1;
    private JToolBar jToolBar1;
    private ALifeWorld world;

    public WorldFrame() {
        initComponents();
        init();
    }

    private void initComponents() {
        this.aLifePanel1 = new ALifePanel();
        this.jToolBar1 = new JToolBar();
        this.jButton1 = new JButton();
        setDefaultCloseOperation(3);
        this.jToolBar1.setRollover(true);
        this.jButton1.setText("updateState");
        this.jButton1.setFocusable(false);
        this.jButton1.setHorizontalTextPosition(0);
        this.jButton1.setVerticalTextPosition(3);
        this.jButton1.addActionListener(new ActionListener() {
            /* class edu.memphis.ccrg.alife.example.WorldFrame.AnonymousClass1 */

            public void actionPerformed(ActionEvent evt) {
                WorldFrame.this.jButton1ActionPerformed(evt);
            }
        });
        this.jToolBar1.add(this.jButton1);
        GroupLayout layout = new GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING).addGroup(layout.createSequentialGroup().addContainerGap().addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING).addComponent(this.aLifePanel1, -1, -1, 32767).addComponent(this.jToolBar1, -2, 100, -2)).addContainerGap()));
        layout.setVerticalGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING).addGroup(GroupLayout.Alignment.TRAILING, layout.createSequentialGroup().addComponent(this.jToolBar1, -2, 25, -2).addPreferredGap(LayoutStyle.ComponentPlacement.RELATED).addComponent(this.aLifePanel1, -1, 582, 32767).addContainerGap()));
        pack();
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void jButton1ActionPerformed(ActionEvent evt) {
        this.world.updateWorldState();
        this.aLifePanel1.refresh();
    }

    public static void main(String[] args) {
        EventQueue.invokeLater(new Runnable() {
            /* class edu.memphis.ccrg.alife.example.WorldFrame.AnonymousClass2 */

            public void run() {
                new WorldFrame().setVisible(true);
            }
        });
    }

    private void init() {
        Properties operartionsProperties = new Properties();
        Properties objectsProperties = new Properties();
        Properties iconsProperties = new Properties();
        try {
            operartionsProperties.load(new FileReader("configs/operations.properties"));
            objectsProperties.load(new FileReader("configs/objects.properties"));
            iconsProperties.load(new FileReader("configs/icons.properties"));
        } catch (IOException ex) {
            Logger.getLogger(WorldFrame.class.getName()).log(Level.SEVERE, (String) null, (Throwable) ex);
        }
        this.world = WorldLoader.loadWorld(10, 10, operartionsProperties, objectsProperties);
        this.aLifePanel1.init(WorldLoader.createRenderer(this.world, iconsProperties), this.world, 50);
    }
}
