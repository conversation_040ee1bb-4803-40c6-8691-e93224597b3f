JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class java.awt.BorderLayout ) ) {
			name: "this"
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "toolbar"
				"rollover": true
				"preferredSize": new java.awt.Dimension( 50, 25 )
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "startPauseButton"
					"font": new java.awt.Font( "Lucida Grande", 0, 12 )
					"text": "Start / Pause "
					"toolTipText": "Toggles system operation"
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "startPauseButtonActionPerformed", true ) )
				} )
				add( new FormComponent( "javax.swing.JLabel" ) {
					name: "statusLabel"
					"font": new java.awt.Font( "Lucida Grande", 1, 12 )
					"horizontalAlignment": 0
					"text": " Paused "
					"toolTipText": "System run status"
				} )
				add( new FormComponent( "javax.swing.JToolBar$Separator" ) {
					name: "jSeparator2"
				} )
				add( new FormComponent( "javax.swing.JLabel" ) {
					name: "jLabel3"
					"font": new java.awt.Font( "Lucida Grande", 0, 12 )
					"text": "  Current tick: "
				} )
				add( new FormComponent( "javax.swing.JTextField" ) {
					name: "currentTickTextField"
					"editable": false
					"horizontalAlignment": 11
					"toolTipText": "Current tick"
					"maximumSize": new java.awt.Dimension( 100, 24 )
					"minimumSize": new java.awt.Dimension( 70, 24 )
					"preferredSize": new java.awt.Dimension( 70, 24 )
				} )
				add( new FormComponent( "javax.swing.JToolBar$Separator" ) {
					name: "jSeparator3"
				} )
				add( new FormComponent( "javax.swing.JToggleButton" ) {
					name: "ticksModeTB"
					"font": new java.awt.Font( "Lucida Grande", 0, 12 )
					"text": "Step mode"
					"toolTipText": "Toggles step-by-step mode"
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "ticksModeTBActionPerformed", true ) )
				} )
				add( new FormComponent( "javax.swing.JTextField" ) {
					name: "tiksTB"
					"horizontalAlignment": 4
					"text": "0"
					"toolTipText": "Enter a number of ticks here.  The system will run this number of ticks when adjacent 'Add' button is pressed and the system is in step mode."
					"maximumSize": new java.awt.Dimension( 100, 24 )
					"minimumSize": new java.awt.Dimension( 70, 24 )
					"preferredSize": new java.awt.Dimension( 60, 24 )
				} )
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "addTicksButton"
					"font": new java.awt.Font( "Lucida Grande", 0, 12 )
					"text": "Run ticks"
					"toolTipText": "Runs system the number of ticks specified in adjacent text field."
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "addTicksButtonActionPerformed", true ) )
				} )
				add( new FormComponent( "javax.swing.JToolBar$Separator" ) {
					name: "jSeparator1"
				} )
				add( new FormComponent( "javax.swing.JLabel" ) {
					name: "jLabel2"
					"font": new java.awt.Font( "Lucida Grande", 0, 12 )
					"text": "  Tick duration (ms): "
				} )
				add( new FormComponent( "javax.swing.JSpinner" ) {
					name: "tickDurationSpinner"
					"model": new javax.swing.SpinnerNumberModel( 1, 0, 1000, 1 )
					"toolTipText": "The system's current tick duration in milliseconds."
					"maximumSize": new java.awt.Dimension( 110, 24 )
					"minimumSize": new java.awt.Dimension( 63, 24 )
					"preferredSize": new java.awt.Dimension( 63, 24 )
					addEvent( new FormEvent( "javax.swing.event.ChangeListener", "stateChanged", "tickDurationSpinnerStateChanged", true ) )
				} )
				add( new FormComponent( "javax.swing.JToolBar$Separator" ) {
					name: "jSeparator4"
				} )
				add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class java.awt.FlowLayout ) ) {
					name: "jPanel1"
				} )
			}, new FormLayoutConstraints( class java.lang.String ) {
				"value": "Center"
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 868, 29 )
		} )
	}
}
