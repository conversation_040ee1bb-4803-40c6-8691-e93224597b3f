'********** abduction

'If robin is a type of bird then robin is a type of animal. 
<<robin --> bird> ==> <robin --> animal>>. 

'If robin can fly then robin is probably a type of animal. 
<<robin --> [flying]> ==> <robin --> animal>>. %0.8% 

19

'I guess if robin is a type of bird then robin can fly. 
''outputMustContain('<<robin --> bird> ==> <robin --> [flying]>>. %1.00;0.39%')

'I guess if robin can fly then robin is a type of bird.
''outputMustContain('<<robin --> [flying]> ==> <robin --> bird>>. %0.80;0.45%')


