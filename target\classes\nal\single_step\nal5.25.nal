'********** conditional deduction

'If robin is a bird and it's living, then robin is an animal
<(&&,<robin --> bird>,<robin --> [living]>) ==> <robin --> animal>>.  

'If robin can fly, then robin is a bird
<<robin --> [flying]> ==> <robin --> bird>>. 

1

'If robin is living and it can fly, then robin is an animal.
''outputMustContain('<(&&,<robin --> [flying]>,<robin --> [living]>) ==> <robin --> animal>>. %1.00;0.81%')
