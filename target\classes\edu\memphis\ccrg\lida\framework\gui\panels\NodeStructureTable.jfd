JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {comp jToolBar1::l::400:x};par l {comp nodeStructurePane::t::400:x}"
			"$verticalGroup": "par l {seq l {comp jToolBar1:::p:25:p, space ::275:x}};par l {seq t {space :p:27:p, comp nodeStructurePane::::273:x}}"
		} ) {
			name: "this"
			"minimumSize": new java.awt.Dimension( 196, 100 )
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "jToolBar1"
				"rollover": true
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "refreshButton"
					"text": "Refresh"
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "refreshButtonActionPerformed", true ) )
				} )
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "nodeStructurePane"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "nodeStructureTable"
					"autoResizeMode": 0
					"maximumSize": new java.awt.Dimension( 1000, 1000 )
					"preferredScrollableViewportSize": new java.awt.Dimension( 450, 310 )
					auxiliary() {
						"JavaCodeGenerator.preInitCode": "${field}.setModel(nodeStructureTableModel);"
					}
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
