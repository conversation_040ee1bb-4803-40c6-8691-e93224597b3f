/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.actionselection;

import java.util.Collection;

import edu.memphis.ccrg.lida.framework.FrameworkModule;

/**
 * Specification for the action selection module of LIDA. LIDA动作选择模块的规范
 * 
 * <AUTHOR>
 * 
 */
public interface ActionSelection extends FrameworkModule {

	/**
	 * Adds specified {@link ActionSelectionListener}.
	 * 
	 * @param l a module that receives selected actions from {@link ActionSelection}
	 *          从{@link ActionSelection}接收选定动作的模块
	 */
	public void addActionSelectionListener(ActionSelectionListener l);

	/**
	 * Adds specified {@link PreafferenceListener}
	 * @param l a module that receives preafference from {@link ActionSelection}
	 */
	public void addPreafferenceListener(PreafferenceListener l);
	
	/**
	 * Selects a behavior (containing an action) for execution.
	 * @param behaviors {@link Collection} of behaviors currently available in the module
	 * @param candidateThreshold threshold for a behavior to be a candidate
	 * @return winning Behavior or null if none was chosen
	 *
	 * 选择要执行的行为（包含动作）。 * @param行为{@link Collection}模块中当前可用的行为*
	 * param候选者行为成为候选者的阈值* @返回获胜行为；如果未选择，则为null
	 */
	public Behavior selectBehavior(Collection<Behavior> behaviors, double candidateThreshold);
	
	/**
	 * Returns a view of the behaviors currently in {@link ActionSelection}
	 * 返回当前在{ActionSelection}中的行为的视图
	 * @return a {@link Collection} of {@link Behavior} objects
	 */
	public Collection<Behavior> getBehaviors();
}
