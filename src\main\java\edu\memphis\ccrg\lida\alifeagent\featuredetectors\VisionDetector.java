/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.alifeagent.featuredetectors;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.lida.framework.shared.ConcurrentHashSet;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.tasks.MultipleDetectionAlgorithm;
import edu.memphis.ccrg.lida.sensorymemory.SensoryMemory;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class VisionDetector extends MultipleDetectionAlgorithm {
	private Map<String, Object> detectorParams = new HashMap<String, Object>();
	private Set<ALifeObject> originObjects = new ConcurrentHashSet<ALifeObject>();
	private Set<ALifeObject> nextCellObjects = new ConcurrentHashSet<ALifeObject>();

	/**
	 * Default constructor. Associated {@link Linkable},
	 * {@link PAMemory} and {@link SensoryMemory} must be set using setters.
	 */
	public VisionDetector() {
	}

	@Override
	public void init() {
		super.init();// 并不是单纯视觉探测，object范围广
	}

	@Override
	public void detectLinkables() {
		// todo 主动睁开眼=打开摄像头，帧率恒定，视觉被动传输，到理解模块，不管有没有理解
		// 目前测试模式视觉，先完全理解再下一帧

	}

}
