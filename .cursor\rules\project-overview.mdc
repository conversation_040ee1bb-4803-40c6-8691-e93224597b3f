---
description:
globs:
alwaysApply: false
---
# NARS项目概述

## 项目结构
- `src/main/java/org/opennars/` - 主要源代码目录
  - `language/` - 语言相关组件
  - `entity/` - 实体类
  - `inference/` - 推理相关代码
  - `io/` - 输入输出处理

## 核心组件
- [Terms.java](mdc:src/main/java/org/opennars/language/Terms.java) - 术语处理工具类
- [Sentence.java](mdc:src/main/java/org/opennars/entity/Sentence.java) - 句子实体类
- [TermLink.java](mdc:src/main/java/org/opennars/entity/TermLink.java) - 术语链接类

## 主要功能
- 术语处理和转换
- 句子解析和构建
- 推理规则应用
- 术语链接管理

## 开发指南
- 所有术语相关的操作都应该通过`Terms`工具类进行
- 句子构建应该使用`Sentence`类
- 术语链接应该使用`TermLink`类
