'********** induction on events 

'<PERSON> is opening door_101 now
<(*,<PERSON>,door_101) --> open>. :|: 

11

'<PERSON> is not entering room_101 now
<(*,<PERSON>,room_101) --> enter>. :|: %0% 

10

'If <PERSON> open the door_101, he will not enter room_101
''outputMustContain('<(&/,<(*,<PERSON>,door_101) --> open>,+11) =/> <(*,<PERSON>,room_101) --> enter>>. :!11: %0.00;0.45%') 
'If <PERSON> enter the door_101, it doesn't mean he will enter the room_101
''outputMustContain('<(&/,<(*,<PERSON>,door_101) --> open>,+11) </> <(*,<PERSON>,room_101) --> enter>>. :!11: %0.00;0.45%')
