[#escape x as (x)!?html]

[#include 'include/inc_header.html'/]
<div class="mxmg_mycase_box">
	<div class="w1180">

		<!--<div class="box_title">-->
			<!--<div class="l">[#if oprt=='edit']稿件修改[#else]稿件发布[/#if]</div>-->
		<!--</div>-->
		
		<form id="validForm" action="${ctx}/my/naotucreate" method="post" style="padding:10px 16px;">
			<input type="hidden" name="nextUrl" value="${ctx}/info/"/>
			<input type="hidden" name="id" value="${bean.id}"/>
			<!-- 对于退回的稿件，修改同时再次提交审核 -->
			<input type="hidden" name="pass" value="true"/>
			<div class="form-section">
				<div class="form-label"><em class="required">*</em>栏目：</div>
				<div class="form-content">
					<select name="nodeId" class="required">
						[#list nodeList as node]
							[#if node.isContriPerm(user, groups)]
								[#if node.treeLevel > 0]<option value="${node.id}"[#if (bean.node.id)?? && node.id == bean.node.id] selected="selected"[/#if]>[#if node.treeLevel > 1][#list 2..node.treeLevel as i]--[/#list][/#if]${node.name}</option>[/#if]
							[/#if]
						[/#list]
					</select>
				</div>
			</div>
			<div class="form-section">
				<div class="form-label"><em class="required">*</em>标题：</div>
				<div class="form-content"><input type="text" name="title" value="${bean.title}" class="required" style="width:500px;"/></div>
			</div>
			<div class="form-section">
				<div class="form-label"><em class="required">*</em>正文：</div>
				<div class="form-content">
					<!-- <textarea id="text" name="text">${bean.text}</textarea> -->
					<script id="text" name="text" type="text/plain"></script>
					<script type="text/javascript">
					$(function() {
						window.UEDITOR_HOME_URL = "${ctx}/static/vendor/ueditor/";
						var editor_text = UE.getEditor('text',{
							toolbars: window.UEDITOR_CONFIG.toolbars_BasicPage,
							//initialFrameWidth:700,
							initialFrameHeight:200,
					    serverUrl:"${ctx}/ueditor?ueditor=true"
						});
						editor_text.ready(function() {
				       editor_text.setContent("[#noescape]${(bean.text)!?js_string}[/#noescape]");
				    });
					});
					</script>
				</div>
			</div>
			<div class="form-section">
				<input type="submit" value="提交1" style="height:25px;width:60px;"/>

				<!--<input type="submit" href="javascript:add1();" value="提交" style="height:25px;width:60px;"/>-->

				<!--<a href="javascript:add1();" style="background-color: #42ACE9;">-->
						  	<!--<span><em>-->
						      	<!--我也要-->
						   <!--</em>创作</span>-->
				<!--</a>-->

			</div>
		</form>
	</div>

    <script>
        /* Simplax Effect - http://github.com/arkaindas/simplax */
        window.onload=function(){
            document.body.style.backgroundAttachment="fixed";
            window.onscroll=function () {
                document.body.style.backgroundPosition = "0px " + (0 + (Math.max(document.documentElement.scrollTop, document.body.scrollTop) / 50)) + "px";
            }
        }
    </script>
    <script src="${ctx}/static/vendor/My97DatePicker/cn_WdatePicker.js"></script>
    <script src="${ctx}/static/vendor/ueditor/ueditor.config.js"></script>
    <script src="${ctx}/static/vendor/ueditor/ueditor.all.min.js"></script>
    <script src="${ctx}/static/vendor/ueditor/lang/zh-cn/zh-cn.js"></script>

</div>
[/#escape]