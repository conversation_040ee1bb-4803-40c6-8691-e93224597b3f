'********** induction

'If robin is a type of bird then robin is a type of animal. 
<<robin --> bird> ==> <robin --> animal>>. 

'If robin is a type of bird then robin can fly. 
<<robin --> bird> ==> <robin --> [flying]>>. %0.80% 

140

'I guess if robin can fly then robin is a type of animal. 
''outputMustContain('<<robin --> [flying]> ==> <robin --> animal>>. %1.00;0.39%')

'I guess if robin is a type of animal then robin can fly. 
''outputMustContain('<<robin --> animal> ==> <robin --> [flying]>>. %0.80;0.45%')

