/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.attentioncodelets;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.activation.LearnableImpl;
import edu.memphis.ccrg.lida.framework.tasks.CodeletImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.globalworkspace.CoalitionImpl;
import edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspace;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Abstract implementation of {@link AttentionCodelet} that checks the CSM for desired
 * content.  If this is found it creates a
 * {@link Coalition} and adds it to the {@link GlobalWorkspace}.
 * 
 * <AUTHOR> J. McCall
 * 
 */
public abstract class AttentionCodeletImpl extends CodeletImpl implements AttentionCodelet {

    private static final Logger logger = Logger.getLogger(AttentionCodeletImpl.class.getCanonicalName());
    private static final int DEFAULT_CODELET_REFRACTORY_PERIOD = 20;
    private int codeletRefractoryPeriod;

    /**
     * Where codelet will look for and retrieve sought content from
     * 小码将在哪里寻找并从中检索所需内容 当前情境模型
     */
    protected WorkspaceBuffer currentSituationalModel;
    /**
     * where {@link Coalition}s will be added
     */
    protected GlobalWorkspace globalWorkspace;

    /**
     * If this method is overridden, this init must be called first! i.e. super.init();
	 * Will set parameters with the following names:<br/><br/>
     * 
     * <b>refractoryPeriod</b> period in ticks that will pass after this codelet creates a coaltion before it can create another<br/> 
     * <br/> 
     * 
     * If any parameter is not specified its default value will be used.
     * @see LearnableImpl#init()
	 */
    @Override
    public void init() {
    	super.init();
        Integer refractoryPeriod = (Integer) getParam("refractoryPeriod", DEFAULT_CODELET_REFRACTORY_PERIOD);
        setRefractoryPeriod(refractoryPeriod);
    }

    @Override
    public void setAssociatedModule(FrameworkModule module, String usage) {
        if (module instanceof WorkspaceBuffer) {
            currentSituationalModel = (WorkspaceBuffer) module;
        } else if (module instanceof GlobalWorkspace) {
            globalWorkspace = (GlobalWorkspace) module;
        } else {
            logger.log(Level.WARNING, "module {1} cannot be associated",
                    new Object[]{TaskManager.getCurrentTick(), module});
        }
    }

    /**
     * If sought content is found it the CSM, then retrieve it
     * and create a coalition from it finally adding it to the
     * {@link GlobalWorkspace}.
     */
    @Override
    protected void runThisFrameworkTask() {

        WorkspaceBuffer csm = (WorkspaceBuffer) ((WorkspaceImpl) AgentStarter.pam
                .getListener()).getSubmodule(ModuleName.PerceptualBuffer);

        GlobalWorkspace globalWorkspace0 = AgentStarter.pam.getGlobalWorkspace();

        if (bufferContainsSoughtContent(csm)) {
            // todo 注意=筛选信息，目前只是简单复制
            NodeStructure csmContent = retrieveWorkspaceContent(csm);
            if(csmContent == null){
            	logger.log(Level.WARNING, "Null WorkspaceContent returned in {1}. Coalition cannot be formed.",
            			new Object[]{TaskManager.getCurrentTick(), this});
            }else if (csmContent.getLinkableCount() > 0) {
                Coalition coalition = new CoalitionImpl(csmContent, this);
                // 多线程汇总交互？
                globalWorkspace0.addCoalition(coalition);
                logger.log(Level.FINER, "{1} adds new coalition with activation {2}",
                        new Object[]{TaskManager.getCurrentTick(), this, coalition.getActivation()});
                setNextTicksPerRun(codeletRefractoryPeriod);
            }

//            if(task0 != null) {
////                KgmakerApplication.nar.memory.inputTask(KgmakerApplication.nar, task0);
//                        KgmakerApplication.nar.addInput("$0.95;0.95;0.95$<(&/,<(*,{foodOrigin},$2) --> at>,+12,(^eat,{SELF}),+100) =/> <{SELF} --> [morehealthy]>>.");
//                task0 = null;
//            }

//            if(task0 != null) {
//                Timer nTimer = new Timer();
//                nTimer.schedule(new TimerTask() {
//                    @Override
//                    public void run() {
//                        KgmakerApplication.nar.memory.inputTask(KgmakerApplication.nar, task0);
////                  KgmakerApplication.nar.addInput("$0.95;0.95;0.95$<(&/,<(*,{foodOrigin},$2) --> at>,+12,(^eat,{SELF}),+100) =/> <{SELF} --> [morehealthy]>>.");
//                        task0 = null;
//                    }
//                },1000);
//
//                nTimer.cancel();
//            }

            cancel();
        }

    }

    @Override
    public void setRefractoryPeriod(int ticks) {
        if (ticks > 0) {
            codeletRefractoryPeriod = ticks;
        } else {
            codeletRefractoryPeriod = DEFAULT_CODELET_REFRACTORY_PERIOD;
            logger.log(Level.WARNING,
                    "refractory period must be positive, using default value",
                    TaskManager.getCurrentTick());
        }

    }

    @Override
    public int getRefractoryPeriod() {
        return codeletRefractoryPeriod;
    }
}