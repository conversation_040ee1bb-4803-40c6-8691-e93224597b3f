package edu.memphis.ccrg.alife.gui;

import javax.swing.*;
import java.awt.*;
import java.awt.image.ImageObserver;

/* access modifiers changed from: private */
public class ImagePanel extends JPanel {
    private final ALifePanel aLifePanel;

    public ImagePanel(ALifePanel aLifePanel) {
        this.aLifePanel = aLifePanel;
        if (aLifePanel.img != null) {
            setSize(aLifePanel.scaleImgWidth, aLifePanel.scaleImgHeight);
        }
    }

    /* access modifiers changed from: protected */
    public void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.setColor(Color.LIGHT_GRAY);
        g.fillRect(0, 0, getWidth(), getHeight());
        if (aLifePanel.scaleImg != null) {
            g.drawImage(aLifePanel.scaleImg, (getWidth() - aLifePanel.scaleImg.getWidth((ImageObserver) null)) / 2,
                    (getHeight() - aLifePanel.scaleImg.getHeight((ImageObserver) null)) / 2, (ImageObserver) null);
        }
    }

    public Point getCellCoordinates(int x, int y) {
        return new Point((x - ((getWidth() - aLifePanel.scaleImgWidth) / 2)) / (aLifePanel.scaleImgWidth / aLifePanel.world.getWidth()),
                (y - ((getHeight() - aLifePanel.scaleImgHeight) / 2)) / (aLifePanel.scaleImgHeight / aLifePanel.world.getHeight()));
    }
}
