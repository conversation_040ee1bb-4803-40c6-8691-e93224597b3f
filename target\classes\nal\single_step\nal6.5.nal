'********** variable unification

'If something can fly, chirp, and eats worms, then it is a bird.
<(&&,<$x --> flyer>,<$x --> [chirping]>, <(*, $x, worms) --> food>) ==> <$x --> bird>>. 

'If something can chirp and has wings, then it is a bird.
<(&&,<$x --> [chirping]>,<$x --> [with-wings]>) ==> <$x --> bird>>.

''//6
12

'If something can fly and eats worms, then I guess it has wings.
''outputMustContain('<(&&,<$1 --> flyer>,<(*,$1,worms) --> food>) ==> <$1 --> [with-wings]>>. %1.00;0.45%')

'I guess if something has wings, then it can fly and eats worms.
''outputMustContain('<<$1 --> [with-wings]> ==> (&&,<$1 --> flyer>,<(*,$1,worms) --> food>)>. %1.00;0.45%')

