!function(t) {
    var n = {};
    function i(r) {
        if (n[r])
            return n[r].exports;
        var e = n[r] = {
            i: r,
            l: !1,
            exports: {}
        };
        return t[r].call(e.exports, e, e.exports, i),
        e.l = !0,
        e.exports
    }
    i.m = t,
    i.c = n,
    i.d = function(t, n, r) {
        i.o(t, n) || Object.defineProperty(t, n, {
            configurable: !1,
            enumerable: !0,
            get: r
        })
    }
    ,
    i.n = function(t) {
        var n = t && t.__esModule ? function() {
            return t.default
        }
        : function() {
            return t
        }
        ;
        return i.d(n, "a", n),
        n
    }
    ,
    i.o = function(t, n) {
        return Object.prototype.hasOwnProperty.call(t, n)
    }
    ,
    i.p = "/react_build/",
    i(i.s = 10)
}([function(t, n, i) {
    "use strict";
    n.a = function(t) {
        return function() {
            return t
        }
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t, n, i) {
        this.node = t,
        this.x0 = n,
        this.x1 = i
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t, n, i, r, e) {
        this.node = t,
        this.x0 = n,
        this.y0 = i,
        this.x1 = r,
        this.y1 = e
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t, n, i, r, e, a, o) {
        this.node = t,
        this.x0 = n,
        this.y0 = i,
        this.z0 = r,
        this.x1 = e,
        this.y1 = a,
        this.z1 = o
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t) {
        return 1e-6 * (t() - .5)
    }
}
, function(t, n, i) {
    "use strict";
    n.b = v,
    n.a = p,
    n.c = d;
    var r, e, a = 0, o = 0, s = 0, u = 1e3, h = 0, c = 0, f = 0, l = "object" == typeof performance && performance.now ? performance : Date, _ = "object" == typeof window && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(t) {
        setTimeout(t, 17)
    }
    ;
    function v() {
        return c || (_(y),
        c = l.now() + f)
    }
    function y() {
        c = 0
    }
    function p() {
        this._call = this._time = this._next = null
    }
    function d(t, n, i) {
        var r = new p;
        return r.restart(t, n, i),
        r
    }
    function x() {
        c = (h = l.now()) + f,
        a = o = 0;
        try {
            !function() {
                v(),
                ++a;
                for (var t, n = r; n; )
                    (t = c - n._time) >= 0 && n._call.call(null, t),
                    n = n._next;
                --a
            }()
        } finally {
            a = 0,
            function() {
                var t, n, i = r, a = 1 / 0;
                for (; i; )
                    i._call ? (a > i._time && (a = i._time),
                    t = i,
                    i = i._next) : (n = i._next,
                    i._next = null,
                    i = t ? t._next = n : r = n);
                e = t,
                w(a)
            }(),
            c = 0
        }
    }
    function g() {
        var t = l.now()
          , n = t - h;
        n > u && (f -= n,
        h = t)
    }
    function w(t) {
        a || (o && (o = clearTimeout(o)),
        t - c > 24 ? (t < 1 / 0 && (o = setTimeout(x, t - l.now() - f)),
        s && (s = clearInterval(s))) : (s || (h = l.now(),
        s = setInterval(g, u)),
        a = 1,
        _(x)))
    }
    p.prototype = d.prototype = {
        constructor: p,
        restart: function(t, n, i) {
            if ("function" != typeof t)
                throw new TypeError("callback is not a function");
            i = (null == i ? v() : +i) + (null == n ? 0 : +n),
            this._next || e === this || (e ? e._next = this : r = this,
            e = this),
            this._call = t,
            this._time = i,
            w()
        },
        stop: function() {
            this._call && (this._call = null,
            this._time = 1 / 0,
            w())
        }
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(15);
    i.d(n, "a", function() {
        return r.a
    })
}
, function(t, n, i) {
    "use strict";
    var r = i(27);
    i.d(n, "a", function() {
        return r.a
    })
}
, function(t, n, i) {
    "use strict";
    var r = i(40);
    i.d(n, "a", function() {
        return r.a
    })
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        return t.x
    }
    ,
    n.c = function(t) {
        return t.y
    }
    ,
    n.d = function(t) {
        return t.z
    }
    ;
    var r = i(56)
      , e = i(58)
      , a = i(61);
    var o = 10
      , s = Math.PI * (3 - Math.sqrt(5))
      , u = 20 * Math.PI / (9 + Math.sqrt(221));
    n.a = function(t, n) {
        n = n || 2;
        var i, h = Math.min(3, Math.max(1, Math.round(n))), c = 1, f = .001, l = 1 - Math.pow(f, 1 / 300), _ = 0, v = .6, y = new Map, p = Object(e.a)(g), d = Object(r.a)("tick", "end"), x = Object(a.a)();
        function g() {
            w(),
            d.call("tick", i),
            c < f && (p.stop(),
            d.call("end", i))
        }
        function w(n) {
            var r, e, a = t.length;
            void 0 === n && (n = 1);
            for (var o = 0; o < n; ++o)
                for (c += (_ - c) * l,
                y.forEach(function(t) {
                    t(c)
                }),
                r = 0; r < a; ++r)
                    null == (e = t[r]).fx ? e.x += e.vx *= v : (e.x = e.fx,
                    e.vx = 0),
                    h > 1 && (null == e.fy ? e.y += e.vy *= v : (e.y = e.fy,
                    e.vy = 0)),
                    h > 2 && (null == e.fz ? e.z += e.vz *= v : (e.z = e.fz,
                    e.vz = 0));
            return i
        }
        function b() {
            for (var n, i = 0, r = t.length; i < r; ++i) {
                if ((n = t[i]).index = i,
                null != n.fx && (n.x = n.fx),
                null != n.fy && (n.y = n.fy),
                null != n.fz && (n.z = n.fz),
                isNaN(n.x) || h > 1 && isNaN(n.y) || h > 2 && isNaN(n.z)) {
                    var e = o * (h > 2 ? Math.cbrt(.5 + i) : h > 1 ? Math.sqrt(.5 + i) : i)
                      , a = i * s
                      , c = i * u;
                    1 === h ? n.x = e : 2 === h ? (n.x = e * Math.cos(a),
                    n.y = e * Math.sin(a)) : (n.x = e * Math.sin(a) * Math.cos(c),
                    n.y = e * Math.cos(a),
                    n.z = e * Math.sin(a) * Math.sin(c))
                }
                (isNaN(n.vx) || h > 1 && isNaN(n.vy) || h > 2 && isNaN(n.vz)) && (n.vx = 0,
                h > 1 && (n.vy = 0),
                h > 2 && (n.vz = 0))
            }
        }
        function N(n) {
            return n.initialize && n.initialize(t, x, h),
            n
        }
        return null == t && (t = []),
        b(),
        i = {
            tick: w,
            restart: function() {
                return p.restart(g),
                i
            },
            stop: function() {
                return p.stop(),
                i
            },
            numDimensions: function(t) {
                return arguments.length ? (h = Math.min(3, Math.max(1, Math.round(t))),
                y.forEach(N),
                i) : h
            },
            nodes: function(n) {
                return arguments.length ? (t = n,
                b(),
                y.forEach(N),
                i) : t
            },
            alpha: function(t) {
                return arguments.length ? (c = +t,
                i) : c
            },
            alphaMin: function(t) {
                return arguments.length ? (f = +t,
                i) : f
            },
            alphaDecay: function(t) {
                return arguments.length ? (l = +t,
                i) : +l
            },
            alphaTarget: function(t) {
                return arguments.length ? (_ = +t,
                i) : _
            },
            velocityDecay: function(t) {
                return arguments.length ? (v = 1 - t,
                i) : 1 - v
            },
            randomSource: function(t) {
                return arguments.length ? (x = t,
                y.forEach(N),
                i) : x
            },
            force: function(t, n) {
                return arguments.length > 1 ? (null == n ? y.delete(t) : y.set(t, N(n)),
                i) : y.get(t)
            },
            find: function() {
                var n, i, r, e, a, o, s = Array.prototype.slice.call(arguments), u = s.shift() || 0, c = (h > 1 ? s.shift() : null) || 0, f = (h > 2 ? s.shift() : null) || 0, l = s.shift() || 1 / 0, _ = 0, v = t.length;
                for (l *= l,
                _ = 0; _ < v; ++_)
                    (e = (n = u - (a = t[_]).x) * n + (i = c - (a.y || 0)) * i + (r = f - (a.z || 0)) * r) < l && (o = a,
                    l = e);
                return o
            },
            on: function(t, n) {
                return arguments.length > 1 ? (d.on(t, n),
                i) : d.on(t)
            }
        }
    }
}
, function(t, n, i) {
    "use strict";
    Object.defineProperty(n, "__esModule", {
        value: !0
    });
    const r = i(11)
      , e = i(67)
      , a = self;
    let o = new r.default;
    self._force3dLayout = o,
    o.doningLayout = function() {
        if (!o.graph)
            return;
        let t = o.graph.nodes
          , n = {
            command: e.UPDATE_POSITION,
            positions: u
        };
        t.forEach((t,n)=>{
            u[3 * n] = t.position.x,
            u[3 * n + 1] = t.position.y,
            u[3 * n + 2] = t.position.z
        }
        ),
        a.postMessage(n)
    }
    ,
    o.finishLayout = function() {
        let t = {
            command: e.FINISH_LAYOUT
        };
        a.postMessage(t)
    }
    ;
    let s = "undefined" != typeof SharedArrayBuffer ? new SharedArrayBuffer(0) : void 0
      , u = new Float32Array(0);
    a.onmessage = (t=>{
        let {data: n} = t;
        switch (n.command) {
        case e.INIT_GRAPH:
            let t = n.graph;
            n.config && o.config(n.config),
            function(t) {
                let n = t.nodes.length;
                const i = Float32Array.BYTES_PER_ELEMENT * n * 3;
                s && s.byteLength != i ? (s = new SharedArrayBuffer(i),
                u = new Float32Array(s)) : u = new Float32Array(i)
            }(t),
            t.nodes.filter(t=>t.pinned).forEach(t=>{
                t.position.fx = t.position.x,
                t.position.fy = t.position.y,
                t.position.fz = t.position.z
            }
            ),
            o.setGraph(t).startLayout();
            break;
        case e.PIN_NODES:
            let i = n.nodes;
            o.pinNodes(i).startLayout();
            break;
        case e.ENABLE_LAYOUT:
            let r = n.enable;
            o.enable = r;
        case e.SETTING_LAYOUT_OPTIONS:
            let a = n.name
              , h = n.value;
            o.updateOptions(a, h).startLayout();
            break;
        case e.RESTART_FORCE_LAYOUT:
            o.restartLayout();
            break;
        case e.STOP_FORCE_LAYOUT:
            o.stopLayout()
        }
    }
    ),
    n.default = null
}
, function(t, n, i) {
    "use strict";
    Object.defineProperty(n, "__esModule", {
        value: !0
    });
    const r = i(12)
      , e = i(66)
      , a = {
        charge: -.03,
        linkStrength: 1.5,
        collision: .02,
        linkDistance: .5,
        alpha: .6,
        gravity: .1,
        heightCompress: 1,
        alphaDecay: .1,
        velocityDecay: .3,
        dimentions: 3,
        gravityX: 1,
        gravityY: 1,
        gravityZ: 1
    };
    n.default = class {
        constructor(t) {
            this.graph = t || {
                nodes: [],
                edges: []
            },
            this.params = Object.assign({}, a),
            this._enable = !0,
            this.lastCompress = this.params.heightCompress,
            this.simulation = r.forceSimulation([], 3).alpha(a.alpha).alphaDecay(a.alphaDecay).velocityDecay(a.velocityDecay).force("collision", r.forceCollide().radius(a.collision)).force("link", r.forceLink()).force("charge", r.forceManyBody()).force("x", r.forceX().strength(this.params.gravity * this.params.gravityX)).force("y", r.forceY().strength(this.params.gravity * this.params.gravityY)).force("z", e.default().strength(this.params.gravity * this.params.gravityZ)).on("tick", this.ticked.bind(this)).on("end", this.done.bind(this)).stop()
        }
        config(t) {
            return Object.assign(this.params, t),
            this
        }
        ticked() {
            this.doningLayout && this.doningLayout()
        }
        done() {
            this.endTime = Date.now(),
            this.endTime,
            this.startTime,
            this.finishLayout && this.finishLayout()
        }
        createLinksByGraph(t) {
            let n = {};
            return t.nodes.forEach((t,i)=>{
                n[t.id] = i
            }
            ),
            t.edges.map(t=>({
                id: t.id,
                source: n[t.sourceId],
                target: n[t.targetId]
            }))
        }
        randomZ() {
            let t = this.graph.nodes;
            t.every(t=>t.position.z < .001) && t.forEach(t=>{
                t.position.z = Math.random()
            }
            )
        }
        setGraph(t) {
            this.graph = t,
            this.simulation.nodes(this.graph.nodes.map(t=>t.position));
            let n = this.createLinksByGraph(this.graph);
            return this.simulation.force("link").links(n),
            this
        }
        changeDimentions(t) {
            2 == t && this.graph.nodes.forEach(t=>{
                t.position.z = 0
            }
            ),
            this.simulation.numDimensions(t)
        }
        startLayout() {
            if (10 == this.params.heightCompress ? this.changeDimentions(2) : this.changeDimentions(3),
            !this.enable)
                return;
            this.startTime = Date.now(),
            this.simulation.force("link").distance(this.params.linkDistance).strength(this.params.linkStrength);
            let t = this.params.heightCompress
              , n = this.params.gravity * Math.sqrt(t)
              , i = this.params.gravity / Math.sqrt(t);
            n = n > 10 ? 10 : n,
            this.simulation.alpha(a.alpha),
            this.simulation.force("x").strength(i * this.params.gravityX),
            this.simulation.force("y").strength(i * this.params.gravityY),
            this.simulation.force("z").strength(n * this.params.gravityZ),
            this.simulation.force("charge").strength(this.params.charge),
            this.simulation.force("collision").radius(this.params.collision),
            this.simulation.restart(),
            this.lastCompress = this.params.heightCompress
        }
        set enable(t) {
            this._enable = t,
            this._enable ? this.simulation.restart() : this.simulation.stop()
        }
        get enable() {
            return this._enable
        }
        updateOptions(t, n) {
            return this.params[t] = n,
            this
        }
        pinNodes(t) {
            return t.forEach(t=>{
                let n = this.graph.nodes.find(n=>n.id == t.id);
                n && (n.pinned = t.pinned),
                t.pinned && n && (n.position.fx = t.position.x,
                n.position.fy = t.position.y,
                n.position.fz = t.position.z)
            }
            ),
            this.graph.nodes.forEach(t=>{
                t.pinned || (delete t.position.fx,
                delete t.position.fy,
                delete t.position.fz)
            }
            ),
            this
        }
        restartLayout() {
            10 == this.params.heightCompress ? this.changeDimentions(2) : this.changeDimentions(3),
            this.enable && (this.simulation.alpha(a.alpha),
            this.simulation.restart())
        }
        stopLayout() {
            this.simulation.stop()
        }
    }
}
, function(t, n, i) {
    "use strict";
    Object.defineProperty(n, "__esModule", {
        value: !0
    });
    var r = i(13);
    i.d(n, "forceCenter", function() {
        return r.a
    });
    var e = i(14);
    i.d(n, "forceCollide", function() {
        return e.a
    });
    var a = i(54);
    i.d(n, "forceLink", function() {
        return a.a
    });
    var o = i(55);
    i.d(n, "forceManyBody", function() {
        return o.a
    });
    var s = i(62);
    i.d(n, "forceRadial", function() {
        return s.a
    });
    var u = i(9);
    i.d(n, "forceSimulation", function() {
        return u.a
    });
    var h = i(63);
    i.d(n, "forceX", function() {
        return h.a
    });
    var c = i(64);
    i.d(n, "forceY", function() {
        return c.a
    });
    var f = i(65);
    i.d(n, "forceZ", function() {
        return f.a
    })
}
, function(t, n, i) {
    "use strict";
    n.a = function(t, n, i) {
        var r, e = 1;
        function a() {
            var a, o, s = r.length, u = 0, h = 0, c = 0;
            for (a = 0; a < s; ++a)
                u += (o = r[a]).x || 0,
                h += o.y || 0,
                c += o.z || 0;
            for (u = (u / s - t) * e,
            h = (h / s - n) * e,
            c = (c / s - i) * e,
            a = 0; a < s; ++a)
                o = r[a],
                u && (o.x -= u),
                h && (o.y -= h),
                c && (o.z -= c)
        }
        return null == t && (t = 0),
        null == n && (n = 0),
        null == i && (i = 0),
        a.initialize = function(t) {
            r = t
        }
        ,
        a.x = function(n) {
            return arguments.length ? (t = +n,
            a) : t
        }
        ,
        a.y = function(t) {
            return arguments.length ? (n = +t,
            a) : n
        }
        ,
        a.z = function(t) {
            return arguments.length ? (i = +t,
            a) : i
        }
        ,
        a.strength = function(t) {
            return arguments.length ? (e = +t,
            a) : e
        }
        ,
        a
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(6)
      , e = i(7)
      , a = i(8)
      , o = i(0)
      , s = i(4);
    function u(t) {
        return t.x + t.vx
    }
    function h(t) {
        return t.y + t.vy
    }
    function c(t) {
        return t.z + t.vz
    }
    n.a = function(t) {
        var n, i, f, l, _ = 1, v = 1;
        function y() {
            for (var t, o, y, d, x, g, w, b, N = n.length, m = 0; m < v; ++m)
                for (o = (1 === i ? Object(r.a)(n, u) : 2 === i ? Object(e.a)(n, u, h) : 3 === i ? Object(a.a)(n, u, h, c) : null).visitAfter(p),
                t = 0; t < N; ++t)
                    y = n[t],
                    w = f[y.index],
                    b = w * w,
                    d = y.x + y.vx,
                    i > 1 && (x = y.y + y.vy),
                    i > 2 && (g = y.z + y.vz),
                    o.visit(z);
            function z(t, n, r, e, a, o, u) {
                var h = [n, r, e, a, o, u]
                  , c = h[0]
                  , f = h[1]
                  , v = h[2]
                  , p = h[i]
                  , N = h[i + 1]
                  , m = h[i + 2]
                  , z = t.data
                  , O = t.r
                  , A = w + O;
                if (!z)
                    return c > d + A || p < d - A || i > 1 && (f > x + A || N < x - A) || i > 2 && (v > g + A || m < g - A);
                if (z.index > y.index) {
                    var M = d - z.x - z.vx
                      , j = i > 1 ? x - z.y - z.vy : 0
                      , k = i > 2 ? g - z.z - z.vz : 0
                      , E = M * M + j * j + k * k;
                    E < A * A && (0 === M && (E += (M = Object(s.a)(l)) * M),
                    i > 1 && 0 === j && (E += (j = Object(s.a)(l)) * j),
                    i > 2 && 0 === k && (E += (k = Object(s.a)(l)) * k),
                    E = (A - (E = Math.sqrt(E))) / E * _,
                    y.vx += (M *= E) * (A = (O *= O) / (b + O)),
                    i > 1 && (y.vy += (j *= E) * A),
                    i > 2 && (y.vz += (k *= E) * A),
                    z.vx -= M * (A = 1 - A),
                    i > 1 && (z.vy -= j * A),
                    i > 2 && (z.vz -= k * A))
                }
            }
        }
        function p(t) {
            if (t.data)
                return t.r = f[t.data.index];
            for (var n = t.r = 0; n < Math.pow(2, i); ++n)
                t[n] && t[n].r > t.r && (t.r = t[n].r)
        }
        function d() {
            if (n) {
                var i, r, e = n.length;
                for (f = new Array(e),
                i = 0; i < e; ++i)
                    r = n[i],
                    f[r.index] = +t(r, i, n)
            }
        }
        return "function" != typeof t && (t = Object(o.a)(null == t ? 1 : +t)),
        y.initialize = function(t, ...r) {
            n = t,
            l = r.find(t=>"function" == typeof t) || Math.random,
            i = r.find(t=>[1, 2, 3].includes(t)) || 2,
            d()
        }
        ,
        y.iterations = function(t) {
            return arguments.length ? (v = +t,
            y) : v
        }
        ,
        y.strength = function(t) {
            return arguments.length ? (_ = +t,
            y) : _
        }
        ,
        y.radius = function(n) {
            return arguments.length ? (t = "function" == typeof n ? n : Object(o.a)(+n),
            d(),
            y) : t
        }
        ,
        y
    }
}
, function(t, n, i) {
    "use strict";
    n.a = v;
    var r = i(16)
      , e = i(17)
      , a = i(18)
      , o = i(19)
      , s = i(20)
      , u = i(21)
      , h = i(22)
      , c = i(23)
      , f = i(24)
      , l = i(25)
      , _ = i(26);
    function v(t, n) {
        var i = new y(null == n ? _.b : n,NaN,NaN);
        return null == t ? i : i.addAll(t)
    }
    function y(t, n, i) {
        this._x = t,
        this._x0 = n,
        this._x1 = i,
        this._root = void 0
    }
    function p(t) {
        for (var n = {
            data: t.data
        }, i = n; t = t.next; )
            i = i.next = {
                data: t.data
            };
        return n
    }
    var d = v.prototype = y.prototype;
    d.copy = function() {
        var t, n, i = new y(this._x,this._x0,this._x1), r = this._root;
        if (!r)
            return i;
        if (!r.length)
            return i._root = p(r),
            i;
        for (t = [{
            source: r,
            target: i._root = new Array(2)
        }]; r = t.pop(); )
            for (var e = 0; e < 2; ++e)
                (n = r.source[e]) && (n.length ? t.push({
                    source: n,
                    target: r.target[e] = new Array(2)
                }) : r.target[e] = p(n));
        return i
    }
    ,
    d.add = r.b,
    d.addAll = r.a,
    d.cover = e.a,
    d.data = a.a,
    d.extent = o.a,
    d.find = s.a,
    d.remove = u.a,
    d.removeAll = u.b,
    d.root = h.a,
    d.size = c.a,
    d.visit = f.a,
    d.visitAfter = l.a,
    d.x = _.a
}
, function(t, n, i) {
    "use strict";
    function r(t, n, i) {
        if (isNaN(n))
            return t;
        var r, e, a, o, s, u, h = t._root, c = {
            data: i
        }, f = t._x0, l = t._x1;
        if (!h)
            return t._root = c,
            t;
        for (; h.length; )
            if ((o = n >= (e = (f + l) / 2)) ? f = e : l = e,
            r = h,
            !(h = h[s = +o]))
                return r[s] = c,
                t;
        if (n === (a = +t._x.call(null, h.data)))
            return c.next = h,
            r ? r[s] = c : t._root = c,
            t;
        do {
            r = r ? r[s] = new Array(2) : t._root = new Array(2),
            (o = n >= (e = (f + l) / 2)) ? f = e : l = e
        } while ((s = +o) == (u = +(a >= e)));
        return r[u] = h,
        r[s] = c,
        t
    }
    n.a = function(t) {
        var n, i, e = t.length, a = new Array(e), o = 1 / 0, s = -1 / 0;
        for (n = 0; n < e; ++n)
            isNaN(i = +this._x.call(null, t[n])) || (a[n] = i,
            i < o && (o = i),
            i > s && (s = i));
        if (o > s)
            return this;
        for (this.cover(o).cover(s),
        n = 0; n < e; ++n)
            r(this, a[n], t[n]);
        return this
    }
    ,
    n.b = function(t) {
        var n = +this._x.call(null, t);
        return r(this.cover(n), n, t)
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t) {
        if (isNaN(t = +t))
            return this;
        var n = this._x0
          , i = this._x1;
        if (isNaN(n))
            i = (n = Math.floor(t)) + 1;
        else {
            for (var r, e, a = i - n || 1, o = this._root; n > t || t >= i; )
                switch (e = +(t < n),
                (r = new Array(2))[e] = o,
                o = r,
                a *= 2,
                e) {
                case 0:
                    i = n + a;
                    break;
                case 1:
                    n = i - a
                }
            this._root && this._root.length && (this._root = o)
        }
        return this._x0 = n,
        this._x1 = i,
        this
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        var t = [];
        return this.visit(function(n) {
            if (!n.length)
                do {
                    t.push(n.data)
                } while (n = n.next)
        }),
        t
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t) {
        return arguments.length ? this.cover(+t[0][0]).cover(+t[1][0]) : isNaN(this._x0) ? void 0 : [[this._x0], [this._x1]]
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(1);
    n.a = function(t, n) {
        var i, e, a, o, s, u = this._x0, h = this._x1, c = [], f = this._root;
        for (f && c.push(new r.a(f,u,h)),
        null == n ? n = 1 / 0 : (u = t - n,
        h = t + n); o = c.pop(); )
            if (!(!(f = o.node) || (e = o.x0) > h || (a = o.x1) < u))
                if (f.length) {
                    var l = (e + a) / 2;
                    c.push(new r.a(f[1],l,a), new r.a(f[0],e,l)),
                    (s = +(t >= l)) && (o = c[c.length - 1],
                    c[c.length - 1] = c[c.length - 1 - s],
                    c[c.length - 1 - s] = o)
                } else {
                    var _ = Math.abs(t - +this._x.call(null, f.data));
                    _ < n && (n = _,
                    u = t - _,
                    h = t + _,
                    i = f.data)
                }
        return i
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        for (var n = 0, i = t.length; n < i; ++n)
            this.remove(t[n]);
        return this
    }
    ,
    n.a = function(t) {
        if (isNaN(a = +this._x.call(null, t)))
            return this;
        var n, i, r, e, a, o, s, u, h, c = this._root, f = this._x0, l = this._x1;
        if (!c)
            return this;
        if (c.length)
            for (; ; ) {
                if ((s = a >= (o = (f + l) / 2)) ? f = o : l = o,
                n = c,
                !(c = c[u = +s]))
                    return this;
                if (!c.length)
                    break;
                n[u + 1 & 1] && (i = n,
                h = u)
            }
        for (; c.data !== t; )
            if (r = c,
            !(c = c.next))
                return this;
        return (e = c.next) && delete c.next,
        r ? (e ? r.next = e : delete r.next,
        this) : n ? (e ? n[u] = e : delete n[u],
        (c = n[0] || n[1]) && c === (n[1] || n[0]) && !c.length && (i ? i[h] = c : this._root = c),
        this) : (this._root = e,
        this)
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        return this._root
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        var t = 0;
        return this.visit(function(n) {
            if (!n.length)
                do {
                    ++t
                } while (n = n.next)
        }),
        t
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(1);
    n.a = function(t) {
        var n, i, e, a, o = [], s = this._root;
        for (s && o.push(new r.a(s,this._x0,this._x1)); n = o.pop(); )
            if (!t(s = n.node, e = n.x0, a = n.x1) && s.length) {
                var u = (e + a) / 2;
                (i = s[1]) && o.push(new r.a(i,u,a)),
                (i = s[0]) && o.push(new r.a(i,e,u))
            }
        return this
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(1);
    n.a = function(t) {
        var n, i = [], e = [];
        for (this._root && i.push(new r.a(this._root,this._x0,this._x1)); n = i.pop(); ) {
            var a = n.node;
            if (a.length) {
                var o, s = n.x0, u = n.x1, h = (s + u) / 2;
                (o = a[0]) && i.push(new r.a(o,s,h)),
                (o = a[1]) && i.push(new r.a(o,h,u))
            }
            e.push(n)
        }
        for (; n = e.pop(); )
            t(n.node, n.x0, n.x1);
        return this
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        return t[0]
    }
    ,
    n.a = function(t) {
        return arguments.length ? (this._x = t,
        this) : this._x
    }
}
, function(t, n, i) {
    "use strict";
    n.a = y;
    var r = i(28)
      , e = i(29)
      , a = i(30)
      , o = i(31)
      , s = i(32)
      , u = i(33)
      , h = i(34)
      , c = i(35)
      , f = i(36)
      , l = i(37)
      , _ = i(38)
      , v = i(39);
    function y(t, n, i) {
        var r = new p(null == n ? _.b : n,null == i ? v.b : i,NaN,NaN,NaN,NaN);
        return null == t ? r : r.addAll(t)
    }
    function p(t, n, i, r, e, a) {
        this._x = t,
        this._y = n,
        this._x0 = i,
        this._y0 = r,
        this._x1 = e,
        this._y1 = a,
        this._root = void 0
    }
    function d(t) {
        for (var n = {
            data: t.data
        }, i = n; t = t.next; )
            i = i.next = {
                data: t.data
            };
        return n
    }
    var x = y.prototype = p.prototype;
    x.copy = function() {
        var t, n, i = new p(this._x,this._y,this._x0,this._y0,this._x1,this._y1), r = this._root;
        if (!r)
            return i;
        if (!r.length)
            return i._root = d(r),
            i;
        for (t = [{
            source: r,
            target: i._root = new Array(4)
        }]; r = t.pop(); )
            for (var e = 0; e < 4; ++e)
                (n = r.source[e]) && (n.length ? t.push({
                    source: n,
                    target: r.target[e] = new Array(4)
                }) : r.target[e] = d(n));
        return i
    }
    ,
    x.add = r.b,
    x.addAll = r.a,
    x.cover = e.a,
    x.data = a.a,
    x.extent = o.a,
    x.find = s.a,
    x.remove = u.a,
    x.removeAll = u.b,
    x.root = h.a,
    x.size = c.a,
    x.visit = f.a,
    x.visitAfter = l.a,
    x.x = _.a,
    x.y = v.a
}
, function(t, n, i) {
    "use strict";
    function r(t, n, i, r) {
        if (isNaN(n) || isNaN(i))
            return t;
        var e, a, o, s, u, h, c, f, l, _ = t._root, v = {
            data: r
        }, y = t._x0, p = t._y0, d = t._x1, x = t._y1;
        if (!_)
            return t._root = v,
            t;
        for (; _.length; )
            if ((h = n >= (a = (y + d) / 2)) ? y = a : d = a,
            (c = i >= (o = (p + x) / 2)) ? p = o : x = o,
            e = _,
            !(_ = _[f = c << 1 | h]))
                return e[f] = v,
                t;
        if (s = +t._x.call(null, _.data),
        u = +t._y.call(null, _.data),
        n === s && i === u)
            return v.next = _,
            e ? e[f] = v : t._root = v,
            t;
        do {
            e = e ? e[f] = new Array(4) : t._root = new Array(4),
            (h = n >= (a = (y + d) / 2)) ? y = a : d = a,
            (c = i >= (o = (p + x) / 2)) ? p = o : x = o
        } while ((f = c << 1 | h) == (l = (u >= o) << 1 | s >= a));
        return e[l] = _,
        e[f] = v,
        t
    }
    n.a = function(t) {
        var n, i, e, a, o = t.length, s = new Array(o), u = new Array(o), h = 1 / 0, c = 1 / 0, f = -1 / 0, l = -1 / 0;
        for (i = 0; i < o; ++i)
            isNaN(e = +this._x.call(null, n = t[i])) || isNaN(a = +this._y.call(null, n)) || (s[i] = e,
            u[i] = a,
            e < h && (h = e),
            e > f && (f = e),
            a < c && (c = a),
            a > l && (l = a));
        if (h > f || c > l)
            return this;
        for (this.cover(h, c).cover(f, l),
        i = 0; i < o; ++i)
            r(this, s[i], u[i], t[i]);
        return this
    }
    ,
    n.b = function(t) {
        const n = +this._x.call(null, t)
          , i = +this._y.call(null, t);
        return r(this.cover(n, i), n, i, t)
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t, n) {
        if (isNaN(t = +t) || isNaN(n = +n))
            return this;
        var i = this._x0
          , r = this._y0
          , e = this._x1
          , a = this._y1;
        if (isNaN(i))
            e = (i = Math.floor(t)) + 1,
            a = (r = Math.floor(n)) + 1;
        else {
            for (var o, s, u = e - i || 1, h = this._root; i > t || t >= e || r > n || n >= a; )
                switch (s = (n < r) << 1 | t < i,
                (o = new Array(4))[s] = h,
                h = o,
                u *= 2,
                s) {
                case 0:
                    e = i + u,
                    a = r + u;
                    break;
                case 1:
                    i = e - u,
                    a = r + u;
                    break;
                case 2:
                    e = i + u,
                    r = a - u;
                    break;
                case 3:
                    i = e - u,
                    r = a - u
                }
            this._root && this._root.length && (this._root = h)
        }
        return this._x0 = i,
        this._y0 = r,
        this._x1 = e,
        this._y1 = a,
        this
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        var t = [];
        return this.visit(function(n) {
            if (!n.length)
                do {
                    t.push(n.data)
                } while (n = n.next)
        }),
        t
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t) {
        return arguments.length ? this.cover(+t[0][0], +t[0][1]).cover(+t[1][0], +t[1][1]) : isNaN(this._x0) ? void 0 : [[this._x0, this._y0], [this._x1, this._y1]]
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(2);
    n.a = function(t, n, i) {
        var e, a, o, s, u, h, c, f = this._x0, l = this._y0, _ = this._x1, v = this._y1, y = [], p = this._root;
        for (p && y.push(new r.a(p,f,l,_,v)),
        null == i ? i = 1 / 0 : (f = t - i,
        l = n - i,
        _ = t + i,
        v = n + i,
        i *= i); h = y.pop(); )
            if (!(!(p = h.node) || (a = h.x0) > _ || (o = h.y0) > v || (s = h.x1) < f || (u = h.y1) < l))
                if (p.length) {
                    var d = (a + s) / 2
                      , x = (o + u) / 2;
                    y.push(new r.a(p[3],d,x,s,u), new r.a(p[2],a,x,d,u), new r.a(p[1],d,o,s,x), new r.a(p[0],a,o,d,x)),
                    (c = (n >= x) << 1 | t >= d) && (h = y[y.length - 1],
                    y[y.length - 1] = y[y.length - 1 - c],
                    y[y.length - 1 - c] = h)
                } else {
                    var g = t - +this._x.call(null, p.data)
                      , w = n - +this._y.call(null, p.data)
                      , b = g * g + w * w;
                    if (b < i) {
                        var N = Math.sqrt(i = b);
                        f = t - N,
                        l = n - N,
                        _ = t + N,
                        v = n + N,
                        e = p.data
                    }
                }
        return e
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        for (var n = 0, i = t.length; n < i; ++n)
            this.remove(t[n]);
        return this
    }
    ,
    n.a = function(t) {
        if (isNaN(a = +this._x.call(null, t)) || isNaN(o = +this._y.call(null, t)))
            return this;
        var n, i, r, e, a, o, s, u, h, c, f, l, _ = this._root, v = this._x0, y = this._y0, p = this._x1, d = this._y1;
        if (!_)
            return this;
        if (_.length)
            for (; ; ) {
                if ((h = a >= (s = (v + p) / 2)) ? v = s : p = s,
                (c = o >= (u = (y + d) / 2)) ? y = u : d = u,
                n = _,
                !(_ = _[f = c << 1 | h]))
                    return this;
                if (!_.length)
                    break;
                (n[f + 1 & 3] || n[f + 2 & 3] || n[f + 3 & 3]) && (i = n,
                l = f)
            }
        for (; _.data !== t; )
            if (r = _,
            !(_ = _.next))
                return this;
        return (e = _.next) && delete _.next,
        r ? (e ? r.next = e : delete r.next,
        this) : n ? (e ? n[f] = e : delete n[f],
        (_ = n[0] || n[1] || n[2] || n[3]) && _ === (n[3] || n[2] || n[1] || n[0]) && !_.length && (i ? i[l] = _ : this._root = _),
        this) : (this._root = e,
        this)
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        return this._root
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        var t = 0;
        return this.visit(function(n) {
            if (!n.length)
                do {
                    ++t
                } while (n = n.next)
        }),
        t
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(2);
    n.a = function(t) {
        var n, i, e, a, o, s, u = [], h = this._root;
        for (h && u.push(new r.a(h,this._x0,this._y0,this._x1,this._y1)); n = u.pop(); )
            if (!t(h = n.node, e = n.x0, a = n.y0, o = n.x1, s = n.y1) && h.length) {
                var c = (e + o) / 2
                  , f = (a + s) / 2;
                (i = h[3]) && u.push(new r.a(i,c,f,o,s)),
                (i = h[2]) && u.push(new r.a(i,e,f,c,s)),
                (i = h[1]) && u.push(new r.a(i,c,a,o,f)),
                (i = h[0]) && u.push(new r.a(i,e,a,c,f))
            }
        return this
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(2);
    n.a = function(t) {
        var n, i = [], e = [];
        for (this._root && i.push(new r.a(this._root,this._x0,this._y0,this._x1,this._y1)); n = i.pop(); ) {
            var a = n.node;
            if (a.length) {
                var o, s = n.x0, u = n.y0, h = n.x1, c = n.y1, f = (s + h) / 2, l = (u + c) / 2;
                (o = a[0]) && i.push(new r.a(o,s,u,f,l)),
                (o = a[1]) && i.push(new r.a(o,f,u,h,l)),
                (o = a[2]) && i.push(new r.a(o,s,l,f,c)),
                (o = a[3]) && i.push(new r.a(o,f,l,h,c))
            }
            e.push(n)
        }
        for (; n = e.pop(); )
            t(n.node, n.x0, n.y0, n.x1, n.y1);
        return this
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        return t[0]
    }
    ,
    n.a = function(t) {
        return arguments.length ? (this._x = t,
        this) : this._x
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        return t[1]
    }
    ,
    n.a = function(t) {
        return arguments.length ? (this._y = t,
        this) : this._y
    }
}
, function(t, n, i) {
    "use strict";
    n.a = p;
    var r = i(41)
      , e = i(42)
      , a = i(43)
      , o = i(44)
      , s = i(45)
      , u = i(46)
      , h = i(47)
      , c = i(48)
      , f = i(49)
      , l = i(50)
      , _ = i(51)
      , v = i(52)
      , y = i(53);
    function p(t, n, i, r) {
        var e = new d(null == n ? _.b : n,null == i ? v.b : i,null == r ? y.b : r,NaN,NaN,NaN,NaN,NaN,NaN);
        return null == t ? e : e.addAll(t)
    }
    function d(t, n, i, r, e, a, o, s, u) {
        this._x = t,
        this._y = n,
        this._z = i,
        this._x0 = r,
        this._y0 = e,
        this._z0 = a,
        this._x1 = o,
        this._y1 = s,
        this._z1 = u,
        this._root = void 0
    }
    function x(t) {
        for (var n = {
            data: t.data
        }, i = n; t = t.next; )
            i = i.next = {
                data: t.data
            };
        return n
    }
    var g = p.prototype = d.prototype;
    g.copy = function() {
        var t, n, i = new d(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1), r = this._root;
        if (!r)
            return i;
        if (!r.length)
            return i._root = x(r),
            i;
        for (t = [{
            source: r,
            target: i._root = new Array(8)
        }]; r = t.pop(); )
            for (var e = 0; e < 8; ++e)
                (n = r.source[e]) && (n.length ? t.push({
                    source: n,
                    target: r.target[e] = new Array(8)
                }) : r.target[e] = x(n));
        return i
    }
    ,
    g.add = r.b,
    g.addAll = r.a,
    g.cover = e.a,
    g.data = a.a,
    g.extent = o.a,
    g.find = s.a,
    g.remove = u.a,
    g.removeAll = u.b,
    g.root = h.a,
    g.size = c.a,
    g.visit = f.a,
    g.visitAfter = l.a,
    g.x = _.a,
    g.y = v.a,
    g.z = y.a
}
, function(t, n, i) {
    "use strict";
    function r(t, n, i, r, e) {
        if (isNaN(n) || isNaN(i) || isNaN(r))
            return t;
        var a, o, s, u, h, c, f, l, _, v, y, p, d = t._root, x = {
            data: e
        }, g = t._x0, w = t._y0, b = t._z0, N = t._x1, m = t._y1, z = t._z1;
        if (!d)
            return t._root = x,
            t;
        for (; d.length; )
            if ((l = n >= (o = (g + N) / 2)) ? g = o : N = o,
            (_ = i >= (s = (w + m) / 2)) ? w = s : m = s,
            (v = r >= (u = (b + z) / 2)) ? b = u : z = u,
            a = d,
            !(d = d[y = v << 2 | _ << 1 | l]))
                return a[y] = x,
                t;
        if (h = +t._x.call(null, d.data),
        c = +t._y.call(null, d.data),
        f = +t._z.call(null, d.data),
        n === h && i === c && r === f)
            return x.next = d,
            a ? a[y] = x : t._root = x,
            t;
        do {
            a = a ? a[y] = new Array(8) : t._root = new Array(8),
            (l = n >= (o = (g + N) / 2)) ? g = o : N = o,
            (_ = i >= (s = (w + m) / 2)) ? w = s : m = s,
            (v = r >= (u = (b + z) / 2)) ? b = u : z = u
        } while ((y = v << 2 | _ << 1 | l) == (p = (f >= u) << 2 | (c >= s) << 1 | h >= o));
        return a[p] = d,
        a[y] = x,
        t
    }
    n.a = function(t) {
        var n, i, e, a, o, s = t.length, u = new Array(s), h = new Array(s), c = new Array(s), f = 1 / 0, l = 1 / 0, _ = 1 / 0, v = -1 / 0, y = -1 / 0, p = -1 / 0;
        for (i = 0; i < s; ++i)
            isNaN(e = +this._x.call(null, n = t[i])) || isNaN(a = +this._y.call(null, n)) || isNaN(o = +this._z.call(null, n)) || (u[i] = e,
            h[i] = a,
            c[i] = o,
            e < f && (f = e),
            e > v && (v = e),
            a < l && (l = a),
            a > y && (y = a),
            o < _ && (_ = o),
            o > p && (p = o));
        if (f > v || l > y || _ > p)
            return this;
        for (this.cover(f, l, _).cover(v, y, p),
        i = 0; i < s; ++i)
            r(this, u[i], h[i], c[i], t[i]);
        return this
    }
    ,
    n.b = function(t) {
        var n = +this._x.call(null, t)
          , i = +this._y.call(null, t)
          , e = +this._z.call(null, t);
        return r(this.cover(n, i, e), n, i, e, t)
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t, n, i) {
        if (isNaN(t = +t) || isNaN(n = +n) || isNaN(i = +i))
            return this;
        var r = this._x0
          , e = this._y0
          , a = this._z0
          , o = this._x1
          , s = this._y1
          , u = this._z1;
        if (isNaN(r))
            o = (r = Math.floor(t)) + 1,
            s = (e = Math.floor(n)) + 1,
            u = (a = Math.floor(i)) + 1;
        else {
            for (var h, c, f = o - r || 1, l = this._root; r > t || t >= o || e > n || n >= s || a > i || i >= u; )
                switch (c = (i < a) << 2 | (n < e) << 1 | t < r,
                (h = new Array(8))[c] = l,
                l = h,
                f *= 2,
                c) {
                case 0:
                    o = r + f,
                    s = e + f,
                    u = a + f;
                    break;
                case 1:
                    r = o - f,
                    s = e + f,
                    u = a + f;
                    break;
                case 2:
                    o = r + f,
                    e = s - f,
                    u = a + f;
                    break;
                case 3:
                    r = o - f,
                    e = s - f,
                    u = a + f;
                    break;
                case 4:
                    o = r + f,
                    s = e + f,
                    a = u - f;
                    break;
                case 5:
                    r = o - f,
                    s = e + f,
                    a = u - f;
                    break;
                case 6:
                    o = r + f,
                    e = s - f,
                    a = u - f;
                    break;
                case 7:
                    r = o - f,
                    e = s - f,
                    a = u - f
                }
            this._root && this._root.length && (this._root = l)
        }
        return this._x0 = r,
        this._y0 = e,
        this._z0 = a,
        this._x1 = o,
        this._y1 = s,
        this._z1 = u,
        this
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        var t = [];
        return this.visit(function(n) {
            if (!n.length)
                do {
                    t.push(n.data)
                } while (n = n.next)
        }),
        t
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function(t) {
        return arguments.length ? this.cover(+t[0][0], +t[0][1], +t[0][2]).cover(+t[1][0], +t[1][1], +t[1][2]) : isNaN(this._x0) ? void 0 : [[this._x0, this._y0, this._z0], [this._x1, this._y1, this._z1]]
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(3);
    n.a = function(t, n, i, e) {
        var a, o, s, u, h, c, f, l, _, v = this._x0, y = this._y0, p = this._z0, d = this._x1, x = this._y1, g = this._z1, w = [], b = this._root;
        for (b && w.push(new r.a(b,v,y,p,d,x,g)),
        null == e ? e = 1 / 0 : (v = t - e,
        y = n - e,
        p = i - e,
        d = t + e,
        x = n + e,
        g = i + e,
        e *= e); l = w.pop(); )
            if (!(!(b = l.node) || (o = l.x0) > d || (s = l.y0) > x || (u = l.z0) > g || (h = l.x1) < v || (c = l.y1) < y || (f = l.z1) < p))
                if (b.length) {
                    var N = (o + h) / 2
                      , m = (s + c) / 2
                      , z = (u + f) / 2;
                    w.push(new r.a(b[7],N,m,z,h,c,f), new r.a(b[6],o,m,z,N,c,f), new r.a(b[5],N,s,z,h,m,f), new r.a(b[4],o,s,z,N,m,f), new r.a(b[3],N,m,u,h,c,z), new r.a(b[2],o,m,u,N,c,z), new r.a(b[1],N,s,u,h,m,z), new r.a(b[0],o,s,u,N,m,z)),
                    (_ = (i >= z) << 2 | (n >= m) << 1 | t >= N) && (l = w[w.length - 1],
                    w[w.length - 1] = w[w.length - 1 - _],
                    w[w.length - 1 - _] = l)
                } else {
                    var O = t - +this._x.call(null, b.data)
                      , A = n - +this._y.call(null, b.data)
                      , M = i - +this._z.call(null, b.data)
                      , j = O * O + A * A + M * M;
                    if (j < e) {
                        var k = Math.sqrt(e = j);
                        v = t - k,
                        y = n - k,
                        p = i - k,
                        d = t + k,
                        x = n + k,
                        g = i + k,
                        a = b.data
                    }
                }
        return a
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        for (var n = 0, i = t.length; n < i; ++n)
            this.remove(t[n]);
        return this
    }
    ,
    n.a = function(t) {
        if (isNaN(a = +this._x.call(null, t)) || isNaN(o = +this._y.call(null, t)) || isNaN(s = +this._z.call(null, t)))
            return this;
        var n, i, r, e, a, o, s, u, h, c, f, l, _, v, y, p = this._root, d = this._x0, x = this._y0, g = this._z0, w = this._x1, b = this._y1, N = this._z1;
        if (!p)
            return this;
        if (p.length)
            for (; ; ) {
                if ((f = a >= (u = (d + w) / 2)) ? d = u : w = u,
                (l = o >= (h = (x + b) / 2)) ? x = h : b = h,
                (_ = s >= (c = (g + N) / 2)) ? g = c : N = c,
                n = p,
                !(p = p[v = _ << 2 | l << 1 | f]))
                    return this;
                if (!p.length)
                    break;
                (n[v + 1 & 7] || n[v + 2 & 7] || n[v + 3 & 7] || n[v + 4 & 7] || n[v + 5 & 7] || n[v + 6 & 7] || n[v + 7 & 7]) && (i = n,
                y = v)
            }
        for (; p.data !== t; )
            if (r = p,
            !(p = p.next))
                return this;
        return (e = p.next) && delete p.next,
        r ? (e ? r.next = e : delete r.next,
        this) : n ? (e ? n[v] = e : delete n[v],
        (p = n[0] || n[1] || n[2] || n[3] || n[4] || n[5] || n[6] || n[7]) && p === (n[7] || n[6] || n[5] || n[4] || n[3] || n[2] || n[1] || n[0]) && !p.length && (i ? i[y] = p : this._root = p),
        this) : (this._root = e,
        this)
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        return this._root
    }
}
, function(t, n, i) {
    "use strict";
    n.a = function() {
        var t = 0;
        return this.visit(function(n) {
            if (!n.length)
                do {
                    ++t
                } while (n = n.next)
        }),
        t
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(3);
    n.a = function(t) {
        var n, i, e, a, o, s, u, h, c = [], f = this._root;
        for (f && c.push(new r.a(f,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1)); n = c.pop(); )
            if (!t(f = n.node, e = n.x0, a = n.y0, o = n.z0, s = n.x1, u = n.y1, h = n.z1) && f.length) {
                var l = (e + s) / 2
                  , _ = (a + u) / 2
                  , v = (o + h) / 2;
                (i = f[7]) && c.push(new r.a(i,l,_,v,s,u,h)),
                (i = f[6]) && c.push(new r.a(i,e,_,v,l,u,h)),
                (i = f[5]) && c.push(new r.a(i,l,a,v,s,_,h)),
                (i = f[4]) && c.push(new r.a(i,e,a,v,l,_,h)),
                (i = f[3]) && c.push(new r.a(i,l,_,o,s,u,v)),
                (i = f[2]) && c.push(new r.a(i,e,_,o,l,u,v)),
                (i = f[1]) && c.push(new r.a(i,l,a,o,s,_,v)),
                (i = f[0]) && c.push(new r.a(i,e,a,o,l,_,v))
            }
        return this
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(3);
    n.a = function(t) {
        var n, i = [], e = [];
        for (this._root && i.push(new r.a(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1)); n = i.pop(); ) {
            var a = n.node;
            if (a.length) {
                var o, s = n.x0, u = n.y0, h = n.z0, c = n.x1, f = n.y1, l = n.z1, _ = (s + c) / 2, v = (u + f) / 2, y = (h + l) / 2;
                (o = a[0]) && i.push(new r.a(o,s,u,h,_,v,y)),
                (o = a[1]) && i.push(new r.a(o,_,u,h,c,v,y)),
                (o = a[2]) && i.push(new r.a(o,s,v,h,_,f,y)),
                (o = a[3]) && i.push(new r.a(o,_,v,h,c,f,y)),
                (o = a[4]) && i.push(new r.a(o,s,u,y,_,v,l)),
                (o = a[5]) && i.push(new r.a(o,_,u,y,c,v,l)),
                (o = a[6]) && i.push(new r.a(o,s,v,y,_,f,l)),
                (o = a[7]) && i.push(new r.a(o,_,v,y,c,f,l))
            }
            e.push(n)
        }
        for (; n = e.pop(); )
            t(n.node, n.x0, n.y0, n.z0, n.x1, n.y1, n.z1);
        return this
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        return t[0]
    }
    ,
    n.a = function(t) {
        return arguments.length ? (this._x = t,
        this) : this._x
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        return t[1]
    }
    ,
    n.a = function(t) {
        return arguments.length ? (this._y = t,
        this) : this._y
    }
}
, function(t, n, i) {
    "use strict";
    n.b = function(t) {
        return t[2]
    }
    ,
    n.a = function(t) {
        return arguments.length ? (this._z = t,
        this) : this._z
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(0)
      , e = i(4);
    function a(t) {
        return t.index
    }
    function o(t, n) {
        var i = t.get(n);
        if (!i)
            throw new Error("node not found: " + n);
        return i
    }
    n.a = function(t) {
        var n, i, s, u, h, c, f, l = a, _ = function(t) {
            return 1 / Math.min(h[t.source.index], h[t.target.index])
        }, v = Object(r.a)(30), y = 1;
        function p(r) {
            for (var a = 0, o = t.length; a < y; ++a)
                for (var s, h, l, _, v, p = 0, d = 0, x = 0, g = 0; p < o; ++p)
                    h = (s = t[p]).source,
                    d = (l = s.target).x + l.vx - h.x - h.vx || Object(e.a)(f),
                    u > 1 && (x = l.y + l.vy - h.y - h.vy || Object(e.a)(f)),
                    u > 2 && (g = l.z + l.vz - h.z - h.vz || Object(e.a)(f)),
                    d *= _ = ((_ = Math.sqrt(d * d + x * x + g * g)) - i[p]) / _ * r * n[p],
                    x *= _,
                    g *= _,
                    l.vx -= d * (v = c[p]),
                    u > 1 && (l.vy -= x * v),
                    u > 2 && (l.vz -= g * v),
                    h.vx += d * (v = 1 - v),
                    u > 1 && (h.vy += x * v),
                    u > 2 && (h.vz += g * v)
        }
        function d() {
            if (s) {
                var r, e, a = s.length, u = t.length, f = new Map(s.map((t,n)=>[l(t, n, s), t]));
                for (r = 0,
                h = new Array(a); r < u; ++r)
                    (e = t[r]).index = r,
                    "object" != typeof e.source && (e.source = o(f, e.source)),
                    "object" != typeof e.target && (e.target = o(f, e.target)),
                    h[e.source.index] = (h[e.source.index] || 0) + 1,
                    h[e.target.index] = (h[e.target.index] || 0) + 1;
                for (r = 0,
                c = new Array(u); r < u; ++r)
                    e = t[r],
                    c[r] = h[e.source.index] / (h[e.source.index] + h[e.target.index]);
                n = new Array(u),
                x(),
                i = new Array(u),
                g()
            }
        }
        function x() {
            if (s)
                for (var i = 0, r = t.length; i < r; ++i)
                    n[i] = +_(t[i], i, t)
        }
        function g() {
            if (s)
                for (var n = 0, r = t.length; n < r; ++n)
                    i[n] = +v(t[n], n, t)
        }
        return null == t && (t = []),
        p.initialize = function(t, ...n) {
            s = t,
            f = n.find(t=>"function" == typeof t) || Math.random,
            u = n.find(t=>[1, 2, 3].includes(t)) || 2,
            d()
        }
        ,
        p.links = function(n) {
            return arguments.length ? (t = n,
            d(),
            p) : t
        }
        ,
        p.id = function(t) {
            return arguments.length ? (l = t,
            p) : l
        }
        ,
        p.iterations = function(t) {
            return arguments.length ? (y = +t,
            p) : y
        }
        ,
        p.strength = function(t) {
            return arguments.length ? (_ = "function" == typeof t ? t : Object(r.a)(+t),
            x(),
            p) : _
        }
        ,
        p.distance = function(t) {
            return arguments.length ? (v = "function" == typeof t ? t : Object(r.a)(+t),
            g(),
            p) : v
        }
        ,
        p
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(6)
      , e = i(7)
      , a = i(8)
      , o = i(0)
      , s = i(4)
      , u = i(9);
    n.a = function() {
        var t, n, i, h, c, f, l = Object(o.a)(-30), _ = 1, v = 1 / 0, y = .81;
        function p(o) {
            var s, h = t.length, f = (1 === n ? Object(r.a)(t, u.b) : 2 === n ? Object(e.a)(t, u.b, u.c) : 3 === n ? Object(a.a)(t, u.b, u.c, u.d) : null).visitAfter(x);
            for (c = o,
            s = 0; s < h; ++s)
                i = t[s],
                f.visit(g)
        }
        function d() {
            if (t) {
                var n, i, r = t.length;
                for (f = new Array(r),
                n = 0; n < r; ++n)
                    i = t[n],
                    f[i.index] = +l(i, n, t)
            }
        }
        function x(t) {
            var i, r, e, a, o, s, u = 0, h = 0, c = t.length;
            if (c) {
                for (e = a = o = s = 0; s < c; ++s)
                    (i = t[s]) && (r = Math.abs(i.value)) && (u += i.value,
                    h += r,
                    e += r * (i.x || 0),
                    a += r * (i.y || 0),
                    o += r * (i.z || 0));
                u *= Math.sqrt(4 / c),
                t.x = e / h,
                n > 1 && (t.y = a / h),
                n > 2 && (t.z = o / h)
            } else {
                (i = t).x = i.data.x,
                n > 1 && (i.y = i.data.y),
                n > 2 && (i.z = i.data.z);
                do {
                    u += f[i.data.index]
                } while (i = i.next)
            }
            t.value = u
        }
        function g(t, r, e, a, o) {
            if (!t.value)
                return !0;
            var u = [e, a, o][n - 1]
              , l = t.x - i.x
              , p = n > 1 ? t.y - i.y : 0
              , d = n > 2 ? t.z - i.z : 0
              , x = u - r
              , g = l * l + p * p + d * d;
            if (x * x / y < g)
                return g < v && (0 === l && (g += (l = Object(s.a)(h)) * l),
                n > 1 && 0 === p && (g += (p = Object(s.a)(h)) * p),
                n > 2 && 0 === d && (g += (d = Object(s.a)(h)) * d),
                g < _ && (g = Math.sqrt(_ * g)),
                i.vx += l * t.value * c / g,
                n > 1 && (i.vy += p * t.value * c / g),
                n > 2 && (i.vz += d * t.value * c / g)),
                !0;
            if (!(t.length || g >= v)) {
                (t.data !== i || t.next) && (0 === l && (g += (l = Object(s.a)(h)) * l),
                n > 1 && 0 === p && (g += (p = Object(s.a)(h)) * p),
                n > 2 && 0 === d && (g += (d = Object(s.a)(h)) * d),
                g < _ && (g = Math.sqrt(_ * g)));
                do {
                    t.data !== i && (x = f[t.data.index] * c / g,
                    i.vx += l * x,
                    n > 1 && (i.vy += p * x),
                    n > 2 && (i.vz += d * x))
                } while (t = t.next)
            }
        }
        return p.initialize = function(i, ...r) {
            t = i,
            h = r.find(t=>"function" == typeof t) || Math.random,
            n = r.find(t=>[1, 2, 3].includes(t)) || 2,
            d()
        }
        ,
        p.strength = function(t) {
            return arguments.length ? (l = "function" == typeof t ? t : Object(o.a)(+t),
            d(),
            p) : l
        }
        ,
        p.distanceMin = function(t) {
            return arguments.length ? (_ = t * t,
            p) : Math.sqrt(_)
        }
        ,
        p.distanceMax = function(t) {
            return arguments.length ? (v = t * t,
            p) : Math.sqrt(v)
        }
        ,
        p.theta = function(t) {
            return arguments.length ? (y = t * t,
            p) : Math.sqrt(y)
        }
        ,
        p
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(57);
    i.d(n, "a", function() {
        return r.a
    })
}
, function(t, n, i) {
    "use strict";
    var r = {
        value: ()=>{}
    };
    function e() {
        for (var t, n = 0, i = arguments.length, r = {}; n < i; ++n) {
            if (!(t = arguments[n] + "") || t in r || /[\s.]/.test(t))
                throw new Error("illegal type: " + t);
            r[t] = []
        }
        return new a(r)
    }
    function a(t) {
        this._ = t
    }
    function o(t, n) {
        for (var i, r = 0, e = t.length; r < e; ++r)
            if ((i = t[r]).name === n)
                return i.value
    }
    function s(t, n, i) {
        for (var e = 0, a = t.length; e < a; ++e)
            if (t[e].name === n) {
                t[e] = r,
                t = t.slice(0, e).concat(t.slice(e + 1));
                break
            }
        return null != i && t.push({
            name: n,
            value: i
        }),
        t
    }
    a.prototype = e.prototype = {
        constructor: a,
        on: function(t, n) {
            var i, r, e = this._, a = (r = e,
            (t + "").trim().split(/^|\s+/).map(function(t) {
                var n = ""
                  , i = t.indexOf(".");
                if (i >= 0 && (n = t.slice(i + 1),
                t = t.slice(0, i)),
                t && !r.hasOwnProperty(t))
                    throw new Error("unknown type: " + t);
                return {
                    type: t,
                    name: n
                }
            })), u = -1, h = a.length;
            if (!(arguments.length < 2)) {
                if (null != n && "function" != typeof n)
                    throw new Error("invalid callback: " + n);
                for (; ++u < h; )
                    if (i = (t = a[u]).type)
                        e[i] = s(e[i], t.name, n);
                    else if (null == n)
                        for (i in e)
                            e[i] = s(e[i], t.name, null);
                return this
            }
            for (; ++u < h; )
                if ((i = (t = a[u]).type) && (i = o(e[i], t.name)))
                    return i
        },
        copy: function() {
            var t = {}
              , n = this._;
            for (var i in n)
                t[i] = n[i].slice();
            return new a(t)
        },
        call: function(t, n) {
            if ((i = arguments.length - 2) > 0)
                for (var i, r, e = new Array(i), a = 0; a < i; ++a)
                    e[a] = arguments[a + 2];
            if (!this._.hasOwnProperty(t))
                throw new Error("unknown type: " + t);
            for (a = 0,
            i = (r = this._[t]).length; a < i; ++a)
                r[a].value.apply(n, e)
        },
        apply: function(t, n, i) {
            if (!this._.hasOwnProperty(t))
                throw new Error("unknown type: " + t);
            for (var r = this._[t], e = 0, a = r.length; e < a; ++e)
                r[e].value.apply(n, i)
        }
    },
    n.a = e
}
, function(t, n, i) {
    "use strict";
    var r = i(5);
    i.d(n, "a", function() {
        return r.c
    });
    i(59),
    i(60)
}
, function(t, n, i) {
    "use strict";
    i(5)
}
, function(t, n, i) {
    "use strict";
    i(5)
}
, function(t, n, i) {
    "use strict";
    const r = 4294967296;
    n.a = function() {
        let t = 1;
        return ()=>(t = (1664525 * t + 1013904223) % r) / r
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(0);
    n.a = function(t, n, i, e) {
        var a, o, s, u, h = Object(r.a)(.1);
        function c(t) {
            for (var r = 0, h = a.length; r < h; ++r) {
                var c = a[r]
                  , f = c.x - n || 1e-6
                  , l = (c.y || 0) - i || 1e-6
                  , _ = (c.z || 0) - e || 1e-6
                  , v = Math.sqrt(f * f + l * l + _ * _)
                  , y = (u[r] - v) * s[r] * t / v;
                c.vx += f * y,
                o > 1 && (c.vy += l * y),
                o > 2 && (c.vz += _ * y)
            }
        }
        function f() {
            if (a) {
                var n, i = a.length;
                for (s = new Array(i),
                u = new Array(i),
                n = 0; n < i; ++n)
                    u[n] = +t(a[n], n, a),
                    s[n] = isNaN(u[n]) ? 0 : +h(a[n], n, a)
            }
        }
        return "function" != typeof t && (t = Object(r.a)(+t)),
        null == n && (n = 0),
        null == i && (i = 0),
        null == e && (e = 0),
        c.initialize = function(t, ...n) {
            a = t,
            o = n.find(t=>[1, 2, 3].includes(t)) || 2,
            f()
        }
        ,
        c.strength = function(t) {
            return arguments.length ? (h = "function" == typeof t ? t : Object(r.a)(+t),
            f(),
            c) : h
        }
        ,
        c.radius = function(n) {
            return arguments.length ? (t = "function" == typeof n ? n : Object(r.a)(+n),
            f(),
            c) : t
        }
        ,
        c.x = function(t) {
            return arguments.length ? (n = +t,
            c) : n
        }
        ,
        c.y = function(t) {
            return arguments.length ? (i = +t,
            c) : i
        }
        ,
        c.z = function(t) {
            return arguments.length ? (e = +t,
            c) : e
        }
        ,
        c
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(0);
    n.a = function(t) {
        var n, i, e, a = Object(r.a)(.1);
        function o(t) {
            for (var r, a = 0, o = n.length; a < o; ++a)
                (r = n[a]).vx += (e[a] - r.x) * i[a] * t
        }
        function s() {
            if (n) {
                var r, o = n.length;
                for (i = new Array(o),
                e = new Array(o),
                r = 0; r < o; ++r)
                    i[r] = isNaN(e[r] = +t(n[r], r, n)) ? 0 : +a(n[r], r, n)
            }
        }
        return "function" != typeof t && (t = Object(r.a)(null == t ? 0 : +t)),
        o.initialize = function(t) {
            n = t,
            s()
        }
        ,
        o.strength = function(t) {
            return arguments.length ? (a = "function" == typeof t ? t : Object(r.a)(+t),
            s(),
            o) : a
        }
        ,
        o.x = function(n) {
            return arguments.length ? (t = "function" == typeof n ? n : Object(r.a)(+n),
            s(),
            o) : t
        }
        ,
        o
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(0);
    n.a = function(t) {
        var n, i, e, a = Object(r.a)(.1);
        function o(t) {
            for (var r, a = 0, o = n.length; a < o; ++a)
                (r = n[a]).vy += (e[a] - r.y) * i[a] * t
        }
        function s() {
            if (n) {
                var r, o = n.length;
                for (i = new Array(o),
                e = new Array(o),
                r = 0; r < o; ++r)
                    i[r] = isNaN(e[r] = +t(n[r], r, n)) ? 0 : +a(n[r], r, n)
            }
        }
        return "function" != typeof t && (t = Object(r.a)(null == t ? 0 : +t)),
        o.initialize = function(t) {
            n = t,
            s()
        }
        ,
        o.strength = function(t) {
            return arguments.length ? (a = "function" == typeof t ? t : Object(r.a)(+t),
            s(),
            o) : a
        }
        ,
        o.y = function(n) {
            return arguments.length ? (t = "function" == typeof n ? n : Object(r.a)(+n),
            s(),
            o) : t
        }
        ,
        o
    }
}
, function(t, n, i) {
    "use strict";
    var r = i(0);
    n.a = function(t) {
        var n, i, e, a = Object(r.a)(.1);
        function o(t) {
            for (var r, a = 0, o = n.length; a < o; ++a)
                (r = n[a]).vz += (e[a] - r.z) * i[a] * t
        }
        function s() {
            if (n) {
                var r, o = n.length;
                for (i = new Array(o),
                e = new Array(o),
                r = 0; r < o; ++r)
                    i[r] = isNaN(e[r] = +t(n[r], r, n)) ? 0 : +a(n[r], r, n)
            }
        }
        return "function" != typeof t && (t = Object(r.a)(null == t ? 0 : +t)),
        o.initialize = function(t) {
            n = t,
            s()
        }
        ,
        o.strength = function(t) {
            return arguments.length ? (a = "function" == typeof t ? t : Object(r.a)(+t),
            s(),
            o) : a
        }
        ,
        o.z = function(n) {
            return arguments.length ? (t = "function" == typeof n ? n : Object(r.a)(+n),
            s(),
            o) : t
        }
        ,
        o
    }
}
, function(t, n, i) {
    "use strict";
    Object.defineProperty(n, "__esModule", {
        value: !0
    });
    const r = function(t) {
        return function() {
            return t
        }
    };
    n.default = function(t) {
        var n, i, e, a = r(.1), o = 1;
        function s(t) {
            for (var r = 0; r < o; ++r)
                for (var a, s = 0, u = n.length; s < u; ++s)
                    a = n[s],
                    isNaN(a.vz) && (a.vz = .5),
                    a.vz += (e[s] - a.z) * i[s] * t
        }
        function u() {
            if (n) {
                var r, o = n.length;
                for (i = new Array(o),
                e = new Array(o),
                r = 0; r < o; ++r)
                    i[r] = isNaN(e[r] = +t(n[r], r, n)) ? 0 : +a()
            }
        }
        return "function" != typeof t && (t = r(null == t ? 0 : +t)),
        s.initialize = function(t) {
            n = t,
            u()
        }
        ,
        s.strength = function(t) {
            return arguments.length ? (a = "function" == typeof t ? t : r(+t),
            u(),
            s) : a
        }
        ,
        s.z = function(n) {
            return arguments.length ? (t = "function" == typeof n ? n : r(+n),
            u(),
            s) : t
        }
        ,
        s.iterations = function(t) {
            return arguments.length ? (o = +t,
            s) : o
        }
        ,
        s
    }
}
, function(t, n, i) {
    "use strict";
    Object.defineProperty(n, "__esModule", {
        value: !0
    });
    n.INIT_GRAPH = "init_graph";
    n.RESTART_FORCE_LAYOUT = "restart_force_layout";
    n.STOP_FORCE_LAYOUT = "stop_force_layout";
    n.PIN_NODES = "pin_nodes";
    n.SETTING_LAYOUT_OPTIONS = "setting_layout_options";
    n.UPDATE_POSITION = "update_position";
    n.ENABLE_LAYOUT = "enable_layout";
    n.FINISH_LAYOUT = "finish_layout"
}
]);
