package edu.memphis.ccrg.alife.elements;

import edu.memphis.ccrg.alife.elements.properties.AttributableImpl;
import edu.memphis.ccrg.alife.opreations.UpdateStrategy;
import edu.memphis.ccrg.alife.utils.ConcurrentHashSet;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import java.util.Collections;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CellImpl extends AttributableImpl implements Cell {
    private static final Logger logger = Logger.getLogger(CellImpl.class.getCanonicalName());
    private int capacity;
    private Set<ALifeObject> objects;
    private int occupancy;
    private UpdateStrategy updateStrategy;
    private int x;
    private int y;

    public CellImpl() {
        this.objects = new ConcurrentHashSet();
    }

    public CellImpl(int x2, int y2) {
        this();
        this.x = x2;
        this.y = y2;
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public synchronized boolean addObject(ALifeObject o) {
        boolean z;
        if (this.objects.contains(o) || this.occupancy + o.getSize() > this.capacity) {
            z = false;
        } else {
            this.occupancy += o.getSize();
            this.objects.add(o);
            o.setContainer(this);
            z = true;
        }
        return z;
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public int getObjectCount() {
        return this.objects.size();
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public boolean isEmpty() {
        return this.objects.size() == 0;
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public synchronized void removeObject(ALifeObject o) {
        if (this.objects.remove(o)) {
            this.occupancy -= o.getSize();
            o.setContainer(null);
        }
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public boolean containsObject(ALifeObject o) {
        return this.objects.contains(o);
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public Set<ALifeObject> getObjects() {
        return Collections.unmodifiableSet(this.objects);
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public synchronized void clearContainer() {
        this.objects.clear();
        this.occupancy = 0;
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public boolean isContainerFull() {
        return this.occupancy == this.capacity;
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public int getContainerOccupancy() {
        return this.occupancy;
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public void setCapacity(int c) {
        this.capacity = c;
    }

    @Override // edu.memphis.ccrg.alife.elements.ObjectContainer
    public int getCapacity() {
        return this.capacity;
    }

    @Override // edu.memphis.ccrg.alife.elements.Cell
    public int getXCoordinate() {
        return this.x;
    }

    @Override // edu.memphis.ccrg.alife.elements.Cell
    public void setXCoordinate(int x2) {
        this.x = x2;
    }

    @Override // edu.memphis.ccrg.alife.elements.Cell
    public int getYCoordinate() {
        return this.y;
    }

    @Override // edu.memphis.ccrg.alife.elements.Cell
    public void setYCoordinate(int y2) {
        this.y = y2;
    }

    @Override // edu.memphis.ccrg.alife.opreations.Updateable
    public void updateState(ALifeWorld world) {
        if (this.updateStrategy != null) {
            this.updateStrategy.updateState(this, world);
        } else {
            logger.log(Level.FINEST, "cell {0} asked to #updateState but does not have an UpdateStrategy", this);
        }
    }

    @Override // edu.memphis.ccrg.alife.opreations.Updateable
    public void setUpdateStrategy(UpdateStrategy updateStrategy2) {
        this.updateStrategy = updateStrategy2;
    }

    public String toString() {
        return "cell[" + this.x + "," + this.y + "]";
    }
}
