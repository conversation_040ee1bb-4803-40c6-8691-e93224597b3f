/*
 * Created by JFormDesigner on Sat Dec 21 01:40:43 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.actionselection.Action;
import edu.memphis.ccrg.lida.actionselection.ActionSelection;
import edu.memphis.ccrg.lida.actionselection.ActionSelectionListener;
import edu.memphis.ccrg.lida.actionselection.Behavior;
import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import java.awt.*;
import java.text.DecimalFormat;
import java.util.Collection;
import java.util.LinkedList;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * A {@link GuiPanel} which displays the current {@link Behavior} elements in
 * the {@link ActionSelection} module.
 * 
 * <AUTHOR>
 */
public class ActionSelectionPanel0 extends GuiPanelImpl implements
        ActionSelectionListener {
    private static final Logger logger = Logger
            .getLogger(ActionSelectionPanel0.class.getCanonicalName());
    private static final int DEFAULT_SELECTED_ACTIONS_SIZE = 20;

    private FrameworkModule module;
    private Collection<Behavior> behaviors;
    private Behavior[] behaviorArray = new Behavior[0];

    private LinkedList<ActionDetail> selectedActions = new LinkedList<ActionDetail>();
    private int selectedActionsSize;

    /** Creates new form ActionSelectionPanel */
    public ActionSelectionPanel0() {
        initComponents();
    }
    private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {
        refresh();
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jSplitPane1 = new JSplitPane();
        winnersPane = new JScrollPane();
        winnersTable = new JTable();
        coalitionsPane1 = new JScrollPane();
        behaviorsTable = new JTable();

        //======== this ========
        setPreferredSize(new Dimension(500, 291));
        setMinimumSize(new Dimension(200, 150));

        //======== jSplitPane1 ========
        {
            jSplitPane1.setDividerLocation(150);
            jSplitPane1.setOrientation(JSplitPane.VERTICAL_SPLIT);

            //======== winnersPane ========
            {

                //---- winnersTable ----
                winnersTable.setModel(new SelectedBehaviorsTableModel());
                winnersPane.setViewportView(winnersTable);
            }
            jSplitPane1.setBottomComponent(winnersPane);

            //======== coalitionsPane1 ========
            {

                //---- behaviorsTable ----
                behaviorsTable.setModel(new BehaviorTableModel());
                coalitionsPane1.setViewportView(behaviorsTable);
            }
            jSplitPane1.setTopComponent(coalitionsPane1);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addComponent(jSplitPane1, GroupLayout.Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addComponent(jSplitPane1, GroupLayout.Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 300, Short.MAX_VALUE)
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JSplitPane jSplitPane1;
    private JScrollPane winnersPane;
    private JTable winnersTable;
    private JScrollPane coalitionsPane1;
    private JTable behaviorsTable;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    @Override
    public void initPanel(String[] param) {
        module = agent.getSubmodule(ModuleName.ActionSelection);
        if (module == null) {
            logger.log(Level.WARNING,
                    "Error initializing panel, Module does not exist in agent.");
        } else {
            module.addListener(this);
        }

        selectedActionsSize = DEFAULT_SELECTED_ACTIONS_SIZE;

        if(param.length > 0){
            try{
                selectedActionsSize = Integer.parseInt(param[0]);
            }catch(NumberFormatException e){
                logger.log(Level.WARNING, "parse error, using default selectActionsSize");
            }
        }else{
            logger.log(Level.INFO, "using default selectActionsSize");
        }
    }

    @Override
    public void refresh() {
        display(module.getModuleContent("behaviors"));
    }

    @SuppressWarnings("unchecked")
    @Override
    public void display(Object o) {
        if(o != null){
            behaviors = (Collection<Behavior>) o;
            behaviorArray = behaviors.toArray(new Behavior[0]);

            ((AbstractTableModel) behaviorsTable.getModel())
                    .fireTableStructureChanged();
            ((AbstractTableModel) winnersTable.getModel())
                    .fireTableStructureChanged();
        }
    }

    private class BehaviorTableModel extends AbstractTableModel {
        private String[] columNames = { "Behavior Label", "Activation",
                "Context", "Action", "Adding Result", "Deleting Result" };
        private DecimalFormat df = new DecimalFormat("0.0000");

        @Override
        public int getColumnCount() {
            return columNames.length;
        }

        @Override
        public int getRowCount() {
            return behaviorArray.length;
        }

        @Override
        public String getColumnName(int column) {
            if (column < columNames.length) {
                return columNames[column];
            }
            return "";
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            if (rowIndex > behaviorArray.length
                    || columnIndex > columNames.length || rowIndex < 0
                    || columnIndex < 0) {
                return null;
            }
            Behavior behavior = behaviorArray[rowIndex];

            switch (columnIndex) {
                case 0:
                    return behavior.getName();
                case 1:
                    return df.format(behavior.getActivation());
                case 2:
                    return behavior.getContextConditions();
                case 3:
                    return behavior.getAction().getName();
                case 4:
                    return behavior.getAddingList();
                case 5:
                    return behavior.getDeletingList();
                default:
                    return "";
            }

        }
    }

    private class SelectedBehaviorsTableModel extends AbstractTableModel {

		private String[] columnNames = { "Tick at Selection",
				"Selection Count", "Action" };

		// private DecimalFormat df = new DecimalFormat("0.0000");

        @Override
        public int getColumnCount() {
            return columnNames.length;
        }

        @Override
        public int getRowCount() {
            return selectedActions.size();
        }

        @Override
        public String getColumnName(int column) {
            if (column < columnNames.length) {
                return columnNames[column];
            }
            return "";
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            if (rowIndex > selectedActions.size() || rowIndex < 0
                    || columnIndex > columnNames.length || columnIndex < 0) {
                return null;
            }
            ActionDetail behaviorDetail = selectedActions.get(rowIndex);
            switch (columnIndex) {
                case 0:
                    return behaviorDetail.getTickAtSelection();
                case 1:
                    return behaviorDetail.getSelectedActionCount();
                case 2:
                    return behaviorDetail.getAction().getName();
                default:
                    return "";
            }
        }
    }

    private class ActionDetail {
        private final long tick;
        private final int selectedActionCount;
        private final Action action;

		public ActionDetail(long t, int count, Action a) {
			tick = t;
			selectedActionCount = count;
			action = a;
		}

        public long getTickAtSelection() {
            return tick;
        }

        public int getSelectedActionCount(){
            return selectedActionCount;
        }

		public Action getAction() {
			return action;
		}
	}

    private int currentSelectionCount;

	@Override
	public void receiveAction(Action action) {
		ActionDetail detail = new ActionDetail(TaskManager.getCurrentTick(),
				currentSelectionCount++, action);
		synchronized (this) {
			selectedActions.addFirst(detail);
			if (selectedActions.size() > selectedActionsSize) {
				selectedActions.pollLast();
			}
		}
	}
}
