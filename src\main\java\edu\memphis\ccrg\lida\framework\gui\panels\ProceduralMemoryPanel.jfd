JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {comp jToolBar1::l::400:x, comp jScrollPane1::l::400:x}"
			"$verticalGroup": "par l {seq l {comp jToolBar1:::p:25:p, space :p::p, comp jScrollPane1::::269:x}}"
		} ) {
			name: "this"
			"preferredSize": new java.awt.Dimension( 300, 200 )
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "jToolBar1"
				"rollover": true
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "refreshButton"
					"text": "Refresh"
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "refreshButtonActionPerformed", true ) )
				} )
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "jScrollPane1"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "schemeTable"
					"preferredScrollableViewportSize": new java.awt.Dimension( 250, 200 )
					auxiliary() {
						"JavaCodeGenerator.preInitCode": "${field}.setModel(new SchemeTableModel());"
					}
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
