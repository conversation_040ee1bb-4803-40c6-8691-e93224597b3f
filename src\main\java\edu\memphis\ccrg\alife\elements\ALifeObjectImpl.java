package edu.memphis.ccrg.alife.elements;

import edu.memphis.ccrg.alife.elements.properties.AttributableImpl;
import edu.memphis.ccrg.alife.opreations.UpdateStrategy;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ALifeObjectImpl extends AttributableImpl implements ALifeObject {
    private static int idGenerator = 0;
    private static final Logger logger = Logger.getLogger(ALifeObjectImpl.class.getCanonicalName());
    private ObjectContainer container;
    private volatile double health;
    private int iconId;
    private int id;
    private String name = "";
    private volatile boolean removable;
    private int size;
    protected UpdateStrategy updateStrategy;

    public ALifeObjectImpl() {
        int i = idGenerator;
        idGenerator = i + 1;
        this.id = i;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public int getId() {
        return this.id;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public String getName() {
        return this.name;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public void setName(String name2) {
        this.name = name2;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public int getIconId() {
        return this.iconId;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public void setIconId(int id2) {
        this.iconId = id2;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public void setRemovable(boolean removable2) {
        this.removable = removable2;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public boolean isRemovable() {
        return this.removable;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public synchronized ObjectContainer getContainer() {
        return this.container;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public synchronized void setContainer(ObjectContainer container2) {
        this.container = container2;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public int getSize() {
        return this.size;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public void setSize(int size2) {
        if (size2 > 0) {
            this.size = size2;
        } else {
            logger.log(Level.WARNING, "size must be positive");
        }
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public double getHealth() {
        return this.health;
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public void setHealth(double amount) {
        if (amount < 0.0d) {
            this.health = 0.0d;
        } else if (amount > 1.0d) {
            this.health = 1.0d;
        } else {
            this.health = amount;
        }
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public void decreaseHealth(double amount) {
        double newAmt = this.health - amount;
        if (newAmt < 0.0d) {
            this.health = 0.0d;
        } else if (newAmt > 1.0d) {
            this.health = 1.0d;
        } else {
            this.health = newAmt;
        }
    }

    @Override // edu.memphis.ccrg.alife.elements.ALifeObject
    public void increaseHealth(double amount) {
        double newAmt = this.health + amount;
        if (newAmt > 1.0d) {
            this.health = 1.0d;
        } else if (newAmt < 0.0d) {
            this.health = 0.0d;
        } else {
            this.health = newAmt;
        }
    }

    @Override // edu.memphis.ccrg.alife.opreations.Updateable
    public void setUpdateStrategy(UpdateStrategy strategy) {
        this.updateStrategy = strategy;
    }

    @Override // edu.memphis.ccrg.alife.opreations.Updateable
    public void updateState(ALifeWorld world) {
    }

    public boolean equals(Object o) {
        if (!(o instanceof ALifeObjectImpl) || ((ALifeObjectImpl) o).id != this.id) {
            return false;
        }
        return true;
    }

    public int hashCode() {
        return this.id;
    }

    public String toString() {
        return this.name + this.id;
    }
}
