/* 

 By 贤心

 */
 

//用于演示页面
layui.use(['util'], function(){
  var $ = layui.$
  ,util = layui.util;
  
  //固定Bar
  util.fixbar({
    bar1: true
    ,click: function(type){
      if(type === 'bar1'){
        location.href = 'http://fly.layui.com/';
      }
    }
  });
  
  
  //记录下载数
  $('.alone-get').on('click',function(){
    $.get('//fly.layui.com/api/handle?id=14', function(){}, 'jsonp');
  });
  
  //获取下载数
  var downs = $('.alone-downs');
  if(downs[0]){
    $.get('//fly.layui.com/api/handle?alias=FlyTemplate', function(res){
      downs.html(res.number);
    }, 'jsonp');
  }
});