<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="1" max="-2" attributes="0">
                  <Component id="jScrollPane2" alignment="0" max="32767" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <Group type="103" groupAlignment="0" max="-2" attributes="0">
                          <Component id="jLabel2" max="32767" attributes="0"/>
                          <Component id="inputPanel" max="32767" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jButton1" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="eventCheck" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="102" attributes="0">
                              <Component id="spliceCheckbox" min="-2" max="-2" attributes="0"/>
                              <EmptySpace type="unrelated" max="-2" attributes="0"/>
                              <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="spliceField" min="-2" pref="43" max="-2" attributes="0"/>
                          </Group>
                          <Component id="jLabel5" min="-2" pref="267" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace pref="47" max="32767" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jLabel4" min="-2" pref="267" max="-2" attributes="0"/>
                          <Component id="jScrollPane3" min="-2" pref="143" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="-2" pref="65" max="-2" attributes="0"/>
                  </Group>
                  <Component id="jLabel1" max="32767" attributes="0"/>
              </Group>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="spliceCheckbox" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="jButton1" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="spliceField" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="eventCheck" alignment="3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="1" max="-2" attributes="0"/>
                              <Component id="jScrollPane3" min="-2" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                  </Group>
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="8" max="-2" attributes="0"/>
                      <Component id="inputPanel" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jScrollPane2" pref="258" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JButton" name="jButton1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Input"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton1ActionPerformed"/>
      </Events>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane2">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextPane" name="jTextPane1">
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Start knowledge and potential training samples:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="Enter a sentence. Example: &quot;tim eats ice in manchester.&quot; Try re-Input in case that it was misinterpreted."/>
      </Properties>
    </Component>
    <Component class="javax.swing.JCheckBox" name="spliceCheckbox">
      <Properties>
        <Property name="selected" type="boolean" value="true"/>
        <Property name="text" type="java.lang.String" value="Cut up"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="spliceField">
      <Properties>
        <Property name="text" type="java.lang.String" value="2"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Minimal segment length:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="Sentence attribute relations:"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane3">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextPane" name="attributePanel">
          <Properties>
            <Property name="text" type="java.lang.String" value="PLACE TIME IT"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="For perceiving the sentence in pieces"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JCheckBox" name="eventCheck">
      <Properties>
        <Property name="selected" type="boolean" value="true"/>
        <Property name="text" type="java.lang.String" value="Input as event"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="inputPanel">
    </Component>
  </SubComponents>
</Form>
