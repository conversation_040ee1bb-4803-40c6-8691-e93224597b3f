package edu.memphis.ccrg.alife.gui;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.elements.Cell;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.Image;
import java.awt.Point;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import javax.swing.DefaultComboBoxModel;
import javax.swing.GroupLayout;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JList;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSpinner;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.JToolBar;
import javax.swing.LayoutStyle;
import javax.swing.SpinnerNumberModel;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.table.AbstractTableModel;

public class ALifePanel extends JPanel {
    private JTable attributesTable;
    private JList cellObjectList;
    private JPanel cellimagePanel;
    private JTextField cellinfoTF;
    private Cell currentSelectedcell;
    private Dimension imageArea = new Dimension(0, 0);
    BufferedImage img;
    private JLabel jLabel1;
    private JPanel jPanel1;
    private JPanel jPanel5;
    private JScrollPane jScrollPane1;
    private JScrollPane jScrollPane3;
    private JScrollPane jScrollPane4;
    private JToolBar.Separator jSeparator1;
    private JSplitPane jSplitPane1;
    private JSplitPane jSplitPane2;
    private JSplitPane jSplitPane3;
    private JToolBar jToolBar1;
    private JPanel mainImagePanel;
    private JComboBox objectsCB;
    private JButton refreshButton;
    private ALifeWorldRenderer renderer;
    Image scaleImg;
    int scaleImgHeight;
    int scaleImgWidth;
    private double scalingFactor = 1.0d;
    private JSpinner scalingFactorSpiner;
    ALifeWorld world;

    public ALifePanel() {
        initComponents();
    }

    public void init(ALifeWorldRenderer renderer2, ALifeWorld world2, int scalingFactor2) {
        this.renderer = renderer2;
        this.world = world2;
        this.img = renderer2.renderWorld(world2);
        if (scalingFactor2 > 0) {
            setScalingFactor(((double) scalingFactor2) / 100.0d);
            this.scalingFactorSpiner.setValue(Integer.valueOf(scalingFactor2));
        }
        this.imageArea.setSize(this.scaleImgWidth, this.scaleImgHeight);
        this.mainImagePanel.setSize(this.imageArea);
        if (world2 != null) {
            this.objectsCB.setModel(new DefaultComboBoxModel(world2.getObjects().toArray()));
        }
        refresh();
    }

    private void changeSelectedCell() {
        if (this.currentSelectedcell != null) {
            // setCell、setCellImg无效，用setModel
            ((AttrListModel)this.cellObjectList.getModel()).setCell(this.currentSelectedcell);
//            this.cellObjectList.getModel().setCell(this.currentSelectedcell);
            ((CellImagePanel)this.cellimagePanel).setCellImg(this.renderer.renderCell(this.currentSelectedcell.getXCoordinate(), this.currentSelectedcell.getYCoordinate(), this.world));
            String text = this.currentSelectedcell + " capacity: " + this.currentSelectedcell.getCapacity() + " occupancy: " + this.currentSelectedcell.getContainerOccupancy();
            this.cellinfoTF.setText(text);
            this.cellinfoTF.setToolTipText(text);
        }
    }

    private void initComponents() {
        this.jToolBar1 = new JToolBar();
        this.refreshButton = new JButton();
        this.jSeparator1 = new JToolBar.Separator();
        this.jLabel1 = new JLabel();
        this.scalingFactorSpiner = new JSpinner();
        this.jSplitPane1 = new JSplitPane();
        this.jSplitPane2 = new JSplitPane();
        this.jPanel5 = new JPanel();
        this.objectsCB = new JComboBox();
        this.jScrollPane3 = new JScrollPane();
        this.attributesTable = new JTable();
        this.jSplitPane3 = new JSplitPane();
        this.jPanel1 = new JPanel();
        this.jScrollPane1 = new JScrollPane();
        this.cellObjectList = new JList();
        this.cellinfoTF = new JTextField();
        this.cellimagePanel = new CellImagePanel();
        this.jScrollPane4 = new JScrollPane();
        this.mainImagePanel = new ImagePanel(this);
        setPreferredSize(new Dimension(600, 300));
        setRequestFocusEnabled(false);
        this.jToolBar1.setRollover(true);
        this.refreshButton.setText("refresh");
        this.refreshButton.setFocusable(false);
        this.refreshButton.setHorizontalTextPosition(0);
        this.refreshButton.setVerticalTextPosition(3);
        this.refreshButton.addActionListener(new ActionListener() {
            /* class edu.memphis.ccrg.alife.gui.ALifePanel.AnonymousClass1 */

            public void actionPerformed(ActionEvent evt) {
                ALifePanel.this.refreshButtonActionPerformed(evt);
            }
        });
        this.jToolBar1.add(this.refreshButton);
        this.jToolBar1.add(this.jSeparator1);
        this.jLabel1.setText("zoom: ");
        this.jToolBar1.add(this.jLabel1);
        this.scalingFactorSpiner.setModel(new SpinnerNumberModel(100, 1, 1000, 10));
        this.scalingFactorSpiner.setToolTipText("zoom");
        this.scalingFactorSpiner.setMaximumSize(new Dimension(60, 20));
        this.scalingFactorSpiner.setPreferredSize(new Dimension(60, 20));
        this.scalingFactorSpiner.addChangeListener(new ChangeListener() {
            /* class edu.memphis.ccrg.alife.gui.ALifePanel.AnonymousClass2 */

            public void stateChanged(ChangeEvent evt) {
                ALifePanel.this.scalingFactorSpinerStateChanged(evt);
            }
        });
        this.jToolBar1.add(this.scalingFactorSpiner);
        this.jSplitPane1.setDividerLocation(getPreferredSize().width / 2);
        this.jSplitPane1.setPreferredSize(new Dimension(500, 500));
        this.jSplitPane2.setDividerLocation(getPreferredSize().height / 2);
        this.jSplitPane2.setOrientation(0);
        this.jSplitPane2.setRequestFocusEnabled(false);
        this.jPanel5.setPreferredSize(new Dimension(300, 300));
        this.objectsCB.addActionListener(new ActionListener() {
            /* class edu.memphis.ccrg.alife.gui.ALifePanel.AnonymousClass3 */

            public void actionPerformed(ActionEvent evt) {
                ALifePanel.this.objectsCBActionPerformed(evt);
            }
        });
        this.jScrollPane3.setPreferredSize(new Dimension(300, 200));
        this.attributesTable.setModel(new AttributesTableModel());
        this.jScrollPane3.setViewportView(this.attributesTable);
        GroupLayout jPanel5Layout = new GroupLayout(this.jPanel5);
        this.jPanel5.setLayout(jPanel5Layout);
        jPanel5Layout.setHorizontalGroup(jPanel5Layout.createParallelGroup(GroupLayout.Alignment.LEADING).addGroup(jPanel5Layout.createSequentialGroup().addContainerGap().addGroup(jPanel5Layout.createParallelGroup(GroupLayout.Alignment.LEADING).addComponent(this.jScrollPane3, -1, 528, 32767).addComponent(this.objectsCB, 0, 528, 32767)).addContainerGap()));
        jPanel5Layout.setVerticalGroup(jPanel5Layout.createParallelGroup(GroupLayout.Alignment.LEADING).addGroup(jPanel5Layout.createSequentialGroup().addContainerGap().addComponent(this.objectsCB, -2, -1, -2).addPreferredGap(LayoutStyle.ComponentPlacement.RELATED).addComponent(this.jScrollPane3, -1, 139, 32767).addContainerGap()));
        this.jSplitPane2.setRightComponent(this.jPanel5);
        this.jSplitPane3.setDividerLocation(getPreferredSize().width / 4);
        this.cellObjectList.setModel(new AttrListModel());
        this.cellObjectList.setSelectionMode(0);
        this.cellObjectList.addListSelectionListener(new ListSelectionListener() {
            /* class edu.memphis.ccrg.alife.gui.ALifePanel.AnonymousClass4 */

            public void valueChanged(ListSelectionEvent evt) {
                ALifePanel.this.cellObjectListValueChanged(evt);
            }
        });
        this.jScrollPane1.setViewportView(this.cellObjectList);
        this.cellinfoTF.setEditable(false);
        this.cellinfoTF.setHorizontalAlignment(11);
        this.cellinfoTF.setAutoscrolls(false);
        GroupLayout jPanel1Layout = new GroupLayout(this.jPanel1);
        this.jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(jPanel1Layout.createParallelGroup(GroupLayout.Alignment.LEADING).addComponent(this.cellinfoTF, -1, 541, 32767).addGroup(jPanel1Layout.createParallelGroup(GroupLayout.Alignment.LEADING).addComponent(this.jScrollPane1, -1, 541, 32767)));
        jPanel1Layout.setVerticalGroup(jPanel1Layout.createParallelGroup(GroupLayout.Alignment.LEADING).addGroup(jPanel1Layout.createSequentialGroup().addComponent(this.cellinfoTF, -2, 31, -2).addContainerGap(28, 32767)).addGroup(jPanel1Layout.createParallelGroup(GroupLayout.Alignment.LEADING).addGroup(GroupLayout.Alignment.TRAILING, jPanel1Layout.createSequentialGroup().addGap(36, 36, 36).addComponent(this.jScrollPane1, -1, 23, 32767))));
        this.jScrollPane1.getAccessibleContext().setAccessibleParent(this.jSplitPane3);
        this.jSplitPane3.setRightComponent(this.jPanel1);
        this.cellimagePanel.setBackground(new Color(51, 255, 255));
        this.cellimagePanel.setMaximumSize(new Dimension(300, 300));
        this.cellimagePanel.setPreferredSize(new Dimension(100, 100));
        this.cellimagePanel.setRequestFocusEnabled(false);
        this.cellimagePanel.setVerifyInputWhenFocusTarget(false);
        this.cellimagePanel.addComponentListener(new ComponentAdapter() {
            /* class edu.memphis.ccrg.alife.gui.ALifePanel.AnonymousClass5 */

            public void componentResized(ComponentEvent evt) {
                ALifePanel.this.cellimagePanelComponentResized(evt);
            }
        });
        GroupLayout cellimagePanelLayout = new GroupLayout(this.cellimagePanel);
        this.cellimagePanel.setLayout(cellimagePanelLayout);
        cellimagePanelLayout.setHorizontalGroup(cellimagePanelLayout.createParallelGroup(GroupLayout.Alignment.LEADING).addGap(0, 0, 32767));
        cellimagePanelLayout.setVerticalGroup(cellimagePanelLayout.createParallelGroup(GroupLayout.Alignment.LEADING).addGap(0, 59, 32767));
        this.jSplitPane3.setLeftComponent(this.cellimagePanel);
        this.jSplitPane2.setTopComponent(this.jSplitPane3);
        this.jSplitPane3.getAccessibleContext().setAccessibleParent(this.jSplitPane2);
        this.jSplitPane1.setRightComponent(this.jSplitPane2);
        this.mainImagePanel.setBackground(new Color(204, 204, 204));
        this.mainImagePanel.setPreferredSize(new Dimension(200, 200));
        this.mainImagePanel.addMouseListener(new MouseAdapter() {
            /* class edu.memphis.ccrg.alife.gui.ALifePanel.AnonymousClass6 */

            public void mouseClicked(MouseEvent evt) {
                ALifePanel.this.mainImagePanelMouseClicked(evt);
            }
        });
        GroupLayout mainImagePanelLayout = new GroupLayout(this.mainImagePanel);
        this.mainImagePanel.setLayout(mainImagePanelLayout);
        mainImagePanelLayout.setHorizontalGroup(mainImagePanelLayout.createParallelGroup(GroupLayout.Alignment.LEADING).addGap(0, 200, 32767));
        mainImagePanelLayout.setVerticalGroup(mainImagePanelLayout.createParallelGroup(GroupLayout.Alignment.LEADING).addGap(0, 236, 32767));
        this.jScrollPane4.setViewportView(this.mainImagePanel);
        this.jSplitPane1.setLeftComponent(this.jScrollPane4);
        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING).addComponent(this.jToolBar1, -1, 600, 32767).addGroup(GroupLayout.Alignment.TRAILING, layout.createSequentialGroup().addContainerGap().addComponent(this.jSplitPane1, -1, 580, 32767).addGap(10, 10, 10)));
        layout.setVerticalGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING).addGroup(layout.createSequentialGroup().addComponent(this.jToolBar1, -2, 25, -2).addGap(7, 7, 7).addComponent(this.jSplitPane1, -1, 257, 32767).addContainerGap()));
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void refreshButtonActionPerformed(ActionEvent evt) {
        refresh();
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void scalingFactorSpinerStateChanged(ChangeEvent evt) {
        setScalingFactor(((double) ((Integer) this.scalingFactorSpiner.getValue()).intValue()) / 100.0d);
        refresh();
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void objectsCBActionPerformed(ActionEvent evt) {
        ALifeObject object = (ALifeObject) this.objectsCB.getSelectedItem();
        if (object != null) {
            ((AttributesTableModel)this.attributesTable.getModel()).setALifeObject(object);
        }
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void mainImagePanelMouseClicked(MouseEvent evt) {
        Point cellCoord = ((ImagePanel)this.mainImagePanel).getCellCoordinates(evt.getX(), evt.getY());
        this.currentSelectedcell = this.world.getCell(cellCoord.x, cellCoord.y);
        changeSelectedCell();
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void cellObjectListValueChanged(ListSelectionEvent evt) {
        if (!evt.getValueIsAdjusting() && this.cellObjectList.getSelectedIndex() != -1) {
            this.objectsCB.setSelectedItem(this.cellObjectList.getSelectedValue());
            this.cellObjectList.clearSelection();
        }
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void cellimagePanelComponentResized(ComponentEvent evt) {
        this.cellimagePanel.repaint();
    }

    public void refresh() {
        this.img = this.renderer.renderWorld(this.world);
        this.scaleImg = this.img;
        if (!(this.scalingFactor == 1.0d || this.img == null)) {
            this.scaleImg = this.img.getScaledInstance(this.scaleImgWidth, this.scaleImgHeight, 4);
        }
        this.mainImagePanel.revalidate();
        changeSelectedCell();
        ((AbstractTableModel)this.attributesTable.getModel()).fireTableDataChanged();
        repaint();
    }

    private void setScalingFactor(double scalingFactor2) {
        if (scalingFactor2 > 0.0d) {
            this.scalingFactor = scalingFactor2;
            this.scaleImgHeight = (int) (((double) this.img.getHeight()) * scalingFactor2);
            this.scaleImgWidth = (int) (((double) this.img.getWidth()) * scalingFactor2);
        }
    }

}
