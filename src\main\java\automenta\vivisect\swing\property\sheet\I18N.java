/**
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package automenta.vivisect.swing.property.sheet;

public interface I18N {

	String NOT_SET = "(not set)";
	String TRUE = "true";
	String FALSE = "false";
	String CHOOSE_FILE = "Choose File";
	String CHOOSE_COLOR = "Choose Color";
	String SELECT = "Select";
	String CANCEL = "Cancel";

}
