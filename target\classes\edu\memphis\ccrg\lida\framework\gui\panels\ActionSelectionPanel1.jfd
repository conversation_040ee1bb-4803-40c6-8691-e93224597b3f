JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {seq {space :0:0:x, comp jSplitPane1:::p:400:p, space :0:0:x}}"
			"$verticalGroup": "par l {seq {space :0:0:x, comp jSplitPane1:::p:299:p, space :0:1:x}}"
		} ) {
			name: "this"
			"preferredSize": new java.awt.Dimension( 800, 350 )
			"minimumSize": new java.awt.Dimension( 800, 350 )
			add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
				name: "jSplitPane1"
				"dividerLocation": 150
				"orientation": 0
				"preferredSize": new java.awt.Dimension( 550, 250 )
				add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
					name: "winnersPane"
					add( new FormComponent( "javax.swing.JTable" ) {
						name: "winnersTable"
						"minimumSize": new java.awt.Dimension( 300, 150 )
						auxiliary() {
							"JavaCodeGenerator.preInitCode": "${field}.setModel(new SelectedBehaviorsTableModel());"
						}
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "right"
				} )
				add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
					name: "coalitionsPane1"
					"minimumSize": new java.awt.Dimension( 300, 100 )
					add( new FormComponent( "javax.swing.JTable" ) {
						name: "behaviorsTable"
						"minimumSize": new java.awt.Dimension( 300, 100 )
						"preferredScrollableViewportSize": new java.awt.Dimension( 450, 300 )
						auxiliary() {
							"JavaCodeGenerator.preInitCode": "${field}.setModel(new BehaviorTableModel());"
						}
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "left"
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 400, 300 )
		} )
	}
}
