###############################################################################
# Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
# This program and the accompanying materials are made available 
# under the terms of the LIDA Software Framework Non-Commercial License v1.0 
# which accompanies this distribution, and is available at
# http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
###############################################################################

#used by predators
move=edu.memphis.ccrg.alife.opreations.MoveOperation
attack=edu.memphis.ccrg.lida.alifeagent.environment.operations.AttackOperation

#to sense environment state
seeobjects=edu.memphis.ccrg.lida.alifeagent.environment.operations.SeeObjectsInCell

#atomic actions agent can take in the alife world
turnLeft=edu.memphis.ccrg.lida.alifeagent.environment.operations.TurnLeftOperation
turnRight=edu.memphis.ccrg.lida.alifeagent.environment.operations.TurnRightOperation
turnAround=edu.memphis.ccrg.lida.alifeagent.environment.operations.TurnAroundOperation
moveAgent=edu.memphis.ccrg.lida.alifeagent.environment.operations.MoveAgentOperation

get=edu.memphis.ccrg.lida.alifeagent.environment.operations.GetFoodOperation

eat=edu.memphis.ccrg.lida.alifeagent.environment.operations.EatOperation

flee=edu.memphis.ccrg.lida.alifeagent.environment.operations.FleeOperation