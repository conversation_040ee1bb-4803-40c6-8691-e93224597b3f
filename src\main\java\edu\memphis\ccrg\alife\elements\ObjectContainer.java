package edu.memphis.ccrg.alife.elements;

import java.util.Set;

public interface ObjectContainer {
    boolean addObject(ALifeObject aLifeObject);

    void clearContainer();

    boolean containsObject(ALifeObject aLifeObject);

    int getCapacity();

    int getContainerOccupancy();

    int getObjectCount();

    Set<ALifeObject> getObjects();

    boolean isContainerFull();

    boolean isEmpty();

    void removeObject(ALifeObject aLifeObject);

    void setCapacity(int i);
}
