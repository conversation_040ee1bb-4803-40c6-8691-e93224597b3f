package edu.memphis.ccrg.alife.gui;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.elements.Cell;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import javax.swing.ImageIcon;

public class DefaultWorldRenderer implements ALifeWorldRenderer {
    private static final int CELL_SIZE = 64;
    private static final int CELL_ZOOM = 4;
    private ImageIcon defaultObjectIcon;
    private ImageIcon emptyCellIcon;
    private Map<Integer, ImageIcon> icons = new HashMap();
    private BufferedImage img;
    private ImageIcon multiObjectIcon;

    public DefaultWorldRenderer(int width, int height) {
        init(width, height);
    }

    public final void init(int width, int height) {
        this.img = new BufferedImage(width * CELL_SIZE, height * CELL_SIZE, 1);
        this.defaultObjectIcon = createImageIcon("/defaultimg/defObjImg.gif", "default object image");
        this.emptyCellIcon = createImageIcon("/defaultimg/emtyCellImg.gif", "empty cell");
        this.multiObjectIcon = createImageIcon("/defaultimg/multipleObjects.jpg", "multiple objects");
    }

    @Override // edu.memphis.ccrg.alife.gui.ALifeWorldRenderer
    public boolean addObjectIcon(int id, String objectName, String path) {
        ImageIcon icon = loadImageIcon(path, objectName);
        if (icon == null) {
            return false;
        }
        this.icons.put(Integer.valueOf(id), icon);
        return true;
    }

    @Override // edu.memphis.ccrg.alife.gui.ALifeWorldRenderer
    public BufferedImage renderWorld(ALifeWorld world) {
        Graphics2D graphics = this.img.createGraphics();
        graphics.setColor(Color.GRAY);
        graphics.setStroke(new BasicStroke(0.1f));
        ImageIcon imageIcon = this.emptyCellIcon;
        for (int i = 0; i < world.getWidth(); i++) {
            for (int j = 0; j < world.getHeight(); j++) {
                ImageIcon icon = this.emptyCellIcon;
                Cell cell = world.getCell(i, j);
                if (cell != null && !cell.isEmpty()) {
                    if (cell.getObjectCount() == 1) {
                        Iterator<ALifeObject> it = cell.getObjects().iterator();
                        if (it.hasNext()) {
                            icon = this.icons.get(Integer.valueOf(it.next().getIconId()));
                        }
                    } else {
                        icon = this.multiObjectIcon;
                    }
                }
                if (icon == null) {
                    icon = this.defaultObjectIcon;
                }
                graphics.drawImage(icon.getImage(), i * CELL_SIZE, j * CELL_SIZE, (ImageObserver) null);
                graphics.drawRect(i * CELL_SIZE, j * CELL_SIZE, (int) CELL_SIZE, (int) CELL_SIZE);
            }
        }
        return this.img;
    }

    private ImageIcon createImageIcon(String path, String description) {
        URL imgURL = getClass().getResource(path);
        if (imgURL != null) {
            return new ImageIcon(imgURL, description);
        }
        System.err.println("Couldn't find file: " + path);
        return null;
    }

    private ImageIcon loadImageIcon(String path, String description) {
        ImageIcon icon = null;
        if (path != null) {
            icon = new ImageIcon(path, description);
        } else {
            System.err.println("Couldn't find file: " + path);
        }
        if (icon == null) {
            System.err.println("Couldn't find file: " + path);
        }
        return icon;
    }

    @Override // edu.memphis.ccrg.alife.gui.ALifeWorldRenderer
    public Image renderCell(int x, int y, ALifeWorld world) {
        BufferedImage cimg = new BufferedImage(256, 256, 1);
        Graphics2D g = cimg.createGraphics();
        g.setColor(Color.white);
        g.fillRect(0, 0, cimg.getWidth(), cimg.getHeight());
        if (x >= 0 && y >= 0 && x < world.getWidth() && y < world.getHeight()) {
            int i = 0;
            int j = 0;
            for (ALifeObject object : world.getCell(x, y).getObjects()) {
                ImageIcon icon = this.icons.get(Integer.valueOf(object.getIconId()));
                if (icon == null) {
                    icon = this.defaultObjectIcon;
                }
                g.drawImage(icon.getImage(), i * CELL_SIZE, j * CELL_SIZE, (ImageObserver) null);
                i++;
                if (i >= CELL_ZOOM) {
                    i = 0;
                    j++;
                    if (j >= CELL_ZOOM) {
                        break;
                    }
                }
            }
        }
        return cimg;
    }
}
