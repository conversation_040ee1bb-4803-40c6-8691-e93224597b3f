package edu.memphis.ccrg.lida.framework.shared.scene;

import edu.memphis.ccrg.lida.framework.shared.NodeImpl;
import org.neo4j.graphdb.Node;

import java.io.Serializable;
import java.util.List;

public class Entity extends NodeImpl implements Serializable {
//    private String id;
//    private String name;
    private String status;

    private List<Node> nodeList;
    private SAttrs sAttrs;

//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = id;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Node> getNodeList() {
        return nodeList;
    }

    public void setNodeList(List<Node> nodeList) {
        this.nodeList = nodeList;
    }

    public SAttrs getsAttrs() {
        return sAttrs;
    }

    public void setsAttrs(SAttrs sAttrs) {
        this.sAttrs = sAttrs;
    }
}
