/*=====start tip-container style=====*/
#tip-container {
  display: flex;
  position: fixed;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 9999999;
  text-align: center;
}

#loading-tip {
  max-width: 600px;
  padding: 10px 12px;
  text-align: center;
  line-height: 32px;
  display: flex;
  justify-content: space-around;
  color: #fff;
  font-size: 16px;
  font-family: Georgia, 'Times New Roman', Times, serif;
  background-color: #222;
  opacity: 0.7;
  z-index: 99999;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;
  filter: progid: DXImageTransform.Microsoft.Alpha(opacity=70);
  border: 1px #666 solid;
}

#loading-tip > img {
  padding-right: 6px;
  width: 32px;
  height: 32px;
  box-sizing: initial;
  -webkit-box-sizing: initial;
  -moz-box-sizing: initial;
  user-select: none;
  -webkit-user-select: none;
}

#loading-tip .loading-text {
  padding: 0 10px;
  max-width: 100%;
  /* word-break: break-all; */
  max-height: 420px;
  text-overflow: ellipsis;
  overflow: hidden;
}

#loading-tip.has-type {
  padding-right: 20px;
  padding-left: 12px;
  min-width: 80px;
  text-align: left;
}

#loading-tip .loading-close {
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
}

#loading-tip.warn, #loading-tip.warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
  opacity: 1.0;
}

#loading-tip.danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
  opacity: 1.0;
}

#loading-tip.info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
  opacity: 1.0;
}

#loading-tip.success {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
  opacity: 1.0;
}

/*=====end tip-container style=====*/
#renderContainer {
  width: 100% !important;
  height: 100% !important;
  position: fixed;
  margin: 0;
  padding: 0;
  top: 0;
  left: 0;
}

#renderContainer {
  display: none;
}

.confirm-modal .modal-content {
  margin-top: 78px !important;
}

body {
  background-color: black;
}

.w-e-toolbar,.w-e-text-container,.w-e-menu-panel {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.w-e-toolbar *,.w-e-text-container *,.w-e-menu-panel * {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.w-e-clear-fix:after {
  content: "";
  display: table;
  clear: both;
}

.w-e-toolbar .w-e-droplist {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #fff;
  border: 1px solid #f1f1f1;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
}

.w-e-toolbar .w-e-droplist .w-e-dp-title {
  text-align: center;
  color: #999;
  line-height: 2;
  border-bottom: 1px solid #f1f1f1;
  font-size: 13px;
}

.w-e-toolbar .w-e-droplist ul.w-e-list {
  list-style: none;
  line-height: 1;
}

.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {
  color: #333;
  padding: 5px 0;
}

.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {
  background-color: #f1f1f1;
}

.w-e-toolbar .w-e-droplist ul.w-e-block {
  list-style: none;
  text-align: left;
  padding: 5px;
}

.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item {
  display: inline-block;
  *display: inline;
  *zoom: 1; padding: 3px 5px;
}

.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item:hover {
  background-color: #f1f1f1;
}

[class^="w-e-icon-"],[class*=" w-e-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'w-e-icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.w-e-icon-close:before {
  content: "\f00d";
}

.w-e-icon-upload2:before {
  content: "\e9c6";
}

.w-e-icon-trash-o:before {
  content: "\f014";
}

.w-e-icon-header:before {
  content: "\f1dc";
}

.w-e-icon-pencil2:before {
  content: "\e906";
}

.w-e-icon-paint-brush:before {
  content: "\f1fc";
}

.w-e-icon-image:before {
  content: "\e90d";
}

.w-e-icon-play:before {
  content: "\e912";
}

.w-e-icon-location:before {
  content: "\e947";
}

.w-e-icon-undo:before {
  content: "\e965";
}

.w-e-icon-redo:before {
  content: "\e966";
}

.w-e-icon-quotes-left:before {
  content: "\e977";
}

.w-e-icon-list-numbered:before {
  content: "\e9b9";
}

.w-e-icon-list2:before {
  content: "\e9bb";
}

.w-e-icon-link:before {
  content: "\e9cb";
}

.w-e-icon-happy:before {
  content: "\e9df";
}

.w-e-icon-bold:before {
  content: "\ea62";
}

.w-e-icon-underline:before {
  content: "\ea63";
}

.w-e-icon-italic:before {
  content: "\ea64";
}

.w-e-icon-strikethrough:before {
  content: "\ea65";
}

.w-e-icon-table2:before {
  content: "\ea71";
}

.w-e-icon-paragraph-left:before {
  content: "\ea77";
}

.w-e-icon-paragraph-center:before {
  content: "\ea78";
}

.w-e-icon-paragraph-right:before {
  content: "\ea79";
}

.w-e-icon-terminal:before {
  content: "\f120";
}

.w-e-icon-page-break:before {
  content: "\ea68";
}

.w-e-icon-cancel-circle:before {
  content: "\ea0d";
}

.w-e-icon-font:before {
  content: "\ea5c";
}

.w-e-icon-text-heigh:before {
  content: "\ea5f";
}

.w-e-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 5px;
  /* flex-wrap: wrap; */
  /* 单个菜单 */
}

.w-e-toolbar .w-e-menu {
  position: relative;
  text-align: center;
  padding: 5px 10px;
  cursor: pointer;
}

.w-e-toolbar .w-e-menu i {
  color: #999;
}

.w-e-toolbar .w-e-menu:hover i {
  color: #333;
}

.w-e-toolbar .w-e-active i {
  color: #1e88e5;
}

.w-e-toolbar .w-e-active:hover i {
  color: #1e88e5;
}

.w-e-text-container .w-e-panel-container {
  position: absolute;
  top: 0;
  left: 50%;
  border: 1px solid #ccc;
  border-top: 0;
  box-shadow: 1px 1px 2px #ccc;
  color: #333;
  background-color: #fff;
  /* 为 emotion panel 定制的样式 */
  /* 上传图片的 panel 定制样式 */
}

.w-e-text-container .w-e-panel-container .w-e-panel-close {
  position: absolute;
  right: 0;
  top: 0;
  padding: 5px;
  margin: 2px 5px 0 0;
  cursor: pointer;
  color: #999;
}

.w-e-text-container .w-e-panel-container .w-e-panel-close:hover {
  color: #333;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-title {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  margin: 2px 10px 0 10px;
  border-bottom: 1px solid #f1f1f1;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-item {
  padding: 3px 5px;
  color: #999;
  cursor: pointer;
  margin: 0 3px;
  position: relative;
  top: 1px;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-active {
  color: #333;
  border-bottom: 1px solid #333;
  cursor: default;
  font-weight: 700;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content {
  padding: 10px 15px 10px 15px;
  font-size: 16px;
  /* 输入框的样式 */
  /* 按钮的样式 */
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content button:focus {
  outline: none;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea {
  width: 100%;
  border: 1px solid #ccc;
  padding: 5px;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus {
  border-color: #1e88e5;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text] {
  border: none;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
  height: 20px;
  color: #333;
  text-align: left;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].small {
  width: 30px;
  text-align: center;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].block {
  display: block;
  width: 100%;
  margin: 10px 0;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text]:focus {
  border-bottom: 2px solid #1e88e5;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {
  font-size: 14px;
  color: #1e88e5;
  border: none;
  padding: 5px 10px;
  background-color: #fff;
  cursor: pointer;
  border-radius: 3px;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.left {
  float: left;
  margin-right: 10px;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.right {
  float: right;
  margin-left: 10px;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.gray {
  color: #999;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.red {
  color: #c24f4a;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {
  background-color: #f1f1f1;
}

.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container:after {
  content: "";
  display: table;
  clear: both;
}

.w-e-text-container .w-e-panel-container .w-e-emoticon-container .w-e-item {
  cursor: pointer;
  font-size: 18px;
  padding: 0 3px;
  display: inline-block;
  *display: inline;
  *zoom: 1;}

.w-e-text-container .w-e-panel-container .w-e-up-img-container {
  text-align: center;
}

.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn {
  display: inline-block;
  *display: inline;
  *zoom: 1; color: #999;
  cursor: pointer;
  font-size: 60px;
  line-height: 1;
}

.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn:hover {
  color: #333;
}

.w-e-text-container {
  position: relative;
}

.w-e-text-container .w-e-progress {
  position: absolute;
  background-color: #1e88e5;
  bottom: 0;
  left: 0;
  height: 1px;
}

.w-e-text {
  padding: 0 10px;
  overflow-y: scroll;
}

.w-e-text p,.w-e-text h1,.w-e-text h2,.w-e-text h3,.w-e-text h4,.w-e-text h5,.w-e-text table,.w-e-text pre {
  margin: 10px 0;
  line-height: 1.5;
}

.w-e-text ul,.w-e-text ol {
  margin: 10px 0 10px 20px;
}

.w-e-text blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}

.w-e-text code {
  display: inline-block;
  *display: inline;
  *zoom: 1; background-color: #f1f1f1;
  border-radius: 3px;
  padding: 3px 5px;
  margin: 0 3px;
}

.w-e-text pre code {
  display: block;
}

.w-e-text table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}

.w-e-text table td,.w-e-text table th {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
}

.w-e-text table th {
  border-bottom: 2px solid #ccc;
  text-align: center;
}

.w-e-text:focus {
  outline: none;
}

.w-e-text img {
  cursor: pointer;
}

.w-e-text img:hover {
  box-shadow: 0 0 5px #333;
}

.react-grid-Header {
  box-shadow: 0 0 4px 0 #ddd;
  background: #f9f9f9
}

.react-grid-Header--resizing {
  cursor: ew-resize
}

.react-grid-HeaderCell,.react-grid-HeaderRow {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.react-grid-HeaderCell {
  background: #f9f9f9;
  padding: 8px;
  font-weight: 700;
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd
}

.react-grid-HeaderCell__value {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  position: relative;
  top: 50%;
  transform: translateY(-50%)
}

.react-grid-HeaderCell__resizeHandle:hover {
  cursor: ew-resize;
  background: #ddd
}

.react-grid-HeaderCell--frozen:last-of-type {
  box-shadow: 2px 0 5px -2px hsla(0,0%,53%,.3)
}

.react-grid-HeaderCell--resizing .react-grid-HeaderCell__resizeHandle {
  background: #ddd
}

.react-grid-HeaderCell__draggable {
  cursor: col-resize
}

.rdg-can-drop>.react-grid-HeaderCell {
  background: #ececec
}

.react-grid-HeaderCell .Select {
  max-height: 30px;
  font-size: 12px;
  font-weight: 400
}

.react-grid-HeaderCell .Select-control {
  max-height: 30px;
  border: 1px solid #ccc;
  color: #555;
  border-radius: 3px
}

.react-grid-HeaderCell .is-focused:not(.is-open)>.Select-control {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)
}

.react-grid-HeaderCell .Select-control .Select-placeholder {
  line-height: 20px;
  color: #999;
  padding: 4px
}

.react-grid-HeaderCell .Select-control .Select-input {
  max-height: 28px;
  padding: 4px;
  margin-left: 0
}

.react-grid-HeaderCell .Select-control .Select-input input {
  padding: 0;
  height: 100%
}

.react-grid-HeaderCell .Select-control .Select-arrow-zone .Select-arrow {
  border-color: gray transparent transparent;
  border-width: 4px 4px 2.5px
}

.react-grid-HeaderCell .Select-control .Select-value {
  padding: 4px;
  line-height: 20px!important
}

.react-grid-HeaderCell .Select--multi .Select-control .Select-value {
  padding: 0;
  line-height: 16px!important;
  max-height: 20px
}

.react-grid-HeaderCell .Select--multi .Select-control .Select-value .Select-value-icon,.react-grid-HeaderCell .Select--multi .Select-control .Select-value .Select-value-label {
  max-height: 20px
}

.react-grid-HeaderCell .Select-control .Select-value .Select-value-label {
  color: #555!important
}

.react-grid-HeaderCell .Select-menu-outer {
  z-index: 2
}

.react-grid-HeaderCell .Select-menu-outer .Select-option {
  padding: 4px;
  line-height: 20px
}

.react-grid-HeaderCell .Select-menu-outer .Select-menu .Select-option.is-focused,.react-grid-HeaderCell .Select-menu-outer .Select-menu .Select-option.is-selected {
  color: #555
}

.react-grid-Cell {
  background-color: #fff;
  padding-left: 8px;
  padding-right: 8px;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #ddd
}

.rdg-selected {
  border: 2px solid #66afe9
}

.rdg-selected-range {
  border: 1px solid #66afe9;
  background-color: #66afe930
}

.moving-element {
  will-change: transform
}

.react-grid-Cell--frozen,.react-grid-Cell--frozen:focus {
  z-index: 12
}

.rdg-last--frozen {
  border-right: 1px solid #ddd;
  box-shadow: 2px 0 5px -2px hsla(0,0%,53%,.3)!important
}

.react-contextmenu--visible {
  z-index: 1000
}

.react-grid-Cell:not(.editing) .react-grid-Cell__value {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  height: inherit
}

.react-grid-Cell.readonly {
  background-color: #000
}

.react-grid-Cell:hover {
  background: #eee
}

.react-grid-cell .form-control-feedback {
  color: #a94442;
  position: absolute;
  top: 0;
  right: 10px;
  z-index: 1000000;
  display: block;
  width: 34px;
  height: 34px
}

.react-grid-Row.row-selected .react-grid-Cell {
  background-color: #dbecfa
}

.react-grid-Cell.editing {
  padding: 0;
  overflow: visible!important
}

.react-grid-Cell--frozen.editing {
  z-index: 100
}

.react-grid-Cell.editing .has-error input {
  border: 2px solid red!important;
  border-radius: 2px!important
}

.react-grid-Cell__value ul {
  margin-top: 0;
  margin-bottom: 0;
  display: inline-block
}

.react-grid-Cell__value .btn-sm {
  padding: 0
}

.cell-tooltip {
  position: relative;
  display: inline-block
}

.cell-tooltip:hover {
  z-index: 101
}

.cell-tooltip .cell-tooltip-text {
  visibility: hidden;
  width: 150px;
  background-color: #000;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  bottom: -150%;
  left: 50%;
  margin-left: -60px;
  opacity: 1s
}

.cell-tooltip:hover .cell-tooltip-text {
  visibility: visible;
  opacity: .8
}

.cell-tooltip .cell-tooltip-text:after {
  content: " ";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent #000
}

.react-grid-Canvas.opaque .react-grid-Cell.cell-tooltip:hover .cell-tooltip-text {
  visibility: hidden
}

.rdg-cell-expand {
  float: right;
  display: table;
  height: 100%
}

.rdg-cell-expand>span {
  display: table-cell;
  vertical-align: middle;
  cursor: pointer
}

.rdg-child-row-action-cross-last:before,.rdg-child-row-action-cross:before,rdg-child-row-action-cross-last:after,rdg-child-row-action-cross:after {
  content: "";
  position: absolute;
  background: grey;
  height: 50%
}

.rdg-child-row-action-cross:before {
  left: 21px;
  width: 1px;
  height: 100%
}

.rdg-child-row-action-cross-last:before {
  left: 21px;
  width: 1px
}

.rdg-child-row-action-cross-last:after,.rdg-child-row-action-cross:after {
  top: 50%;
  left: 20px;
  height: 1px;
  width: 15px;
  content: "";
  position: absolute;
  background: grey
}

.rdg-child-row-action-cross:hover {
  background: red
}

.rdg-child-row-btn {
  position: absolute;
  cursor: pointer;
  border: 1px solid grey;
  border-radius: 14px;
  z-index: 3;
  background: #fff
}

.rdg-child-row-btn div {
  font-size: 12px;
  text-align: center;
  line-height: 19px;
  color: grey;
  height: 20px;
  width: 20px;
  position: absolute;
  top: 60%;
  left: 53%;
  margin-top: -10px;
  margin-left: -10px
}

.rdg-empty-child-row:hover .glyphicon-plus-sign,.rdg-empty-child-row:hover a {
  color: green
}

.rdg-child-row-btn .glyphicon-remove-sign:hover {
  color: red
}

.last-column .cell-tooltip-text {
  right: 100%;
  left: 0!important
}

.rdg-cell-action {
  float: right;
  height: 100%
}

.rdg-cell-action-last {
  margin-right: -8px
}

.rdg-cell-action-button {
  width: 35px;
  height: 100%;
  text-align: center;
  position: relative;
  display: table;
  color: #4a9de2
}

.rdg-cell-action-button>span {
  display: table-cell;
  vertical-align: middle
}

.rdg-cell-action-button-toggled,.rdg-cell-action-button:hover {
  color: #447bbb
}

.rdg-cell-action-menu {
  position: absolute;
  top: 100%;
  z-index: 1000;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  box-shadow: 0 0 3px 0 #ccc
}

.rdg-cell-action-menu>span {
  display: block;
  padding: 3px 10px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap
}

.rdg-cell-action-menu>span:hover {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5
}

.react-grid-Row.row-context-menu .react-grid-Cell,.react-grid-Row:hover .react-grid-Cell {
  background-color: #f9f9f9
}

.react-grid-Row:hover .rdg-row-index {
  display: none
}

.react-grid-Row:hover .rdg-actions-checkbox {
  display: block
}

.react-grid-Row:hover .rdg-drag-row-handle {
  cursor: move;
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;
  width: 12px;
  height: 30px;
  margin-left: 0;
  background-repeat: no-repeat
}

.react-grid-Row.row-selected,.react-grid-Row .row-selected {
  background-color: #dbecfa
}

.react-grid-row-group .row-expand-icon:hover {
  color: #777
}

.react-grid-row-index {
  padding: 0 18px
}

.rdg-row-index {
  display: block;
  text-align: center
}

.rdg-row-actions-cell {
  padding: 0
}

.rdg-actions-checkbox {
  display: none;
  text-align: center
}

.rdg-actions-checkbox.selected {
  display: block
}

.rdg-dragging {
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing
}

.rdg-dragged-row {
  border-bottom: 1px solid #000
}

.rdg-scrolling {
  pointer-events: none
}

.react-grid-Container {
  clear: both;
  margin-top: 0;
  padding: 0
}

.react-grid-Main {
  background-color: #fff;
  color: inherit;
  padding: 0;
  outline: 1px solid #e7eaec;
  clear: both
}

.react-grid-Grid {
  border: 1px solid #ddd;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.react-grid-Canvas,.react-grid-Grid {
  background-color: #fff
}

.rdg-selected {
  border: 2px solid #66afe9
}

.rdg-selected .drag-handle {
  pointer-events: auto;
  position: absolute;
  bottom: -5px;
  right: -4px;
  background: #66afe9;
  width: 8px;
  height: 8px;
  border: 1px solid #fff;
  border-right: 0;
  border-bottom: 0;
  z-index: 8;
  cursor: crosshair;
  cursor: -moz-grab;
  cursor: -webkit-grab;
  cursor: grab
}

.rdg-selected:hover .drag-handle {
  bottom: -8px;
  right: -7px;
  background: #fff;
  width: 16px;
  height: 16px;
  border: 1px solid #66afe9
}

.rdg-selected:hover .drag-handle .glyphicon-arrow-down {
  display: "block"
}

.react-grid-cell-dragged-over-down,.react-grid-cell-dragged-over-up {
  border: 1px dashed #000;
  background: rgba(0,0,255,.2)!important
}

.react-grid-cell-dragged-over-up {
  border-bottom-width: 0
}

.react-grid-cell-dragged-over-down {
  border-top-width: 0
}

.react-grid-cell-copied {
  background: rgba(0,0,255,.2)!important
}

.rdg-editor-container input.editor-main,select.editor-main {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

input.editor-main:focus,select.editor-main:focus {
  border-color: #66afe9;
  border: 2px solid #66afe9;
  background: #eee;
  border-radius: 4px
}

.rdg-editor-container input.editor-main::-moz-placeholder,select.editor-main::-moz-placeholder {
  color: #999;
  opacity: 1
}

.rdg-editor-container input.editor-main:-ms-input-placeholder,select.editor-main:-ms-input-placeholder {
  color: #999
}

.rdg-editor-container input.editor-main::-webkit-input-placeholder,select.editor-main::-webkit-input-placeholder {
  color: #999
}

.rdg-editor-container input.editor-main[disabled],.rdg-editor-container input.editor-main[readonly],fieldset[disabled] .rdg-editor-container input.editor-main,fieldset[disabled] select.editor-main,select.editor-main[disabled],select.editor-main[readonly] {
  cursor: not-allowed;
  background-color: #eee;
  opacity: 1
}

textarea.rdg-editor-container input.editor-main,textareaselect.editor-main {
  height: auto
}

.radio-custom,.react-grid-checkbox {
  opacity: 0;
  position: absolute
}

.radio-custom,.radio-custom-label,.react-grid-checkbox,.react-grid-checkbox-label {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer
}

.radio-custom-label,.react-grid-checkbox-label {
  position: relative
}

.radio-custom+.radio-custom-label:before,.react-grid-checkbox+.react-grid-checkbox-label:before {
  content: "";
  background: #fff;
  border: 2px solid #ddd;
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  text-align: center
}

.react-grid-checkbox:checked+.react-grid-checkbox-label:before {
  background: #005295;
  box-shadow: inset 0 0 0 4px #fff
}

.radio-custom:focus+.radio-custom-label,.react-grid-checkbox:focus+.react-grid-checkbox-label {
  outline: 1px solid #ddd
}

.react-grid-HeaderCell input[type=checkbox] {
  z-index: 99999
}

.react-grid-HeaderCell>.react-grid-checkbox-container {
  padding: 0 10px;
  height: 100%
}

.react-grid-HeaderCell>.react-grid-checkbox-container>.react-grid-checkbox-label {
  margin: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%)
}

.radio-custom+.radio-custom-label:before {
  border-radius: 50%
}

.radio-custom:checked+.radio-custom-label:before {
  background: #ccc;
  box-shadow: inset 0 0 0 4px #fff
}

.checkbox-align {
  text-align: center
}

.radio-custom,.react-grid-checkbox {
  opacity: 0;
  position: absolute
}

.radio-custom,.radio-custom-label,.react-grid-checkbox,.react-grid-checkbox-label {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer
}

.radio-custom-label,.react-grid-checkbox-label {
  position: relative
}

.radio-custom+.radio-custom-label:before,.react-grid-checkbox+.react-grid-checkbox-label:before {
  content: "";
  background: #fff;
  border: 2px solid #ddd;
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  text-align: center
}

.react-grid-checkbox:checked+.react-grid-checkbox-label:before {
  background: #005295;
  box-shadow: inset 0 0 0 4px #fff
}

.radio-custom:focus+.radio-custom-label,.react-grid-checkbox:focus+.react-grid-checkbox-label {
  outline: 1px solid #ddd
}

.react-grid-HeaderCell input[type=checkbox] {
  z-index: 99999
}

.react-grid-HeaderCell>.react-grid-checkbox-container {
  padding: 0 10px;
  height: 100%
}

.react-grid-HeaderCell>.react-grid-checkbox-container>.react-grid-checkbox-label {
  margin: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%)
}

.radio-custom+.radio-custom-label:before {
  border-radius: 50%
}

.radio-custom:checked+.radio-custom-label:before {
  background: #ccc;
  box-shadow: inset 0 0 0 4px #fff
}

.checkbox-align {
  text-align: center
}

.react-autocomplete-Autocomplete__search {
  display: block;
  width: 100%;
  height: 36px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

.react-autocomplete-Autocomplete__search:focus {
  border-color: #a21618;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(162,22,24,.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(162,22,24,.6)
}

.react-autocomplete-Autocomplete__search::-moz-placeholder {
  color: #777;
  opacity: 1
}

.react-autocomplete-Autocomplete__search:-ms-input-placeholder {
  color: #777
}

.react-autocomplete-Autocomplete__search::-webkit-input-placeholder {
  color: #777
}

.react-autocomplete-Autocomplete__search[disabled],.react-autocomplete-Autocomplete__search[readonly],fieldset[disabled] .react-autocomplete-Autocomplete__search {
  cursor: not-allowed;
  background-color: #eee;
  opacity: 1
}

textarea.react-autocomplete-Autocomplete__search {
  height: auto
}

.react-autocomplete-Autocomplete__results {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  text-align: left;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0,0,0,.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
  box-shadow: 0 6px 12px rgba(0,0,0,.175);
  background-clip: padding-box;
  min-width: 250px;
  width: 100%;
  max-height: 200px;
  overflow: auto
}

.react-autocomplete-Autocomplete__results.pull-right {
  right: 0;
  left: auto
}

.react-autocomplete-Autocomplete__results .divider {
  height: 1px;
  margin: 10px 0;
  overflow: hidden;
  background-color: #e5e5e5
}

.react-autocomplete-Autocomplete__results>li>a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.6;
  color: #333;
  white-space: nowrap
}

.react-autocomplete-Autocomplete__results div.action-button {
  display: block!important;
  padding: 4px
}

.react-autocomplete-Result {
  cursor: pointer
}

.react-autocomplete-Result>a {
  text-decoration: none
}

.react-autocomplete-Result--active {
  color: #262626;
  background-color: #f5f5f5
}

.react-grid-image {
  background: #efefef;
  background-size: 100%;
  display: inline-block;
  height: 40px;
  width: 40px
}

.react-grid-Toolbar {
  background-color: #fff;
  border-color: #e7eaec;
  border-image: none;
  border-style: solid solid none;
  border-width: 1px;
  color: inherit;
  margin-bottom: 0;
  padding: 14px 15px 7px;
  height: 48px
}

.react-grid-Toolbar .btn {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  color: inherit;
  background: #fff;
  border: 1px solid #e7eaec
}

.react-grid-Toolbar .btn:hover {
  color: inherit;
  border: 1px solid #d2d2d2
}

.react-grid-Toolbar .grouped-col-btn {
  background-color: #428bca;
  color: #fff;
  border-color: #2b669a
}

.react-grid-Toolbar .grouped-col-btn:hover {
  color: #fff;
  border-color: #2b669a
}

.react-grid-Toolbar .grouped-col-btn+.grouped-col-btn,.react-grid-Toolbar .grouped-col-btn__remove {
  margin-left: 5px
}

.react-grid-Toolbar .tools {
  display: inline-block;
  float: right;
  margin-top: 0;
  position: relative;
  padding: 0;
  margin-top: -6px
}

.react-grid-Cell {
  background-color: #fff;
  padding-left: 8px;
  padding-right: 8px;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #ddd
}

.rdg-selected {
  border: 2px solid #66afe9
}

.rdg-selected-range {
  border: 1px solid #66afe9;
  background-color: #66afe930
}

.moving-element {
  will-change: transform
}

.react-grid-Cell--frozen,.react-grid-Cell--frozen:focus {
  z-index: 12
}

.rdg-last--frozen {
  border-right: 1px solid #ddd;
  box-shadow: 2px 0 5px -2px hsla(0,0%,53%,.3)!important
}

.react-contextmenu--visible {
  z-index: 1000
}

.react-grid-Cell:not(.editing) .react-grid-Cell__value {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  height: inherit
}

.react-grid-Cell.readonly {
  background-color: #000
}

.react-grid-Cell:hover {
  background: #eee
}

.react-grid-cell .form-control-feedback {
  color: #a94442;
  position: absolute;
  top: 0;
  right: 10px;
  z-index: 1000000;
  display: block;
  width: 34px;
  height: 34px
}

.react-grid-Row.row-selected .react-grid-Cell {
  background-color: #dbecfa
}

.react-grid-Cell.editing {
  padding: 0;
  overflow: visible!important
}

.react-grid-Cell--frozen.editing {
  z-index: 100
}

.react-grid-Cell.editing .has-error input {
  border: 2px solid red!important;
  border-radius: 2px!important
}

.react-grid-Cell__value ul {
  margin-top: 0;
  margin-bottom: 0;
  display: inline-block
}

.react-grid-Cell__value .btn-sm {
  padding: 0
}

.cell-tooltip {
  position: relative;
  display: inline-block
}

.cell-tooltip:hover {
  z-index: 101
}

.cell-tooltip .cell-tooltip-text {
  visibility: hidden;
  width: 150px;
  background-color: #000;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  bottom: -150%;
  left: 50%;
  margin-left: -60px;
  opacity: 1s
}

.cell-tooltip:hover .cell-tooltip-text {
  visibility: visible;
  opacity: .8
}

.cell-tooltip .cell-tooltip-text:after {
  content: " ";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent #000
}

.react-grid-Canvas.opaque .react-grid-Cell.cell-tooltip:hover .cell-tooltip-text {
  visibility: hidden
}

.rdg-cell-expand {
  float: right;
  display: table;
  height: 100%
}

.rdg-cell-expand>span {
  display: table-cell;
  vertical-align: middle;
  cursor: pointer
}

.rdg-child-row-action-cross-last:before,.rdg-child-row-action-cross:before,rdg-child-row-action-cross-last:after,rdg-child-row-action-cross:after {
  content: "";
  position: absolute;
  background: grey;
  height: 50%
}

.rdg-child-row-action-cross:before {
  left: 21px;
  width: 1px;
  height: 100%
}

.rdg-child-row-action-cross-last:before {
  left: 21px;
  width: 1px
}

.rdg-child-row-action-cross-last:after,.rdg-child-row-action-cross:after {
  top: 50%;
  left: 20px;
  height: 1px;
  width: 15px;
  content: "";
  position: absolute;
  background: grey
}

.rdg-child-row-action-cross:hover {
  background: red
}

.rdg-child-row-btn {
  position: absolute;
  cursor: pointer;
  border: 1px solid grey;
  border-radius: 14px;
  z-index: 3;
  background: #fff
}

.rdg-child-row-btn div {
  font-size: 12px;
  text-align: center;
  line-height: 19px;
  color: grey;
  height: 20px;
  width: 20px;
  position: absolute;
  top: 60%;
  left: 53%;
  margin-top: -10px;
  margin-left: -10px
}

.rdg-empty-child-row:hover .glyphicon-plus-sign,.rdg-empty-child-row:hover a {
  color: green
}

.rdg-child-row-btn .glyphicon-remove-sign:hover {
  color: red
}

.last-column .cell-tooltip-text {
  right: 100%;
  left: 0!important
}

.rdg-cell-action {
  float: right;
  height: 100%
}

.rdg-cell-action-last {
  margin-right: -8px
}

.rdg-cell-action-button {
  width: 35px;
  height: 100%;
  text-align: center;
  position: relative;
  display: table;
  color: #4a9de2
}

.rdg-cell-action-button>span {
  display: table-cell;
  vertical-align: middle
}

.rdg-cell-action-button-toggled,.rdg-cell-action-button:hover {
  color: #447bbb
}

.rdg-cell-action-menu {
  position: absolute;
  top: 100%;
  z-index: 1000;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  box-shadow: 0 0 3px 0 #ccc
}

.rdg-cell-action-menu>span {
  display: block;
  padding: 3px 10px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap
}

.rdg-cell-action-menu>span:hover {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5
}

.react-grid-Row.row-context-menu .react-grid-Cell,.react-grid-Row:hover .react-grid-Cell {
  background-color: #f9f9f9
}

.react-grid-Row:hover .rdg-row-index {
  display: none
}

.react-grid-Row:hover .rdg-actions-checkbox {
  display: block
}

.react-grid-Row:hover .rdg-drag-row-handle {
  cursor: move;
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;
  width: 12px;
  height: 30px;
  margin-left: 0;
  background-image: url("data:image/svg+xml;base64, 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");
  background-repeat: no-repeat
}

.react-grid-Row.row-selected,.react-grid-Row .row-selected {
  background-color: #dbecfa
}

.react-grid-row-group .row-expand-icon:hover {
  color: #777
}

.react-grid-row-index {
  padding: 0 18px
}

.rdg-row-index {
  display: block;
  text-align: center
}

.rdg-row-actions-cell {
  padding: 0
}

.rdg-actions-checkbox {
  display: none;
  text-align: center
}

.rdg-actions-checkbox.selected {
  display: block
}

.rdg-dragging {
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing
}

.rdg-dragged-row {
  border-bottom: 1px solid #000
}

.rdg-scrolling {
  pointer-events: none
}

.slideUp {
  -webkit-animation-name: slideUp;
  animation-name: slideUp;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  visibility: visible!important
}

@keyframes slideUp {
  0% {
      transform: translateY(100%)
  }

  50% {
      transform: translateY(-8%)
  }

  65% {
      transform: translateY(4%)
  }

  80% {
      transform: translateY(-4%)
  }

  95% {
      transform: translateY(2%)
  }

  to {
      transform: translateY(0)
  }
}

@-webkit-keyframes slideUp {
  0% {
      -webkit-transform: translateY(100%)
  }

  50% {
      -webkit-transform: translateY(-8%)
  }

  65% {
      -webkit-transform: translateY(4%)
  }

  80% {
      -webkit-transform: translateY(-4%)
  }

  95% {
      -webkit-transform: translateY(2%)
  }

  to {
      -webkit-transform: translateY(0)
  }
}

.rowDropTarget {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 1;
  border-bottom: 1px solid #000
}

.Select {
  position: relative
}

.Select input::-webkit-contacts-auto-fill-button,.Select input::-webkit-credentials-auto-fill-button {
  display: none!important
}

.Select input::-ms-clear,.Select input::-ms-reveal {
  display: none!important
}

.Select,.Select div,.Select input,.Select span {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box
}

.Select.is-disabled .Select-arrow-zone {
  cursor: default;
  pointer-events: none;
  opacity: .35
}

.Select.is-disabled>.Select-control {
  background-color: #f9f9f9
}

.Select.is-disabled>.Select-control:hover {
  box-shadow: none
}

.Select.is-open>.Select-control {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  background: #fff;
  border-color: #b3b3b3 #ccc #d9d9d9
}

.Select.is-open>.Select-control .Select-arrow {
  top: -2px;
  border-color: transparent transparent #999;
  border-width: 0 5px 5px
}

.Select.is-searchable.is-focused:not(.is-open)>.Select-control,.Select.is-searchable.is-open>.Select-control {
  cursor: text
}

.Select.is-focused>.Select-control {
  background: #fff
}

.Select.is-focused:not(.is-open)>.Select-control {
  border-color: #007eff;
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 0 3px rgba(0,126,255,.1);
  background: #fff
}

.Select.has-value.is-clearable.Select--single>.Select-control .Select-value {
  padding-right: 42px
}

.Select.has-value.is-pseudo-focused.Select--single>.Select-control .Select-value .Select-value-label,.Select.has-value.Select--single>.Select-control .Select-value .Select-value-label {
  color: #333
}

.Select.has-value.is-pseudo-focused.Select--single>.Select-control .Select-value a.Select-value-label,.Select.has-value.Select--single>.Select-control .Select-value a.Select-value-label {
  cursor: pointer;
  text-decoration: none
}

.Select.has-value.is-pseudo-focused.Select--single>.Select-control .Select-value a.Select-value-label:focus,.Select.has-value.is-pseudo-focused.Select--single>.Select-control .Select-value a.Select-value-label:hover,.Select.has-value.Select--single>.Select-control .Select-value a.Select-value-label:focus,.Select.has-value.Select--single>.Select-control .Select-value a.Select-value-label:hover {
  color: #007eff;
  outline: none;
  text-decoration: underline
}

.Select.has-value.is-pseudo-focused.Select--single>.Select-control .Select-value a.Select-value-label:focus,.Select.has-value.Select--single>.Select-control .Select-value a.Select-value-label:focus {
  background: #fff
}

.Select.has-value.is-pseudo-focused .Select-input {
  opacity: 0
}

.Select.is-open .Select-arrow,.Select .Select-arrow-zone:hover>.Select-arrow {
  border-top-color: #666
}

.Select.Select--rtl {
  direction: rtl;
  text-align: right
}

.Select-control {
  background-color: #fff;
  border-color: #d9d9d9 #ccc #b3b3b3;
  border-radius: 4px;
  border: 1px solid #ccc;
  color: #333;
  cursor: default;
  display: table;
  border-spacing: 0;
  border-collapse: separate;
  height: 36px;
  outline: none;
  overflow: hidden;
  position: relative;
  width: 100%
}

.Select-control:hover {
  box-shadow: 0 1px 0 rgba(0,0,0,.06)
}

.Select-control .Select-input:focus {
  outline: none;
  background: #fff
}

.Select--single>.Select-control .Select-value,.Select-placeholder {
  bottom: 0;
  color: #aaa;
  left: 0;
  line-height: 34px;
  padding-left: 10px;
  padding-right: 10px;
  position: absolute;
  right: 0;
  top: 0;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.Select-input {
  height: 34px;
  padding-left: 10px;
  padding-right: 10px;
  vertical-align: middle
}

.Select-input>input {
  width: 100%;
  background: none transparent;
  border: 0 none;
  box-shadow: none;
  cursor: default;
  display: inline-block;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  outline: none;
  line-height: 17px;
  padding: 8px 0 12px;
  -webkit-appearance: none
}

.is-focused .Select-input>input {
  cursor: text
}

.has-value.is-pseudo-focused .Select-input {
  opacity: 0
}

.Select-control:not(.is-searchable)>.Select-input {
  outline: none
}

.Select-loading-zone {
  cursor: pointer;
  display: table-cell;
  text-align: center
}

.Select-loading,.Select-loading-zone {
  position: relative;
  vertical-align: middle;
  width: 16px
}

.Select-loading {
  -webkit-animation: Select-animation-spin .4s infinite linear;
  -o-animation: Select-animation-spin .4s infinite linear;
  animation: Select-animation-spin .4s infinite linear;
  height: 16px;
  box-sizing: border-box;
  border-radius: 50%;
  border: 2px solid #ccc;
  border-right-color: #333;
  display: inline-block
}

.Select-clear-zone {
  -webkit-animation: Select-animation-fadeIn .2s;
  -o-animation: Select-animation-fadeIn .2s;
  animation: Select-animation-fadeIn .2s;
  color: #999;
  cursor: pointer;
  display: table-cell;
  position: relative;
  text-align: center;
  vertical-align: middle;
  width: 17px
}

.Select-clear-zone:hover {
  color: #d0021b
}

.Select-clear {
  display: inline-block;
  font-size: 18px;
  line-height: 1
}

.Select--multi .Select-clear-zone {
  width: 17px
}

.Select-arrow-zone {
  cursor: pointer;
  display: table-cell;
  position: relative;
  text-align: center;
  vertical-align: middle;
  width: 25px;
  padding-right: 5px
}

.Select--rtl .Select-arrow-zone {
  padding-right: 0;
  padding-left: 5px
}

.Select-arrow {
  border-color: #999 transparent transparent;
  border-style: solid;
  border-width: 5px 5px 2.5px;
  display: inline-block;
  height: 0;
  width: 0;
  position: relative
}

.Select-control>:last-child {
  padding-right: 5px
}

.Select--multi .Select-multi-value-wrapper {
  display: inline-block
}

.Select .Select-aria-only {
  position: absolute;
  display: inline-block;
  height: 1px;
  width: 1px;
  margin: -1px;
  clip: rect(0,0,0,0);
  overflow: hidden;
  float: left
}

@-webkit-keyframes Select-animation-fadeIn {
  0% {
      opacity: 0
  }

  to {
      opacity: 1
  }
}

@keyframes Select-animation-fadeIn {
  0% {
      opacity: 0
  }

  to {
      opacity: 1
  }
}

.Select-menu-outer {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top-color: #e6e6e6;
  box-shadow: 0 1px 0 rgba(0,0,0,.06);
  box-sizing: border-box;
  margin-top: -1px;
  max-height: 200px;
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  z-index: 1;
  -webkit-overflow-scrolling: touch
}

.Select-menu {
  max-height: 198px;
  overflow-y: auto
}

.Select-option {
  box-sizing: border-box;
  background-color: #fff;
  color: #666;
  cursor: pointer;
  display: block;
  padding: 8px 10px
}

.Select-option:last-child {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px
}

.Select-option.is-selected {
  background-color: #f5faff;
  background-color: rgba(0,126,255,.04);
  color: #333
}

.Select-option.is-focused {
  background-color: #ebf5ff;
  background-color: rgba(0,126,255,.08);
  color: #333
}

.Select-option.is-disabled {
  color: #ccc;
  cursor: default
}

.Select-noresults {
  box-sizing: border-box;
  color: #999;
  cursor: default;
  display: block;
  padding: 8px 10px
}

.Select--multi .Select-input {
  vertical-align: middle;
  margin-left: 10px;
  padding: 0
}

.Select--multi.Select--rtl .Select-input {
  margin-left: 0;
  margin-right: 10px
}

.Select--multi.has-value .Select-input {
  margin-left: 5px
}

.Select--multi .Select-value {
  background-color: #ebf5ff;
  background-color: rgba(0,126,255,.08);
  border-radius: 2px;
  border: 1px solid #c2e0ff;
  border: 1px solid rgba(0,126,255,.24);
  color: #007eff;
  display: inline-block;
  font-size: .9em;
  line-height: 1.4;
  margin-left: 5px;
  margin-top: 5px;
  vertical-align: top
}

.Select--multi .Select-value-icon,.Select--multi .Select-value-label {
  display: inline-block;
  vertical-align: middle
}

.Select--multi .Select-value-label {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
  cursor: default;
  padding: 2px 5px
}

.Select--multi a.Select-value-label {
  color: #007eff;
  cursor: pointer;
  text-decoration: none
}

.Select--multi a.Select-value-label:hover {
  text-decoration: underline
}

.Select--multi .Select-value-icon {
  cursor: pointer;
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px;
  border-right: 1px solid #c2e0ff;
  border-right: 1px solid rgba(0,126,255,.24);
  padding: 1px 5px 3px
}

.Select--multi .Select-value-icon:focus,.Select--multi .Select-value-icon:hover {
  background-color: #d8eafd;
  background-color: rgba(0,113,230,.08);
  color: #0071e6
}

.Select--multi .Select-value-icon:active {
  background-color: #c2e0ff;
  background-color: rgba(0,126,255,.24)
}

.Select--multi.Select--rtl .Select-value {
  margin-left: 0;
  margin-right: 5px
}

.Select--multi.Select--rtl .Select-value-icon {
  border-right: none;
  border-left: 1px solid #c2e0ff;
  border-left: 1px solid rgba(0,126,255,.24)
}

.Select--multi.is-disabled .Select-value {
  background-color: #fcfcfc;
  border: 1px solid #e3e3e3;
  color: #333
}

.Select--multi.is-disabled .Select-value-icon {
  cursor: not-allowed;
  border-right: 1px solid #e3e3e3
}

.Select--multi.is-disabled .Select-value-icon:active,.Select--multi.is-disabled .Select-value-icon:focus,.Select--multi.is-disabled .Select-value-icon:hover {
  background-color: #fcfcfc
}

@keyframes Select-animation-spin {
  to {
      transform: rotate(1turn)
  }
}

@-webkit-keyframes Select-animation-spin {
  to {
      -webkit-transform: rotate(1turn)
  }
}

.property-popover {
  position: absolute;
  min-width: 300px;
  min-height: 30px;
  background: #282f3f;
  color: #dddddd;
  border-radius: 5px;
  padding: 10px 15px;
  left: 100px;
  top: 100px;
}

.property-popover label {
  font-size: 18px;
}

.property-popover>body {
  display: flex;
  position: fixed;
  flex-direction: row;
  background: black;
}

.property-popover table {
  border-top: 1px solid rgba(128, 128, 128, 0.6);
  width: 100%;
}

.property-popover .row-item {
  width: 100%;
  border-bottom: 1px dashed rgba(128, 128, 128, 0.5);
}

.property-popover .row-key {
  padding: 3px;
  min-width: 100px;
}

.property-popover .row-value {
  padding: 3px;
  border-left: 1px dashed rgba(128, 128, 128, 0.5);
  text-align: left;
  font-style: italic;
  word-break: keep-all;
}

.graphxr-dialog-root {
  background-color: rgb(0, 0, 0, 0.6);
  pointer-events: none;
  padding: 1px;
}

.graphxr-dialog-root .modal-dialog {
  max-width: 80%;
  min-height: 400px;
  height: 100%;
}

.graphxr-dialog-root .modal-content {
  background: var(--main-surface-bg-color);
  border: 1px solid var(--main-border-color);
  /*position: absolute;*/
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.graphxr-dialog-root .modal-header {
  padding: 10px;
  color: var(--main-text-color);
  border-bottom: 1px solid var(--main-border-color);
}

.graphxr-dialog-root .modal-header .close {
  color: var(--main-icon-color);
  opacity: 0.5;
  margin-right: 0px;
}

.graphxr-dialog-root .modal-header .close:focus, .graphxr-dialog-root .modal-header .close:hover {
  opacity: 1.0;
}

.graphxr-dialog-root .modal-body {
  padding: 0px;
  height: 100%;
  overflow-y: auto;
}

#svgElement circle.node-base {
  fill: #1abc9c;
  stroke-width: 3px;
}

#svgElement text {
  fill: white;
}

#svgElement circle.node.overlay:hover {
  fill: rgba(150, 150, 255, 0.5);
}

#svgElement circle.node.ring:hover {
  stroke: rgba(150, 150, 255, 0.5);
}

#svgElement path.relationship {
  fill: var(--main-text-color);
}

#svgElement path.relationship.overlay:hover {
  fill: rgba(150, 150, 255, 0.5);
  stroke: rgba(150, 150, 255, 0.5);
}

svg {
  fill: green;
}

/* BASICS */
.CodeMirror {
  /* Set height, width, borders, and global font properties here */
  font-family: monospace;
  height: 300px;
  color: black;
  direction: ltr;
}

/* PADDING */
.CodeMirror-lines {
  padding: 4px 0;
  /* Vertical padding around content */
}

.CodeMirror pre.CodeMirror-line, .CodeMirror pre.CodeMirror-line-like {
  padding: 0 4px;
  /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: white;
  /* The little square between H and V scrollbars */
}

/* GUTTER */
.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
  white-space: nowrap;
}

.CodeMirror-linenumbers {
}

.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #999;
  white-space: nowrap;
}

.CodeMirror-guttermarker {
  color: black;
}

.CodeMirror-guttermarker-subtle {
  color: #999;
}

/* CURSOR */
.CodeMirror-cursor {
  border-left: 1px solid black;
  border-right: none;
  width: 0;
}

/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}

.cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}

.cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}

.cm-fat-cursor-mark {
  background-color: rgba(20, 255, 20, 0.5);
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
}

.cm-animate-fat-cursor {
  width: auto;
  border: 0;
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}

@-moz-keyframes blink {
  0% {
  }

  50% {
      background-color: transparent;
  }

  100% {
  }
}

@-webkit-keyframes blink {
  0% {
  }

  50% {
      background-color: transparent;
  }

  100% {
  }
}

@keyframes blink {
  0% {
  }

  50% {
      background-color: transparent;
  }

  100% {
  }
}

/* Can style cursor different in overwrite (non-insert) mode */
.CodeMirror-overwrite .CodeMirror-cursor {
}

.cm-tab {
  display: inline-block;
  text-decoration: inherit;
}

.CodeMirror-rulers {
  position: absolute;
  left: 0;
  right: 0;
  top: -50px;
  bottom: 0;
  overflow: hidden;
}

.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  top: 0;
  bottom: 0;
  position: absolute;
}

/* DEFAULT THEME */
.cm-s-default .cm-header {
  color: blue;
}

.cm-s-default .cm-quote {
  color: #090;
}

.cm-negative {
  color: #d44;
}

.cm-positive {
  color: #292;
}

.cm-header, .cm-strong {
  font-weight: bold;
}

.cm-em {
  font-style: italic;
}

.cm-link {
  text-decoration: underline;
}

.cm-strikethrough {
  text-decoration: line-through;
}

.cm-s-default .cm-keyword {
  color: #708;
}

.cm-s-default .cm-atom {
  color: #219;
}

.cm-s-default .cm-number {
  color: #164;
}

.cm-s-default .cm-def {
  color: #00f;
}

.cm-s-default .cm-variable, .cm-s-default .cm-punctuation, .cm-s-default .cm-property, .cm-s-default .cm-operator {
}

.cm-s-default .cm-variable-2 {
  color: #05a;
}

.cm-s-default .cm-variable-3, .cm-s-default .cm-type {
  color: #085;
}

.cm-s-default .cm-comment {
  color: #a50;
}

.cm-s-default .cm-string {
  color: #a11;
}

.cm-s-default .cm-string-2 {
  color: #f50;
}

.cm-s-default .cm-meta {
  color: #555;
}

.cm-s-default .cm-qualifier {
  color: #555;
}

.cm-s-default .cm-builtin {
  color: #30a;
}

.cm-s-default .cm-bracket {
  color: #997;
}

.cm-s-default .cm-tag {
  color: #170;
}

.cm-s-default .cm-attribute {
  color: #00c;
}

.cm-s-default .cm-hr {
  color: #999;
}

.cm-s-default .cm-link {
  color: #00c;
}

.cm-s-default .cm-error {
  color: #f00;
}

.cm-invalidchar {
  color: #f00;
}

.CodeMirror-composing {
  border-bottom: 2px solid;
}

/* Default styles for common addons */
div.CodeMirror span.CodeMirror-matchingbracket {
  color: #0b0;
}

div.CodeMirror span.CodeMirror-nonmatchingbracket {
  color: #a22;
}

.CodeMirror-matchingtag {
  background: rgba(255, 150, 0, .3);
}

.CodeMirror-activeline-background {
  background: #e8f2ff;
}

/* STOP */
/* The rest of this file contains styles related to the mechanics of
 the editor. You probably shouldn't touch them. */
.CodeMirror {
  position: relative;
  overflow: hidden;
  background: white;
}

.CodeMirror-scroll {
  overflow: scroll !important;
  /* Things will break if this is overridden */
  /* 50px is the magic margin used to hide the element's real scrollbars */
  /* See overflow: hidden in .CodeMirror */
  margin-bottom: -50px;
  margin-right: -50px;
  padding-bottom: 50px;
  height: 100%;
  outline: none;
  /* Prevent dragging from highlighting the element */
  position: relative;
}

.CodeMirror-sizer {
  position: relative;
  border-right: 50px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
 before actual scrolling happens, thus preventing shaking and
 flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  position: absolute;
  z-index: 6;
  display: none;
  outline: none;
}

.CodeMirror-vscrollbar {
  right: 0;
  top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}

.CodeMirror-hscrollbar {
  bottom: 0;
  left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}

.CodeMirror-scrollbar-filler {
  right: 0;
  bottom: 0;
}

.CodeMirror-gutter-filler {
  left: 0;
  bottom: 0;
}

.CodeMirror-gutters {
  position: absolute;
  left: 0;
  top: 0;
  min-height: 100%;
  z-index: 3;
}

.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -50px;
}

.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: none !important;
  border: none !important;
}

.CodeMirror-gutter-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 4;
}

.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}

.CodeMirror-gutter-wrapper ::selection {
  background-color: transparent
}

.CodeMirror-gutter-wrapper ::-moz-selection {
  background-color: transparent
}

.CodeMirror-lines {
  cursor: text;
  min-height: 1px;
  /* prevents collapsing before first draw */
}

.CodeMirror pre.CodeMirror-line, .CodeMirror pre.CodeMirror-line-like {
  /* Reset some styles that the rest of the page might have set */
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  border-width: 0;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-variant-ligatures: contextual;
  font-variant-ligatures: contextual;
}

.CodeMirror-wrap pre.CodeMirror-line, .CodeMirror-wrap pre.CodeMirror-line-like {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  padding: 0.1px;
  /* Force widget margins to stay inside of the container */
}

.CodeMirror-widget {
}

.CodeMirror-rtl pre {
  direction: rtl;
}

.CodeMirror-code {
  outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll, .CodeMirror-sizer, .CodeMirror-gutter, .CodeMirror-gutters, .CodeMirror-linenumber {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
  pointer-events: none;
}

.CodeMirror-measure pre {
  position: static;
}

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}

div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
  visibility: visible;
}

.CodeMirror-selected {
  background: #d9d9d9;
}

.CodeMirror-focused .CodeMirror-selected {
  background: #d7d4f0;
}

.CodeMirror-crosshair {
  cursor: crosshair;
}

.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection {
  background: #d7d4f0;
}

.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}

.cm-searching {
  background-color: #ffa;
  background-color: rgba(255, 255, 0, .4);
}

/* Used to force a border model for a node */
.cm-force-border {
  padding-right: .1px;
}

@media print {
  /* Hide the cursor when printing */
  .CodeMirror div.CodeMirror-cursors {
      visibility: hidden;
  }
}

/* See issue #2901 */
.cm-tab-wrap-hack:after {
  content: '';
}

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext {
  background: none;
}

.live-code-editor .modal-body {
  height: 100%;
}

.live-code {
  height: 100%;
  padding-bottom: 50px;
  /* display: flex;
  flex-direction: column; */
}

.live-code .react-codemirror2 {
  height: 100%;
  width: 100%;
  font-size: 12px;
  flex: 1;
  overflow: auto;
}

.live-code .actions {
  padding: 5px 0 0 10px;
  border-top: 1px solid var(--main-border-color);
  text-align: right;
  padding-right: 10px;
}

.live-code .actions > .kv-button {
  min-width: 80px;
}

.live-code .react-codemirror2 .cm-s-mbo.CodeMirror {
  width: 100%;
  overflow: hidden;
  padding-right: 30px;
  min-height: 100%;
}

.map-setting {
}

.map-setting .setting-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 10px;
  color: var(--main-text-color);
}

.map-setting .kv-select {
  width: 60%;
  margin-left: 10px;
}

.map-setting .kv-input {
  margin: 0px;
  margin-left: 10px;
  height: 25px;
}

.map-setting span {
  color: var(--main-text-color);
}

.map-setting .button-group {
  text-align: right;
}

.map-setting .mode {
}

.map-setting .mode input {
  margin-left: 10px;
}

.map-setting .setting-row .kv-input {
  width: 80%;
}

/****************************************************************/
/*   Based on mbonaci's Brackets mbo theme                      */
/*   https://github.com/mbonaci/global/blob/master/Mbo.tmTheme  */
/*   Create your own: http://tmtheme-editor.herokuapp.com       */
/****************************************************************/
.cm-s-mbo.CodeMirror {
  background: #2c2c2c;
  color: #ffffec;
}

.cm-s-mbo div.CodeMirror-selected {
  background: #716C62;
}

.cm-s-mbo .CodeMirror-line::selection, .cm-s-mbo .CodeMirror-line > span::selection, .cm-s-mbo .CodeMirror-line > span > span::selection {
  background: rgba(113, 108, 98, .99);
}

.cm-s-mbo .CodeMirror-line::-moz-selection, .cm-s-mbo .CodeMirror-line > span::-moz-selection, .cm-s-mbo .CodeMirror-line > span > span::-moz-selection {
  background: rgba(113, 108, 98, .99);
}

.cm-s-mbo .CodeMirror-gutters {
  background: #4e4e4e;
  border-right: 0px;
}

.cm-s-mbo .CodeMirror-guttermarker {
  color: white;
}

.cm-s-mbo .CodeMirror-guttermarker-subtle {
  color: grey;
}

.cm-s-mbo .CodeMirror-linenumber {
  color: #dadada;
}

.cm-s-mbo .CodeMirror-cursor {
  border-left: 1px solid #ffffec;
}

.cm-s-mbo span.cm-comment {
  color: #95958a;
}

.cm-s-mbo span.cm-atom {
  color: #00a8c6;
}

.cm-s-mbo span.cm-number {
  color: #00a8c6;
}

.cm-s-mbo span.cm-property, .cm-s-mbo span.cm-attribute {
  color: #9ddfe9;
}

.cm-s-mbo span.cm-keyword {
  color: #ffb928;
}

.cm-s-mbo span.cm-string {
  color: #ffcf6c;
}

.cm-s-mbo span.cm-string.cm-property {
  color: #ffffec;
}

.cm-s-mbo span.cm-variable {
  color: #ffffec;
}

.cm-s-mbo span.cm-variable-2 {
  color: #00a8c6;
}

.cm-s-mbo span.cm-def {
  color: #ffffec;
}

.cm-s-mbo span.cm-bracket {
  color: #fffffc;
  font-weight: bold;
}

.cm-s-mbo span.cm-tag {
  color: #9ddfe9;
}

.cm-s-mbo span.cm-link {
  color: #f54b07;
}

.cm-s-mbo span.cm-error {
  border-bottom: #636363;
  color: #ffffec;
}

.cm-s-mbo span.cm-qualifier {
  color: #ffffec;
}

.cm-s-mbo .CodeMirror-activeline-background {
  background: #494b41;
}

.cm-s-mbo .CodeMirror-matchingbracket {
  color: #ffb928 !important;
}

.cm-s-mbo .CodeMirror-matchingtag {
  background: rgba(255, 255, 255, .37);
}

/* The lint marker gutter */
.CodeMirror-lint-markers {
  width: 16px;
}

.CodeMirror-lint-tooltip {
  background-color: #ffd;
  border: 1px solid black;
  border-radius: 4px 4px 4px 4px;
  color: black;
  font-family: monospace;
  font-size: 10pt;
  overflow: hidden;
  padding: 2px 5px;
  position: fixed;
  white-space: pre;
  white-space: pre-wrap;
  z-index: 100;
  max-width: 600px;
  opacity: 0;
  transition: opacity .4s;
  -moz-transition: opacity .4s;
  -webkit-transition: opacity .4s;
  -o-transition: opacity .4s;
  -ms-transition: opacity .4s;
}

.CodeMirror-lint-mark {
  background-position: left bottom;
  background-repeat: repeat-x;
}

.CodeMirror-lint-mark-warning {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAADCAYAAAC09K7GAAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9sJFhQXEbhTg7YAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAMklEQVQI12NkgIIvJ3QXMjAwdDN+OaEbysDA4MPAwNDNwMCwiOHLCd1zX07o6kBVGQEAKBANtobskNMAAAAASUVORK5CYII=");
}

.CodeMirror-lint-mark-error {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAADCAYAAAC09K7GAAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9sJDw4cOCW1/KIAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAHElEQVQI12NggIL/DAz/GdA5/xkY/qPKMDAwAADLZwf5rvm+LQAAAABJRU5ErkJggg==");
}

.CodeMirror-lint-marker {
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  display: inline-block;
  height: 16px;
  width: 16px;
  vertical-align: middle;
  position: relative;
}

.CodeMirror-lint-message {
  padding-left: 18px;
  background-position: top left;
  background-repeat: no-repeat;
}

.CodeMirror-lint-marker-warning, .CodeMirror-lint-message-warning {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAANlBMVEX/uwDvrwD/uwD/uwD/uwD/uwD/uwD/uwD/uwD6twD/uwAAAADurwD2tQD7uAD+ugAAAAD/uwDhmeTRAAAADHRSTlMJ8mN1EYcbmiixgACm7WbuAAAAVklEQVR42n3PUQqAIBBFUU1LLc3u/jdbOJoW1P08DA9Gba8+YWJ6gNJoNYIBzAA2chBth5kLmG9YUoG0NHAUwFXwO9LuBQL1giCQb8gC9Oro2vp5rncCIY8L8uEx5ZkAAAAASUVORK5CYII=");
}

.CodeMirror-lint-marker-error, .CodeMirror-lint-message-error {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAHlBMVEW7AAC7AACxAAC7AAC7AAAAAAC4AAC5AAD///+7AAAUdclpAAAABnRSTlMXnORSiwCK0ZKSAAAATUlEQVR42mWPOQ7AQAgDuQLx/z8csYRmPRIFIwRGnosRrpamvkKi0FTIiMASR3hhKW+hAN6/tIWhu9PDWiTGNEkTtIOucA5Oyr9ckPgAWm0GPBog6v4AAAAASUVORK5CYII=");
}

.CodeMirror-lint-marker-multiple {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAHCAMAAADzjKfhAAAACVBMVEUAAAAAAAC/v7914kyHAAAAAXRSTlMAQObYZgAAACNJREFUeNo1ioEJAAAIwmz/H90iFFSGJgFMe3gaLZ0od+9/AQZ0ADosbYraAAAAAElFTkSuQmCC");
  background-repeat: no-repeat;
  background-position: right bottom;
  width: 100%;
  height: 100%;
}

.CodeMirror-lint-line-error {
  background-color: rgba(183, 76, 81, 0.08);
}

.CodeMirror-lint-line-warning {
  background-color: rgba(255, 211, 0, 0.1);
}

/*
* Copyright (c) 2002-2017 "Neo Technology,"
* Network Engine for Objects in Lund AB [http://neotechnology.com]
*
* This file is part of Neo4j.
*
* Neo4j is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
/*
* Copyright (c) 2002-2017 "Neo Technology,"
* Network Engine for Objects in Lund AB [http://neotechnology.com]
*
* This file is part of Neo4j.
*
* Neo4j is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
/*
* Copyright (c) 2002-2017 "Neo Technology,"
* Network Engine for Objects in Lund AB [http://neotechnology.com]
*
* This file is part of Neo4j.
*
* Neo4j is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
/*
Credits: http://ethanschoonover.com/solarized

SOLARIZED HEX     16/8 TERMCOL  XTERM/HEX   L*A*B      RGB         HSB
--------- ------- ---- -------  ----------- ---------- ----------- -----------
base03    #002b36  8/4 brblack  234 #1c1c1c 15 -12 -12   0  43  54 193 100  21
base02    #073642  0/4 black    235 #262626 20 -12 -12   7  54  66 192  90  26
base01    #586e75 10/7 brgreen  240 #585858 45 -07 -07  88 110 117 194  25  46
base00    #657b83 11/7 bryellow 241 #626262 50 -07 -07 101 123 131 195  23  51
base0     #839496 12/6 brblue   244 #808080 60 -06 -03 131 148 150 186  13  59
base1     #93a1a1 14/4 brcyan   245 #8a8a8a 65 -05 -02 147 161 161 180   9  63
base2     #eee8d5  7/7 white    254 #e4e4e4 92 -00  10 238 232 213  44  11  93
base3     #fdf6e3 15/7 brwhite  230 #ffffd7 97  00  10 253 246 227  44  10  99
yellow    #b58900  3/3 yellow   136 #af8700 60  10  65 181 137   0  45 100  71
orange    #cb4b16  9/3 brred    166 #d75f00 50  50  55 203  75  22  18  89  80
red       #dc322f  1/1 red      160 #d70000 50  65  45 220  50  47   1  79  86
magenta   #d33682  5/5 magenta  125 #af005f 50  65 -05 211  54 130 331  74  83
violet    #6c71c4 13/5 brmagenta 61 #5f5faf 50  15 -45 108 113 196 237  45  77
blue      #268bd2  4/4 blue      33 #0087ff 55 -10 -45  38 139 210 205  82  82
cyan      #2aa198  6/6 cyan      37 #00afaf 60 -35 -05  42 161 152 175  74  63
green     #859900  2/2 green     64 #5f8700 60 -20  65 133 153   0  68 100  60
*/
/***********
* Editor
*/
.CodeMirror {
  height: 100px;
  width: 100%;
  font-size: 12px;
  padding: 4px 6px;
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: #2c2c2c;
}

.CodeMirror-lint-tooltip {
  z-index: 2090 !important;
}

.CodeMirror.cm-s-cypher {
  background-color: #fdf6e3;
  line-height: 1.4375;
  color: #657b83;
}

.CodeMirror.cm-s-cypher.cm-s-cypher-dark {
  background-color: #222222;
  color: #839496;
}

.cm-s-cypher pre {
  padding: 0;
}

.cm-s-cypher .CodeMirror-lines {
  padding: 0;
}

.cm-s-cypher .CodeMirror-cursor {
  width: auto;
  border: 0;
  background: rgba(147, 161, 161, 0.37);
  z-index: 1;
}

.cm-s-cypher.cm-s-cypher-dark .CodeMirror-cursor {
  background: rgba(131, 148, 150, 0.37);
}

/***********
* Gutter
*/
.cm-s-cypher .CodeMirror-gutters {
  border: none;
  padding-left: 5px;
  padding-right: 3px;
  background-color: #eee8d5;
  border-right: 3px solid #fdf6e3;
}

.cm-s-cypher.cm-s-cypher-dark .CodeMirror-gutters {
  background-color: #073642;
  border-right: 3px solid #002b36;
}

.cm-s-cypher .CodeMirror-linenumber {
  padding-left: 5px;
  padding-right: 3px;
  color: #657b83;
}

.cm-s-cypher.cm-s-cypher-dark .CodeMirror-linenumber {
  color: #839496;
}

/*
* Copyright (c) 2002-2017 "Neo Technology,"
* Network Engine for Objects in Lund AB [http://neotechnology.com]
*
* This file is part of Neo4j.
*
* Neo4j is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
/***********
* Token
*/
.cm-s-cypher .cm-comment {
  color: #93a1a1;
}

.cm-s-cypher.cm-s-cypher-dark .cm-comment {
  color: #586e75;
}

.cm-s-cypher .cm-string {
  color: #b58900;
}

.cm-s-cypher .cm-number {
  color: #2aa198;
}

.cm-s-cypher .cm-operator {
}

.cm-s-cypher .cm-keyword {
  color: #859900;
}

/***********
* Parser
*/
.cm-s-cypher .cm-p-label {
  color: #cb4b16;
}

.cm-s-cypher .cm-p-relationshipType {
  color: #cb4b16;
}

.cm-s-cypher .cm-p-variable {
  color: #268bd2;
}

.cm-s-cypher .cm-p-procedure {
  color: #6c71c4;
}

.cm-s-cypher .cm-p-function {
  color: #6c71c4;
}

.cm-s-cypher .cm-p-parameter {
  color: #dc322f;
}

.cm-s-cypher .cm-p-property {
  color: #586e75;
}

.cm-s-cypher.cm-s-cypher-dark .cm-p-property {
  color: #93a1a1;
}

.cm-s-cypher .cm-p-consoleCommand {
  color: #d33682;
}

.cm-s-cypher .cm-p-procedureOutput {
  color: #268bd2;
}

/*
* Copyright (c) 2002-2017 "Neo Technology,"
* Network Engine for Objects in Lund AB [http://neotechnology.com]
*
* This file is part of Neo4j.
*
* Neo4j is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
.CodeMirror-hints {
  margin: 0;
  padding: 0;
  position: absolute;
  z-index: 10;
  list-style: none;
  box-shadow: none;
  border: 1px solid gray;
  border-radius: 4px;
  background-color: rgba(16, 16, 16, 0.8);
  font-size: 90%;
  font-family: monospace;
  max-height: 30em;
  max-width: 600px;
  overflow-y: auto;
  overflow-x: auto;
  z-index: 4090;
}

.CodeMirror-hint {
  padding: 4px 10px;
  white-space: pre;
  color: #DDD;
  cursor: pointer;
  font-size: 11pt;
  background-position-x: 50px;
}

.CodeMirror-hint-active {
  color: #222 !important;
  background-color: rgba(255, 255, 255, 0.6) !important;
}

.cm-hint-keyword {
}

.cm-hint-label {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23cb4b16'>L</text></svg>");
}

.cm-hint-relationshipType {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23cb4b16'>R</text></svg>");
}

.cm-hint-variable {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23268bd2'>V</text></svg>");
}

.cm-hint-procedure {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%236c71c4'>λ</text></svg>");
}

.cm-hint-function {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%236c71c4'>λ</text></svg>");
}

.cm-hint-parameter {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23dc322f'>$</text></svg>");
}

.cm-hint-propertyKey {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23586e75'>P</text></svg>");
}

.cm-hint-consoleCommand {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23d33682'>C</text></svg>");
}

.cm-hint-consoleCommandSubcommand {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23d33682'>C</text></svg>");
}

.cm-hint-procedureOutput {
  padding-left: 22px !important;
  background-size: auto 60% !important;
  background-position: 3px center;
  background-repeat: no-repeat !important;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' viewBox='0 0 40 40'><circle cx='20' cy='20' r='19' stroke='black' stroke-width='1' fill='white'></circle><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' font-family='Monaco' fill='%23268bd2'>V</text></svg>");
}

.ReactTable {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  border: 1px solid rgba(0,0,0,0.1);
}

.ReactTable * {
  box-sizing: border-box
}

.ReactTable .rt-table {
  -webkit-box-flex: 1;
  -ms-flex: auto 1;
  flex: auto 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
  border-collapse: collapse;
  overflow: auto
}

.ReactTable .rt-thead {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ReactTable .rt-thead.-headerGroups {
  background: rgba(0,0,0,0.03);
  border-bottom: 1px solid rgba(0,0,0,0.05)
}

.ReactTable .rt-thead.-filters {
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.ReactTable .rt-thead.-filters input,.ReactTable .rt-thead.-filters select {
  border: 1px solid rgba(0,0,0,0.1);
  background: #fff;
  padding: 5px 7px;
  font-size: inherit;
  border-radius: 3px;
  font-weight: normal;
  outline-width: 0
}

.ReactTable .rt-thead.-filters .rt-th {
  border-right: 1px solid rgba(0,0,0,0.02)
}

.ReactTable .rt-thead.-header {
  box-shadow: 0 2px 15px 0 rgba(0,0,0,0.15)
}

.ReactTable .rt-thead .rt-tr {
  text-align: center
}

.ReactTable .rt-thead .rt-th,.ReactTable .rt-thead .rt-td {
  padding: 5px 5px;
  line-height: normal;
  position: relative;
  border-right: 1px solid rgba(0,0,0,0.05);
  transition: box-shadow .3s cubic-bezier(.175,.885,.32,1.275);
  box-shadow: inset 0 0 0 0 transparent;
}

.ReactTable .rt-thead .rt-th.-sort-asc,.ReactTable .rt-thead .rt-td.-sort-asc {
  box-shadow: inset 0 3px 0 0 rgba(0,0,0,0.6)
}

.ReactTable .rt-thead .rt-th.-sort-desc,.ReactTable .rt-thead .rt-td.-sort-desc {
  box-shadow: inset 0 -3px 0 0 rgba(0,0,0,0.6)
}

.ReactTable .rt-thead .rt-th.-cursor-pointer,.ReactTable .rt-thead .rt-td.-cursor-pointer {
  cursor: pointer
}

.ReactTable .rt-thead .rt-th:last-child,.ReactTable .rt-thead .rt-td:last-child {
  border-right: 0
}

.ReactTable .rt-thead .rt-th:focus {
  outline-width: 0
}

.ReactTable .rt-thead .rt-resizable-header {
  overflow: visible;
}

.ReactTable .rt-thead .rt-resizable-header:last-child {
  overflow: hidden
}

.ReactTable .rt-thead .rt-resizable-header-content {
  overflow: hidden;
  text-overflow: ellipsis
}

.ReactTable .rt-thead .rt-header-pivot {
  border-right-color: #f7f7f7
}

.ReactTable .rt-thead .rt-header-pivot:after,.ReactTable .rt-thead .rt-header-pivot:before {
  left: 100%;
  top: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none
}

.ReactTable .rt-thead .rt-header-pivot:after {
  border-color: rgba(255,255,255,0);
  border-left-color: #fff;
  border-width: 8px;
  margin-top: -8px
}

.ReactTable .rt-thead .rt-header-pivot:before {
  border-color: rgba(102,102,102,0);
  border-left-color: #f7f7f7;
  border-width: 10px;
  margin-top: -10px
}

.ReactTable .rt-tbody {
  -webkit-box-flex: 99999;
  -ms-flex: 99999 1 auto;
  flex: 99999 1 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow: auto;
}

.ReactTable .rt-tbody .rt-tr-group {
  border-bottom: solid 1px rgba(0,0,0,0.05);
}

.ReactTable .rt-tbody .rt-tr-group:last-child {
  border-bottom: 0
}

.ReactTable .rt-tbody .rt-td {
  border-right: 1px solid rgba(0,0,0,0.02);
}

.ReactTable .rt-tbody .rt-td:last-child {
  border-right: 0
}

.ReactTable .rt-tbody .rt-expandable {
  cursor: pointer;
  text-overflow: clip
}

.ReactTable .rt-tr-group {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch
}

.ReactTable .rt-tr {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex
}

.ReactTable .rt-th,.ReactTable .rt-td {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 7px 5px;
  overflow: hidden;
  transition: .3s ease;
  transition-property: width,min-width,padding,opacity;
}

.ReactTable .rt-th.-hidden,.ReactTable .rt-td.-hidden {
  width: 0 !important;
  min-width: 0 !important;
  padding: 0 !important;
  border: 0 !important;
  opacity: 0 !important
}

.ReactTable .rt-expander {
  display: inline-block;
  position: relative;
  margin: 0;
  color: transparent;
  margin: 0 10px;
}

.ReactTable .rt-expander:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%) rotate(-90deg);
  transform: translate(-50%,-50%) rotate(-90deg);
  border-left: 5.04px solid transparent;
  border-right: 5.04px solid transparent;
  border-top: 7px solid rgba(0,0,0,0.8);
  transition: all .3s cubic-bezier(.175,.885,.32,1.275);
  cursor: pointer
}

.ReactTable .rt-expander.-open:after {
  -webkit-transform: translate(-50%,-50%) rotate(0);
  transform: translate(-50%,-50%) rotate(0)
}

.ReactTable .rt-resizer {
  display: inline-block;
  position: absolute;
  width: 36px;
  top: 0;
  bottom: 0;
  right: -18px;
  cursor: col-resize;
  z-index: 10
}

.ReactTable .rt-tfoot {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  box-shadow: 0 0 15px 0 rgba(0,0,0,0.15);
}

.ReactTable .rt-tfoot .rt-td {
  border-right: 1px solid rgba(0,0,0,0.05);
}

.ReactTable .rt-tfoot .rt-td:last-child {
  border-right: 0
}

.ReactTable.-striped .rt-tr.-odd {
  background: rgba(0,0,0,0.03)
}

.ReactTable.-highlight .rt-tbody .rt-tr:not(.-padRow):hover {
  background: rgba(0,0,0,0.05)
}

.ReactTable .-pagination {
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 3px;
  box-shadow: 0 0 15px 0 rgba(0,0,0,0.1);
  border-top: 2px solid rgba(0,0,0,0.1);
}

.ReactTable .-pagination input,.ReactTable .-pagination select {
  border: 1px solid rgba(0,0,0,0.1);
  background: #fff;
  padding: 5px 7px;
  font-size: inherit;
  border-radius: 3px;
  font-weight: normal;
  outline-width: 0
}

.ReactTable .-pagination .-btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: block;
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: 3px;
  padding: 6px;
  font-size: 1em;
  color: rgba(0,0,0,0.6);
  background: rgba(0,0,0,0.1);
  transition: all .1s ease;
  cursor: pointer;
  outline-width: 0;
}

.ReactTable .-pagination .-btn[disabled] {
  opacity: .5;
  cursor: default
}

.ReactTable .-pagination .-btn:not([disabled]):hover {
  background: rgba(0,0,0,0.3);
  color: #fff
}

.ReactTable .-pagination .-previous,.ReactTable .-pagination .-next {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: center
}

.ReactTable .-pagination .-center {
  -webkit-box-flex: 1.5;
  -ms-flex: 1.5;
  flex: 1.5;
  text-align: center;
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: distribute;
  justify-content: space-around
}

.ReactTable .-pagination .-pageInfo {
  display: inline-block;
  margin: 3px 10px;
  white-space: nowrap
}

.ReactTable .-pagination .-pageJump {
  display: inline-block;
}

.ReactTable .-pagination .-pageJump input {
  width: 70px;
  text-align: center
}

.ReactTable .-pagination .-pageSizeOptions {
  margin: 3px 10px
}

.ReactTable .rt-noData {
  display: block;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  background: rgba(255,255,255,0.8);
  transition: all .3s ease;
  z-index: 1;
  pointer-events: none;
  padding: 20px;
  color: rgba(0,0,0,0.5)
}

.ReactTable .-loading {
  display: block;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(255,255,255,0.8);
  transition: all .3s ease;
  z-index: -1;
  opacity: 0;
  pointer-events: none;
}

.ReactTable .-loading > div {
  position: absolute;
  display: block;
  text-align: center;
  width: 100%;
  top: 50%;
  left: 0;
  font-size: 15px;
  color: rgba(0,0,0,0.6);
  -webkit-transform: translateY(-52%);
  transform: translateY(-52%);
  transition: all .3s cubic-bezier(.25,.46,.45,.94)
}

.ReactTable .-loading.-active {
  opacity: 1;
  z-index: 2;
  pointer-events: all;
}

.ReactTable .-loading.-active > div {
  -webkit-transform: translateY(50%);
  transform: translateY(50%)
}

.ReactTable .rt-resizing .rt-th,.ReactTable .rt-resizing .rt-td {
  transition: none !important;
  cursor: col-resize;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.rc-slider {
  position: relative;
  height: 14px;
  padding: 5px 0;
  width: 100%;
  border-radius: 6px;
  -ms-touch-action: none;
  touch-action: none;
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.rc-slider * {
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.rc-slider-rail {
  position: absolute;
  width: 100%;
  background-color: #e9e9e9;
  height: 4px;
  border-radius: 6px;
}

.rc-slider-track {
  position: absolute;
  left: 0;
  height: 4px;
  border-radius: 6px;
  background-color: #abe2fb;
}

.rc-slider-handle {
  position: absolute;
  width: 14px;
  height: 14px;
  cursor: pointer;
  cursor: -webkit-grab;
  margin-top: -5px;
  cursor: grab;
  border-radius: 50%;
  border: solid 2px #96dbfa;
  background-color: #fff;
  -ms-touch-action: pan-x;
  touch-action: pan-x;
}

.rc-slider-handle:focus {
  border-color: #57c5f7;
  box-shadow: 0 0 0 5px #96dbfa;
  outline: none;
}

.rc-slider-handle-click-focused:focus {
  border-color: #96dbfa;
  box-shadow: unset;
}

.rc-slider-handle:hover {
  border-color: #57c5f7;
}

.rc-slider-handle:active {
  border-color: #57c5f7;
  box-shadow: 0 0 5px #57c5f7;
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

.rc-slider-mark {
  position: absolute;
  top: 18px;
  left: 0;
  width: 100%;
  font-size: 12px;
}

.rc-slider-mark-text {
  position: absolute;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
  color: #999;
}

.rc-slider-mark-text-active {
  color: #666;
}

.rc-slider-step {
  position: absolute;
  width: 100%;
  height: 4px;
  background: transparent;
}

.rc-slider-dot {
  position: absolute;
  bottom: -2px;
  margin-left: -4px;
  width: 8px;
  height: 8px;
  border: 2px solid #e9e9e9;
  background-color: #fff;
  cursor: pointer;
  border-radius: 50%;
  vertical-align: middle;
}

.rc-slider-dot-active {
  border-color: #96dbfa;
}

.rc-slider-dot-reverse {
  margin-left: 0;
  margin-right: -4px;
}

.rc-slider-disabled {
  background-color: #e9e9e9;
}

.rc-slider-disabled .rc-slider-track {
  background-color: #ccc;
}

.rc-slider-disabled .rc-slider-handle, .rc-slider-disabled .rc-slider-dot {
  border-color: #ccc;
  box-shadow: none;
  background-color: #fff;
  cursor: not-allowed;
}

.rc-slider-disabled .rc-slider-mark-text, .rc-slider-disabled .rc-slider-dot {
  cursor: not-allowed !important;
}

.rc-slider-vertical {
  width: 14px;
  height: 100%;
  padding: 0 5px;
}

.rc-slider-vertical .rc-slider-rail {
  height: 100%;
  width: 4px;
}

.rc-slider-vertical .rc-slider-track {
  left: 5px;
  bottom: 0;
  width: 4px;
}

.rc-slider-vertical .rc-slider-handle {
  margin-left: -5px;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}

.rc-slider-vertical .rc-slider-mark {
  top: 0;
  left: 18px;
  height: 100%;
}

.rc-slider-vertical .rc-slider-step {
  height: 100%;
  width: 4px;
}

.rc-slider-vertical .rc-slider-dot {
  left: 2px;
  margin-bottom: -4px;
}

.rc-slider-vertical .rc-slider-dot:first-child {
  margin-bottom: -4px;
}

.rc-slider-vertical .rc-slider-dot:last-child {
  margin-bottom: -4px;
}

.rc-slider-tooltip-zoom-down-enter, .rc-slider-tooltip-zoom-down-appear {
  animation-duration: .3s;
  animation-fill-mode: both;
  display: block !important;
  animation-play-state: paused;
}

.rc-slider-tooltip-zoom-down-leave {
  animation-duration: .3s;
  animation-fill-mode: both;
  display: block !important;
  animation-play-state: paused;
}

.rc-slider-tooltip-zoom-down-enter.rc-slider-tooltip-zoom-down-enter-active, .rc-slider-tooltip-zoom-down-appear.rc-slider-tooltip-zoom-down-appear-active {
  animation-name: rcSliderTooltipZoomDownIn;
  animation-play-state: running;
}

.rc-slider-tooltip-zoom-down-leave.rc-slider-tooltip-zoom-down-leave-active {
  animation-name: rcSliderTooltipZoomDownOut;
  animation-play-state: running;
}

.rc-slider-tooltip-zoom-down-enter, .rc-slider-tooltip-zoom-down-appear {
  transform: scale(0, 0);
  animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}

.rc-slider-tooltip-zoom-down-leave {
  animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
}

@keyframes rcSliderTooltipZoomDownIn {
  0% {
      opacity: 0;
      transform-origin: 50% 100%;
      transform: scale(0, 0);
  }

  100% {
      transform-origin: 50% 100%;
      transform: scale(1, 1);
  }
}

@keyframes rcSliderTooltipZoomDownOut {
  0% {
      transform-origin: 50% 100%;
      transform: scale(1, 1);
  }

  100% {
      opacity: 0;
      transform-origin: 50% 100%;
      transform: scale(0, 0);
  }
}

.rc-slider-tooltip {
  position: absolute;
  left: -9999px;
  top: -9999px;
  visibility: visible;
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.rc-slider-tooltip * {
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.rc-slider-tooltip-hidden {
  display: none;
}

.rc-slider-tooltip-placement-top {
  padding: 4px 0 8px 0;
}

.rc-slider-tooltip-inner {
  padding: 6px 2px;
  min-width: 24px;
  height: 24px;
  font-size: 12px;
  line-height: 1;
  color: #fff;
  text-align: center;
  text-decoration: none;
  background-color: #6c6c6c;
  border-radius: 6px;
  box-shadow: 0 0 4px #d9d9d9;
}

.rc-slider-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.rc-slider-tooltip-placement-top .rc-slider-tooltip-arrow {
  bottom: 4px;
  left: 50%;
  margin-left: -4px;
  border-width: 4px 4px 0;
  border-top-color: #6c6c6c;
}

/*!
* https://github.com/YouCanBookMe/react-datetime
*/
.rdt {
  position: relative;
}

.rdtPicker {
  display: none;
  position: absolute;
  width: 250px;
  padding: 4px;
  margin-top: 1px;
  z-index: 99999 !important;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,.1);
  border: 1px solid #f9f9f9;
}

.rdtOpen .rdtPicker {
  display: block;
}

.rdtStatic .rdtPicker {
  box-shadow: none;
  position: static;
}

.rdtPicker .rdtTimeToggle {
  text-align: center;
}

.rdtPicker table {
  width: 100%;
  margin: 0;
}

.rdtPicker td, .rdtPicker th {
  text-align: center;
  height: 28px;
}

.rdtPicker td {
  cursor: pointer;
}

.rdtPicker td.rdtDay:hover, .rdtPicker td.rdtHour:hover, .rdtPicker td.rdtMinute:hover, .rdtPicker td.rdtSecond:hover, .rdtPicker .rdtTimeToggle:hover {
  background: #eeeeee;
  cursor: pointer;
}

.rdtPicker td.rdtOld, .rdtPicker td.rdtNew {
  color: #999999;
}

.rdtPicker td.rdtToday {
  position: relative;
}

.rdtPicker td.rdtToday:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #428bca;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.rdtPicker td.rdtActive, .rdtPicker td.rdtActive:hover {
  background-color: #428bca;
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.rdtPicker td.rdtActive.rdtToday:before {
  border-bottom-color: #fff;
}

.rdtPicker td.rdtDisabled, .rdtPicker td.rdtDisabled:hover {
  background: none;
  color: #999999;
  cursor: not-allowed;
}

.rdtPicker td span.rdtOld {
  color: #999999;
}

.rdtPicker td span.rdtDisabled, .rdtPicker td span.rdtDisabled:hover {
  background: none;
  color: #999999;
  cursor: not-allowed;
}

.rdtPicker th {
  border-bottom: 1px solid #f9f9f9;
}

.rdtPicker .dow {
  width: 14.2857%;
  border-bottom: none;
  cursor: default;
}

.rdtPicker th.rdtSwitch {
  width: 100px;
}

.rdtPicker th.rdtNext, .rdtPicker th.rdtPrev {
  font-size: 21px;
  vertical-align: top;
}

.rdtPrev span, .rdtNext span {
  display: block;
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Chrome/Safari/Opera */
  -khtml-user-select: none;
  /* Konqueror */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
}

.rdtPicker th.rdtDisabled, .rdtPicker th.rdtDisabled:hover {
  background: none;
  color: #999999;
  cursor: not-allowed;
}

.rdtPicker thead tr:first-child th {
  cursor: pointer;
}

.rdtPicker thead tr:first-child th:hover {
  background: #eeeeee;
}

.rdtPicker tfoot {
  border-top: 1px solid #f9f9f9;
}

.rdtPicker button {
  border: none;
  background: none;
  cursor: pointer;
}

.rdtPicker button:hover {
  background-color: #eee;
}

.rdtPicker thead button {
  width: 100%;
  height: 100%;
}

td.rdtMonth, td.rdtYear {
  height: 50px;
  width: 25%;
  cursor: pointer;
}

td.rdtMonth:hover, td.rdtYear:hover {
  background: #eee;
}

.rdtCounters {
  display: inline-block;
}

.rdtCounters > div {
  float: left;
}

.rdtCounter {
  height: 100px;
}

.rdtCounter {
  width: 40px;
}

.rdtCounterSeparator {
  line-height: 100px;
}

.rdtCounter .rdtBtn {
  height: 40%;
  line-height: 40px;
  cursor: pointer;
  display: block;
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Chrome/Safari/Opera */
  -khtml-user-select: none;
  /* Konqueror */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
}

.rdtCounter .rdtBtn:hover {
  background: #eee;
}

.rdtCounter .rdtCount {
  height: 20%;
  font-size: 1.2em;
}

.rdtMilli {
  vertical-align: middle;
  padding-left: 8px;
  width: 48px;
}

.rdtMilli input {
  width: 100%;
  font-size: 1.2em;
  margin-top: 37px;
}

.rdtTime td {
  cursor: default;
}

/*

  Name:       Hopscotch
  Author:     Jan T. Sott

  CodeMirror template by Jan T. Sott (https://github.com/idleberg/base16-codemirror)
  Original Base16 color scheme by Chris Kempson (https://github.com/chriskempson/base16)

*/
.cm-s-hopscotch.CodeMirror {
  background: #322931;
  color: #d5d3d5;
}

.cm-s-hopscotch div.CodeMirror-selected {
  background: #433b42 !important;
}

.cm-s-hopscotch .CodeMirror-gutters {
  background: #322931;
  border-right: 0px;
}

.cm-s-hopscotch .CodeMirror-linenumber {
  color: #797379;
}

.cm-s-hopscotch .CodeMirror-cursor {
  border-left: 1px solid #989498 !important;
}

.cm-s-hopscotch span.cm-comment {
  color: #b33508;
}

.cm-s-hopscotch span.cm-atom {
  color: #c85e7c;
}

.cm-s-hopscotch span.cm-number {
  color: #c85e7c;
}

.cm-s-hopscotch span.cm-property, .cm-s-hopscotch span.cm-attribute {
  color: #8fc13e;
}

.cm-s-hopscotch span.cm-keyword {
  color: #dd464c;
}

.cm-s-hopscotch span.cm-string {
  color: #fdcc59;
}

.cm-s-hopscotch span.cm-variable {
  color: #8fc13e;
}

.cm-s-hopscotch span.cm-variable-2 {
  color: #1290bf;
}

.cm-s-hopscotch span.cm-def {
  color: #fd8b19;
}

.cm-s-hopscotch span.cm-error {
  background: #dd464c;
  color: #989498;
}

.cm-s-hopscotch span.cm-bracket {
  color: #d5d3d5;
}

.cm-s-hopscotch span.cm-tag {
  color: #dd464c;
}

.cm-s-hopscotch span.cm-link {
  color: #c85e7c;
}

.cm-s-hopscotch .CodeMirror-matchingbracket {
  text-decoration: underline;
  color: white !important;
}

.cm-s-hopscotch .CodeMirror-activeline-background {
  background: #302020;
}

#apoc-index-panel {
  background-color: var(--main-text-color);
  color: var(--main-bg-color);
  border-radius: 4px;
  padding: 20px;
}

#apoc-index-panel .iconfont, #apoc-index-panel .tips .fa {
  font-size: 18px;
}

#apoc-index-panel .iconfont {
  margin-right: 10px;
}

#apoc-index-panel .tips .fa {
  margin-left: 10px;
}

#apoc-index-panel .index-header {
  margin-bottom: 6px;
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
}

#apoc-index-panel .index-header.props-header {
  /* border-bottom: 1px dashed gray; */
}

#apoc-index-panel .index-header .select-all {
  font-size: 20px;
}

#apoc-index-panel .header-item {
  display: flex;
  justify-content: space-between;
  font-size: 20px;
  margin-bottom: 20px;
  z-index: 1080;
  border-top: 3px solid gray;
  padding-top: 20px;
}

#apoc-index-panel .header-item .title {
  min-width: 190px;
  width: 190px;
  line-height: 40px;
}

#apoc-index-panel .header-item .input {
  width: 660px;
}

#apoc-index-panel .header-item.search-index-config .input {
  width: 100%;
  padding-right: 20px;
}

#apoc-index-panel .search-index-config .select__control {
  height: 40px;
  font-size: 18px;
}

#apoc-index-panel .search-index-config .select__control, #apoc-index-panel .search-index-config .select__menu {
  z-index: 1490;
  color: black;
}

#apoc-index-panel .btn, #apoc-index-panel .btn:hover, #apoc-index-panel .btn:focus {
  width: 200px;
  min-width: 200px;
  color: var(--main-text-color);
  height: 40px;
  margin: 0;
}

#apoc-index-panel .index-search-tour-keyword .input {
  width: 100%;
  padding-right: 20px;
  padding-left: 10px;
}

#apoc-index-panel .index-search-tour-keyword .input input {
  width: 100%;
  border-radius: 4px;
  border: 1px solid var(--main-border-color);
  padding: 3px 20px;
}

#apoc-index-panel .index-text {
  margin-top: 0px;
  margin-bottom: 20px;
}

#apoc-index-panel #index-editor {
  height: 140px;
  margin: 6px 0 20px 0;
  overflow-y: auto;
  word-break: break-all;
}

#apoc-index-panel #index-editor .CodeMirror {
  height: 100%;
  border-radius: 4px;
}

#apoc-index-panel #index-label-list .label-item {
  margin-top: 20px;
}

#apoc-index-panel #index-label-list .label-item-name {
  border-bottom: 1px dashed var(--main-border-color);
}

#apoc-index-panel #index-label-list .label-item .name {
  border: none;
  padding-left: 0;
  text-align: left;
  /* border: 1px solid #888; */
  /* padding: 6px 10px; */
  /* border-radius: 4px; */
}

#apoc-index-panel #index-label-list .label-item-props {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: wrap;
  margin-top: 10px;
  /* padding-left: 40px; */
}

#apoc-index-panel #index-label-list .label-item-props .prop-item {
  height: 32px;
  margin: 0px;
  width: 25%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 20px;
}

#apoc-index-panel #index-label-list .label-item-props .prop-item:nth-child(4n+1) {
  padding-left: 0px;
}

/*Select All and tips*/
#apoc-index-panel .select-all.active {
  color: #dc3545 !important;
}

#apoc-index-panel .select-all .tips-content {
  margin-left: 10px;
  font-size: 14px;
  display: none;
}

#apoc-index-panel .select-all .tips:hover .tips-content, #apoc-index-panel .select-all:hover .tips-content, #apoc-index-panel .select-all.active .tips-content {
  display: inline;
}

#enhanced-table {
  background-color: #e2e3e5;
  position: fixed;
  margin: 40px 10%;
  width: 80%;
  height: 80%;
  z-index: 1490;
  border-radius: 4px;
}

#enhanced-table .form-control {
  min-width: auto;
  background-color: white;
  color: black;
  border: 1px solid #ced4da;
}

#enhanced-table .container-fluid {
  margin: 15px 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

#enhanced-table .kv-scrollbar ::-webkit-scrollbar-track, #enhanced-table .kv-scrollbar ::-webkit-scrollbar-corner {
  background-color: #FFF;
  border-bottom-color: #17a2b8;
}

#enhanced-table .kv-scrollbar ::-webkit-scrollbar-thumb {
  background-color: #888;
}

#enhanced-table .kv-scrollbar ::-webkit-scrollbar-thumb:hover {
  background-color: #666;
}

#enhanced-table .kv-scrollbar.kv-scrollbar-default ::-webkit-scrollbar-track {
  background-color: #DDD;
}

#enhanced-table .table-header {
  margin-bottom: 10px;
}

#enhanced-table .table-header>div {
  margin-bottom: 10px;
}

#enhanced-table .table-header .header-actions {
  height: 22px;
  margin-bottom: -22px;
  display: flex;
  justify-content: flex-end;
}

#enhanced-table .table-header .header-actions > div {
  cursor: pointer;
  padding: 0 10px;
}

#enhanced-table .table-header .header-actions > div.action-selection-only {
  font-size: 20px;
  margin: auto;
}

#enhanced-table .table-header .header-actions > div.action-selection-only .fa {
  margin-left: 10px;
  font-weight: 600;
}

/**
table-header > props-multi-select
*/
#enhanced-table .table-header .table-tabs .nav-item {
  font-size: 16px;
  font-weight: 600;
}

#enhanced-table .table-header .table-tabs .nav-tabs .nav-link {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom: none;
}

/**
table-header > props-multi-select
*/
#enhanced-table .table-header .props-multi-select {
  font-size: 16px;
  z-index: 1090;
}

#enhanced-table .table-header .props-multi-select .select__placeholder {
  font-size: 16px;
}

#enhanced-table .table-header .props-multi-select .select__value-container {
  margin: 3px auto;
}

#enhanced-table .table-header .props-multi-select .select__multi-value {
  border-radius: 4px;
  margin: 2px 4px;
  padding: 2px;
  color: #222;
}

/**
table-header >  table-names-list
*/
#enhanced-table .nav-tabs {
  border-bottom: 1px solid #dee2e6;
}

#enhanced-table .nav-tabs>li:first-child {
  padding-left: 0;
}

#enhanced-table .nav-tabs>li>a:hover, #enhanced-table .nav-tabs>li>a:focus, #enhanced-table .nav-tabs>li>a.active {
  color: #495057 !important;
  background-color: #fff !important;
  border-color: #dee2e6 #dee2e6 #fff !important;
}

#enhanced-table .table-header .table-names-list {
  padding: 10px 4px;
  width: 100%;
  background-color: white;
  border-radius: 0 4px 4px 4px;
  text-align: left;
  margin-left: 1px;
}

#enhanced-table .table-header .table-names-list .label {
  display: inline-block;
  border: 1.2px solid black;
  border-radius: 12px;
  padding: 4px 6px;
  margin: 4px 6px;
  cursor: pointer;
  background-color: transparent;
  font-size: 14px;
  user-select: none !important;
}

#enhanced-table .table-header .table-names-list .label:hover, #enhanced-table .table-header .table-names-list .label.active {
  background-color: rgba(189, 33, 27, 0.6);
  color: #444 !important;
}

/**
table-main
*/
#enhanced-table .table-main {
  z-index: 1080;
  width: 100%;
}

#enhanced-table .table-main .tools {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  margin-top: -8px !important;
  float: none;
}

#enhanced-table .table-main .tools button.btn {
  margin-top: -0px;
  height: 36px !important;
  margin-left: 0px;
  margin-right: 10px;
  background: #fff;
  border: 1px solid #e7eaec;
}

#enhanced-table .table-main .tools .footer-nav {
  display: inline-block;
  margin-top: -2px;
  margin-left: 10px;
}

#enhanced-table .table-main .tools .footer-nav .page-link {
  height: 38px;
  line-height: 28px;
}

#enhanced-table .table-main .tools .basic-select-footer {
  display: block;
  margin-top: -2px;
  width: 100px;
  margin-left: 10px;
}

#enhanced-table .table-main .tools .select__value-container.select__value-container--has-value {
  font-size: 16px !important;
}

#enhanced-table .table-main .tools .page-link {
  font-size: 16px !important;
}

#enhanced-table .table-main .tools button.btn .btn-title {
  margin-top: -4px;
  font-size: 80%;
}

#enhanced-table .table-main .tools button.btn .btn-sub-title {
  font-size: 60%;
}

#enhanced-table .table-main .tools button.btn.btn-success {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4;
}

#enhanced-table .table-main .tools button.btn.btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

#enhanced-table .table-main .tools button.btn.btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

#enhanced-table .table-main .react-grid-HeaderCell-sortable {
  font-size: 14px;
}

#enhanced-table .table-main .react-grid-Cell__value div span {
  font-size: 13px;
}

#enhanced-table .table-main .react-grid-Canvas {
  overflow: auto !important;
}

#enhanced-table .table-main .react-grid-Container {
  width: 100%;
}

#enhanced-table .table-main .prop-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

#enhanced-table .table-main .prop-item .item-name {
  display: block;
  opacity: 0.9;
}

#enhanced-table .table-main .prop-item .item-cog {
  display: block;
  cursor: pointer;
  opacity: 0.9;
  margin-left: 10px;
  margin-right: 10px;
}

#enhanced-table .table-main .prop-item .item-cog:hover {
  opacity: 1;
}

#enhanced-table .table-main .prop-item input.form-control {
  margin-top: -8px;
}

#enhanced-table .table-main .item-list {
  /* height: 200px; */
  width: 140px;
  /* background-color: #fff; */
  position: absolute;
  top: 180px;
  left: 40px;
  z-index: 9999;
  /* border-radius: 4px; */
  cursor: pointer;
}

#enhanced-table .table-main .item-list .list-group-item:hover {
  background-color: #eee;
}

#enhanced-table .table-main .item-list .list-group-item .icon {
  margin-right: 14px;
}

#enhanced-table .table-main .formatter-image {
  display: inline;
}

#enhanced-table .table-main .formatter-image .image {
  height: 32px;
  text-align: center;
  width: 100%;
  background-repeat: no-repeat;
  background-size: contain;
  background-position-x: 50%;
}

#enhanced-table .table-main .cell-item {
  cursor: text;
}

#enhanced-table .table-main .cell-item.disable {
  pointer-events: none;
  cursor: not-allowed;
}

#enhanced-table .table-main .tools .actions-select {
  order: -1;
  cursor: pointer;
  border: none !important;
  text-decoration: none !important;
  display: flex;
}

#enhanced-table .table-main .tools .actions-select .fa {
  font-size: 28px;
  padding-right: 4px;
}

#enhanced-table .table-main .tools .more-btn {
  display: block ;
}

#enhanced-table .table-main .tools .more-actions, #enhanced-table .table-main .tools .more-actions.hide {
  display: none;
}

#enhanced-table .table-main .tools .more-actions.show {
  display: flex;
  flex-direction: column;
  position: absolute;
  z-index: 1080;
  background-color: #FFF;
  padding: 0 10px 10px 10px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid rgba(0,0,0,0.2);
}

#enhanced-table .table-main .tools .more-actions.show button.btn {
  margin: 10px 0 0 0 !important;
}

/** For open as window */
.window-app, .window-app #enhanced-table {
  width: 100% ;
  height: 100%;
  margin: 0 ;
}

.window-app #enhanced-table .header-actions .action-open-window, .window-app #enhanced-table .header-actions .action-close {
  display: none;
}

#visual-query-builder {
  z-index: 1500;
  position: fixed;
  width: 80%;
  height: 80%;
  border: 1px solid #adff2f;
}
