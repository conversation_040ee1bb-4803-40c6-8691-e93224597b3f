package edu.memphis.ccrg.lida.framework.shared.scene;

import edu.memphis.ccrg.lida.framework.shared.*;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class WorldStructure {

    private ConcurrentMap<Integer, Node> snodes = new ConcurrentHashMap<Integer, Node>();

    private ConcurrentMap<ExtendedId, Link> slinks = new ConcurrentHashMap<ExtendedId, Link>();

    private ConcurrentMap<Linkable, Set<Link>> slinkableMap = new ConcurrentHashMap<Linkable, Set<Link>>();

    private NodeStructure ns;

   public WorldStructure(){

   }

    public ConcurrentMap<Integer, Node> getSnodes() {
        return snodes;
    }

    public void setSnodes(ConcurrentMap<Integer, Node> snodes) {
        this.snodes = snodes;
    }

    public ConcurrentMap<ExtendedId, Link> getSlinks() {
        return slinks;
    }

    public void setSlinks(ConcurrentMap<ExtendedId, Link> slinks) {
        this.slinks = slinks;
    }

    public ConcurrentMap<Linkable, Set<Link>> getSlinkableMap() {
        return slinkableMap;
    }

    public void setSlinkableMap(ConcurrentMap<Linkable, Set<Link>> slinkableMap) {
        this.slinkableMap = slinkableMap;
    }

    public NodeStructure getNs() {
        return ns;
    }

    public void setNs(NodeStructure ns) {
        this.ns = ns;
    }
}
