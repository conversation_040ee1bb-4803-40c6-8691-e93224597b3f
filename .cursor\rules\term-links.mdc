---
description:
globs:
alwaysApply: false
---
# 术语链接指南

## TermLink类型
- `COMPOUND` - 复合术语链接
- `COMPOUND_STATEMENT` - 复合语句链接
- `COMPOUND_CONDITION` - 复合条件链接
- `TRANSFORM` - 转换链接

## 链接构建规则

### 基本规则
1. 变量术语不创建链接
2. 复合术语需要递归处理其组件
3. 图像和乘积术语需要特殊处理

### 链接层级
- 第一层：直接组件链接
- 第二层：复合组件内部链接
- 第三层：图像/乘积内部链接

## 使用示例

### 创建基本链接
```java
TermLink link = new TermLink(TermLink.COMPOUND, term, index);
```

### 创建转换链接
```java
TermLink transformLink = new TermLink(TermLink.TRANSFORM, term, i, j, k);
```

### 准备组件链接
```java
List<TermLink> links = new ArrayList<>();
Terms.prepareComponentLinks(links, compoundTerm);
```

## 注意事项
- 链接类型必须与术语类型匹配
- 转换链接需要正确的索引信息
- 组件链接的构建需要考虑术语的层级结构
