package com.warmer.kgmaker;

import org.neo4j.configuration.connectors.BoltConnector;
import org.neo4j.configuration.connectors.HttpConnector;
import org.neo4j.configuration.connectors.HttpsConnector;
import org.neo4j.configuration.helpers.SocketAddress;
import org.neo4j.dbms.api.DatabaseManagementService;
import org.neo4j.dbms.api.DatabaseManagementServiceBuilder;
import org.neo4j.graphdb.GraphDatabaseService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.io.File;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

import static org.neo4j.configuration.GraphDatabaseSettings.DEFAULT_DATABASE_NAME;

@SpringBootApplication
public class KgmakerApplication {
//	public static int actionId = 0;
//	public static PerceptualAssociativeMemory pam;

	public static List<String> message = new ArrayList<>();
	public static List<String> words = new ArrayList<>();

	//	public static DatabaseManagementService managementService = null;

//	public static String filename0 = "lida-language\\graph.db";
//	public static String filename1 = "data-20210618\\graph.db";

	//	public static String filename = "D:\\data\\" + filename1;
	//D:\soft\neo4j-community-4.3.2\data\databases\graph.db
//	public static String filename = "D:\\lida\\data";
//	public static String filename = "D:\\soft\\neo4j-community-4.3.2";

	public static String filename0 = "D:\\lida\\data\\test\\data_neo5";
//	public static String filename1 = "D:\\downfile\\neo4j-community-5.16.0\\";
	public static String filename1 = "I:\\graph\\neo4j-community-5.16.0";
//	public static final File databaseDirectory0 = new File(filename);

//	public static GraphDatabaseService graphDb = new GraphDatabaseFactory().newEmbeddedDatabase(databaseDirectory);
//	public static GraphDatabaseService graphDb = new DatabaseManagementServiceBuilder(databaseDirectory)
//														.build().database(DEFAULT_DATABASE_NAME);

	public static Path databaseDirectory = FileSystems.getDefault().getPath(filename1, "");

	public static DatabaseManagementService managementService = new DatabaseManagementServiceBuilder(databaseDirectory)
			.setConfig(BoltConnector.enabled, true)
			// 没7687，则graphxr没法保存图谱数据
			.setConfig( BoltConnector.listen_address, new SocketAddress( "localhost",  7687))//7474
//			.setConfig(HttpConnector.enabled, true)
//			.setConfig( HttpConnector.listen_address, new SocketAddress( "localhost",  7474))
			.build();

	public static GraphDatabaseService graphDb = managementService.database( DEFAULT_DATABASE_NAME );

	public static void main(String[] args) throws Exception {
		System.setProperty("java.awt.headless", "false");
		System.setProperty("spring.devtools.restart.enabled", "false");

		registerShutdownHook( managementService );

		SpringApplication.run(KgmakerApplication.class, args);

//		AgentStarter.main(args);
	}

	private static void registerShutdownHook(final DatabaseManagementService managementService) {
		// Registers a shutdown hook for the Neo4j instance so that it
		// shuts down nicely when the VM exits (even if you "Ctrl-C" the
		// running example before it's completed)
		// 为 Neo4j 实例注册一个关闭钩子，以便它在 VM 退出时很好地关闭（即使你在它完成之前“Ctrl-C”运行示例）
		Runtime.getRuntime().addShutdownHook(new Thread() {
			@Override
			public void run() {
				managementService.shutdown();
			}
		});
	}

}
