/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.alifeagent.featuredetectors;

import com.warmer.kgmaker.KgmakerApplication;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.pam.tasks.MultipleDetectionAlgorithm;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

public class ListenDetector extends MultipleDetectionAlgorithm {
	private final String modality = "";
//	private Map<String, Object> detectorParams = new HashMap<String, Object>();

	private NodeStructure nodeStructure = new NodeStructureImpl();

	private String message;

	Set<String> mmcache0 = PamImpl0.gmcache;

	@Override
	public void init(){
		super.init();
//		detectorParams.put("mode","listen");
	}
	
	@Override
	public void detectLinkables() {
//		String value = (String) sensoryMemory.getSensoryContent(modality, detectorParams);

//		sensorParam.put("mode","listen");
//		message = (String) environment.getState(sensorParam);

		if (KgmakerApplication.message.size() > mmcache0.size() ) {
			System.out.println("听到未查看信息" + KgmakerApplication.message.get(mmcache0.size()));
			// todo 后期要识别说话对象，数据多维度，不仅仅是字符串，注意加持=提高感知激活
			message = KgmakerApplication.message.get(mmcache0.size());

			// 过多重复激活。但只有一轮激活会弱化，后面无法取到已激活数据
//			if (PamImpl0.mmcache2.contains(message)) {
//				return;
//			}
			KgmakerApplication.message.remove(0);
		}else {
//			return;
		}

		// 听到各种声音，与听到语言不同，这里已经识别出语言词语，理应还有原音识别，图像识别类似，语音buffer与其他声音独立
		// todo 原子声音结构识别，类似语言结构识别，构建到词语阶段=识别到有语音=有语句，输入语言模块，分阶段，可共用结构分析
		// 音色、频率等，类似图像边缘骨架，特定属性分析，可尝试原子点边化，也可用现有分析算法，只要不用神经网络

		// 听到和看到语言文字，放同一个语言buffer？no，声音，语音，图像，字形buffer，文本（独立与视听），整合buffer
		// 感知模态各自结构分析，原子感知信息，激活概念和场景，框架构建完整，然后模态两两组合？到大整合
		// todo 符号交流也有各种环境信息，如交流对象。这里应有听觉模态推理，pam即是整合扩散？pam也是一个特殊结构分析
		// 各模态内部结构分析，只到概念的特定感知属性层次，概念到相关，如其他属性、时序、蕴含等，需联合扩散，公共扩散逻辑=pam

		// 对主人尊敬+爱戴--》维护+祝福+顺从
		// 逐字听到=循环即是逐字，能不能理解=意象都是一样的，理解=信息齐全=概念+搭配+来龙去脉
		// 意象与理解分离，忠实展现意象+尽量完善属性，知道自己理解=能联想+能预测+能描述属性
		// 每个字词都附属于特定说话者，字词被识别出来，说话者也识别出来了，并行且同时，可再提前？
		// 针对听者，在这阶段应该还没被识别，需要更深入的理解
		// 连成一句话 = 已有场景即时激活，无场景 = 尝试创建新场景 = 借助类似场景
		if(message != null && !"".equals(message)){
//			excite("listen", 0.82, "listen");
//			PamNode msg = new PamNodeImpl();
//			msg.setName(message);
//			pam.receiveExcitation(msg, 0.85, "listen");

			System.out.println("听到---------------" + message);
			excite(message, 0.95, "listen");
			excite("(*," + message + ",听到)", 0.95, "listen");

			//todo 后期构建自传体系，把听到的信息存储在自传体系中，以便后期的提高感知激活
//			excite("柯东", 0.93, "listen");

			// 意象本体+意象内容+意象属性=声音本身+声音内容+声音来源

			message = "";
//			excite("说", 0.93, "listen");

//			PamNode person = new PamNodeImpl();
//			person.setName("主人");
//			pam.receiveExcitation(person, 0.73, "listen");

//			PamNode action = new PamNodeImpl();
//			action.setName("说");
//			pam.receiveExcitation(action, 0.73, "listen");

//			PamNode person1 = new PamNodeImpl();
//			person1.setName("小柯");
//			pam.receiveExcitation(person1, 0.73, "listen");
		}

		// 将【运算，25，加，8】逐个激活
		List<String> words = new ArrayList<>();
		String ll = "";

		AgentStarter.inputQueueStr = "";

		words.add("运算");
		words.add("26"); // 得数34好区分
		words.add("加");
		words.add("8");

		ll = "听到";

//		words = Arrays.asList(
//				"教你","运算","26","加","8","，",
//				"第一步","从","个位数","开始","，",
//				"第二步","26","个位数","是","6","，",
//				"8","个位数","是","8","，",
//				"6","加","8","等于","14");

		// 将“教你运算26加8”、“第一步从个位数开始”、“第二步26个位数是6”、“8个位数是8”、“6加8等于14”逐个激活
		// 不用首先然后，用步数，因为有循环，需跳转回某步骤，步数好跳转。也不能没有次序，否则无法跳转
		// 概述时序=上位时序：计算多位数加法，先从个位数开始，然后循环，每一步都记录当前进位，和当前位数，然后计算

		// 要构建参数表，多种情况都要能构建：一个案例+参数表说明，多个案例+无说明=抽取共性，直接变量式描述=无案例
		// 单个案例，没有参数表说明，也可抽象出变量，但复杂困难，不如单案例+说明。直接变量式描述？多个案例太多输入
		// 多案例，需要足够多样，才能抽取完善时序，否则有些分支在判断和跳转时隐藏了，变量式最完善

		// 需要背景知识，方法名，表达式，参数名，各概念内涵，等。变量式描述简洁，包含多种信息，但不太像人话，先做一版
		// 操作+参数+返回=在同一变量句。初始值，输入参数，返回值，内部参数，全局参数。需要多种后天认知辨别
		// 截取$加数1$当前位数得到$数一数，截取=^cutstr，得到=返回赋值，动词，不是对应底层操作，就是对应后天时序
		// 构建、调用并输出时，保留“截取”，执行时，隐式调用底层操作api即可，不输出底层，人类调用底层也是内隐，不可知
//		words = Arrays.asList(
//				"教你","运算","$加数1","加","$加数2","，",
//				"第一步","，",
//						"从","个位数","开始","，",
//						"$当前位数","为","个位数","，",
//						"$当前进位","为","0","，",
//				"第二步","，",
//						"截取","$加数1","个位数","求得","$数一数","，",
//						"截取","$加数2","个位数","求得","$数二数","，",
//						"$数一数","加","$数二数","求得","$一二和","，");

		// todo 纯nl，无变量标记
		// todo 长句学习暂缓：需要分段，句式词组繁多，llm可抽取时序=替代？
		// ------时序执行------:
		// 	nars_7-判断-(&/,(&/,(*,$数一数,加上,$当前进位,求得,(^addd,$数一数,$当前进位)),nars_4),(*,(*,得数,$位数),$数一数))
		//	(*,$数二数,为,0)--条件1--(&/,(&/,(*,$数一数,加上,$当前进位,求得,(^addd,$数一数,$当前进位)),nars_4),(*,(*,得数,$位数),$数一数))
		//	(*,$数一数,不为,0)--条件2--(&/,(&/,(*,$数一数,加上,$当前进位,求得,(^addd,$数一数,$当前进位)),nars_4),(*,(*,得数,$位数),$数一数))
		//	且--关系12--(&/,(&/,(*,$数一数,加上,$当前进位,求得,(^addd,$数一数,$当前进位)),nars_4),(*,(*,得数,$位数),$数一数))
//		words = Arrays.asList(
//		"教你","运算","加数1","加","加数2","，",
//		"第一步","，",
//				"从","个位数","开始","，",
//				"当前位数","为","个位数","，",
//				"当前进位","为","0","，",
//		"第二步","，",
//				"截取","加数1","个位数","求得","数一数","，",
//				"截取","加数2","个位数","求得","数二数","，",
//				"数一数","加","数二数","求得","一二和","，"
//				);


		// 条件标志词整合，则+如果=则如果，（如果，$条件集1，则，如果，$条件集11，则，$结果时序），底层不变，认知绑定可变
		// 分离式连续判断=每个判断单独一句，连体式连续判断=一个整体=一句，条件之间关联，互斥，参考编程语言
		// 连体式大白话：看条件是否成立，是就做某事，不是的话再看其他条件，再不行就只能else，else就是否=单句非
		// 判断结构建模，就是普通与或非结构？只有ifelse，没有elseif，也可达到同样效果，只是将条件分开了，形式不同
		// 单句与=分离式连续判断=每分句都执行，单句或=连体式连续判断=分句互斥=只执行一个，从认知方面控制，互斥也是语义
		// 有elseif形式需按顺序，底层规则多了，elseif可能是后天习得，按最简原则，层层嵌套ifelse=多个条件集=多个结果时序
		// 最简ifelse=肯定顺承+否定顺承，（||，（$条件集1 =/> $结果时序），（$条件集1否定 =/> $结果时序））

		//nlp录入案例（初期有前缀版本）
//		words.add("1_1_苹果");
//		words.add("1_1_去");
//		words.add("1_1_哪里");
//		words.add("1_1_了");

//		words.add("苹果");
//		words.add("去");
//		words.add("哪里");
//		words.add("了");
//
//		ll = "听到1";// 苹果去哪案例，对话窗口发来暂时不奏效

//		words.add("是");
//		words.add("什么");// 水果、手机、电影等

		excite(ll, 0.95, "listen0");

		// 先关注时态、字符模态，主体和对象等先不管，都是主人和我
		for (String word : words) {
			System.out.println("----------测试听到---------------" + word);
			excite(word, 0.95, "listen0");

			AgentStarter.inputQueueStr += word;

//			excite("(*," + ll + "," + word + ")", 0.95, "listen");
		}
	}
}
