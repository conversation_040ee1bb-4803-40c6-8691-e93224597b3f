package com.warmer.kgmaker.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/api")
public class XR0Controller {

	@ResponseBody
	@RequestMapping("/tempModule/tempModules")
	public Map<String, Object> getInfo(HttpServletRequest request) {
		Map mm = request.getParameterMap();
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/grovePinned/getWithProjectId")
	public Map<String, Object> getWithProjectId(HttpServletRequest request) {
		Map mm = request.getParameterMap();
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/perspectives/getWithProjectId")
	public Map<String, Object> getWithProjectId0(HttpServletRequest request) {
		Map mm = request.getParameterMap();
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/project/connect")
	public Map<String, Object> connect(HttpServletRequest request) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/project/getProjectSettings")
	public Map<String, Object> getProjectSettings(HttpServletRequest request) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@RequestMapping("/getnlpword")
	@ResponseBody
	public Map<String, Object> query(HttpServletRequest request){
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

}
