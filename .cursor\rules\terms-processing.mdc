---
description:
globs:
alwaysApply: false
---
# 术语处理指南

## Terms类主要功能

### 术语比较
- `equalSubTermsInRespectToImageAndProduct()` - 比较两个术语在图像和乘积方面的等价性
- `equalSubjectPredicateInRespectToImageAndProduct()` - 比较语句的主语和谓语在图像和乘积方面的等价性

### 术语转换
- `reduceUntilLayer2()` - 将复合术语简化到第二层
- `reduceComponentOneLayer()` - 将复合术语简化一层
- `unwrapNegation()` - 去除否定包装

### 术语构建
- `term()` - 从模板和组件列表构建复合术语
- `prepareComponentLinks()` - 准备术语链接模板

## 使用示例

### 构建复合术语
```java
Term[] components = new Term[]{term1, term2};
Term compound = Terms.term(Symbols.NativeOperator.CONJUNCTION, components);
```

### 简化术语
```java
Term simplified = Terms.reduceComponentOneLayer(compoundTerm, component, memory);
```

### 比较术语
```java
boolean areEqual = Terms.equalSubTermsInRespectToImageAndProduct(term1, term2);
```

## 注意事项
- 术语操作前必须进行非空检查
- 复合术语的组件必须是有序的
- 术语链接的构建需要考虑类型和层级
