/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Dec 21 01:52:39 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.gui.commands.Command;
import edu.memphis.ccrg.lida.framework.gui.commands.SetTimeScaleCommand;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ControlToolBarPanel extends GuiPanelImpl {
    private boolean isPaused = true;
    private int tickDurationStartValue = 1;
    private TaskManager tm;
    private final String PAUSED_LABEL = " Paused ";
    private final String RUNNING_LABEL = " Running ";

    /** Creates new form ControlToolBarPanel */
    public ControlToolBarPanel() {
        initComponents();
    }

    @Override
    public void initPanel(String[] params) {
        tm = agent.getTaskManager();
        tickDurationStartValue = tm.getTickDuration();
        tickDurationSpinner.setValue(tickDurationStartValue);
    }

    private void tickDurationSpinnerStateChanged(javax.swing.event.ChangeEvent evt) {//GEN-FIRST:event_tickDurationSpinnerStateChanged
        int tickDuration = (Integer) tickDurationSpinner.getValue();
        if (tickDuration >= 0) {
            // Another way to execute commands
            Command command = new SetTimeScaleCommand();
            command.setParameter("tickDuration", tickDuration);
            controller.executeCommand(command);
            refresh();
        }
    }//GEN-LAST:event_tickDurationSpinnerStateChanged

    /*
     * Sends pauseRunningThreads and resumeRunningThreads commands
     * @param evt
     */
    private void startPauseButtonActionPerformed(java.awt.event.ActionEvent evt) {
        isPaused = !isPaused;
        if (isPaused) {
            statusLabel.setText(PAUSED_LABEL);
            controller.executeCommand("pauseRunningThreads", null);
        } else {
            statusLabel.setText(RUNNING_LABEL);
            controller.executeCommand("resumeRunningThreads", null);
        }
        refresh();
    }

	/*
	 * Adds ticks for execution during ticks mode. using AddTicksCommand
	 * 
	 * @param evt
	 */
	private void addTicksButtonActionPerformed(java.awt.event.ActionEvent evt) {
		if(!ticksModeTB.isSelected()){
			ticksModeTB.setSelected(true);
			executeTicksModeCommand();
		}
		
		Map<String, Object> parameters = new HashMap<String, Object>();
		int ticks = 0;
		try {
			ticks = Integer.parseInt(tiksTB.getText());
		} catch (NumberFormatException e) {
		}
		parameters.put("ticks", ticks);
		controller.executeCommand("AddTicks", parameters);

		if (isPaused) {
			isPaused = !isPaused;
			statusLabel.setText(RUNNING_LABEL);
			controller.executeCommand("resumeRunningThreads", null);
		}
	}

	/*
	 * Toggles the TaskManager's ticks mode using the EnableTicksMode command.
	 * 
	 * @param evt
	 */
	private void ticksModeTBActionPerformed(java.awt.event.ActionEvent evt) {
		executeTicksModeCommand();
	}
	private void executeTicksModeCommand(){
		Map<String, Object> parameters = new HashMap<String, Object>();
		parameters.put("enable", ticksModeTB.isSelected());
		controller.executeCommand("EnableTicksMode", parameters);
	}

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        toolbar = new JToolBar();
        startPauseButton = new JButton();
        statusLabel = new JLabel();
        jLabel3 = new JLabel();
        currentTickTextField = new JTextField();
        ticksModeTB = new JToggleButton();
        tiksTB = new JTextField();
        addTicksButton = new JButton();
        jLabel2 = new JLabel();
        tickDurationSpinner = new JSpinner();
        jPanel1 = new JPanel();

        //======== this ========
        setLayout(new BorderLayout());

        //======== toolbar ========
        {
            toolbar.setRollover(true);
            toolbar.setPreferredSize(new Dimension(50, 25));

            //---- startPauseButton ----
            startPauseButton.setFont(new Font("Lucida Grande", Font.PLAIN, 12));
            startPauseButton.setText("Start / Pause ");
            startPauseButton.setToolTipText("Toggles system operation");
            startPauseButton.setFocusable(false);
            startPauseButton.setHorizontalTextPosition(SwingConstants.CENTER);
            startPauseButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            startPauseButton.addActionListener(e -> startPauseButtonActionPerformed(e));
            toolbar.add(startPauseButton);

            //---- statusLabel ----
            statusLabel.setFont(new Font("Lucida Grande", Font.BOLD, 12));
            statusLabel.setHorizontalAlignment(SwingConstants.CENTER);
            statusLabel.setText(" Paused ");
            statusLabel.setToolTipText("System run status");
            toolbar.add(statusLabel);
            toolbar.addSeparator();

            //---- jLabel3 ----
            jLabel3.setFont(new Font("Lucida Grande", Font.PLAIN, 12));
            jLabel3.setText("  Current tick: ");
            toolbar.add(jLabel3);

            //---- currentTickTextField ----
            currentTickTextField.setEditable(false);
            currentTickTextField.setHorizontalAlignment(SwingConstants.TRAILING);
            currentTickTextField.setToolTipText("Current tick");
            currentTickTextField.setMaximumSize(new Dimension(100, 24));
            currentTickTextField.setMinimumSize(new Dimension(70, 24));
            currentTickTextField.setPreferredSize(new Dimension(70, 24));
            toolbar.add(currentTickTextField);
            toolbar.addSeparator();

            //---- ticksModeTB ----
            ticksModeTB.setFont(new Font("Lucida Grande", Font.PLAIN, 12));
            ticksModeTB.setText("Step mode");
            ticksModeTB.setToolTipText("Toggles step-by-step mode");
            ticksModeTB.setFocusable(false);
            ticksModeTB.setHorizontalTextPosition(SwingConstants.CENTER);
            ticksModeTB.setVerticalTextPosition(SwingConstants.BOTTOM);
            ticksModeTB.addActionListener(e -> ticksModeTBActionPerformed(e));
            toolbar.add(ticksModeTB);

            //---- tiksTB ----
            tiksTB.setHorizontalAlignment(SwingConstants.RIGHT);
            tiksTB.setText("0");
            tiksTB.setToolTipText("Enter a number of ticks here.  The system will run this number of ticks when adjacent 'Add' button is pressed and the system is in step mode.");
            tiksTB.setMaximumSize(new Dimension(100, 24));
            tiksTB.setMinimumSize(new Dimension(70, 24));
            tiksTB.setPreferredSize(new Dimension(60, 24));
            toolbar.add(tiksTB);

            //---- addTicksButton ----
            addTicksButton.setFont(new Font("Lucida Grande", Font.PLAIN, 12));
            addTicksButton.setText("Run ticks");
            addTicksButton.setToolTipText("Runs system the number of ticks specified in adjacent text field.");
            addTicksButton.setFocusable(false);
            addTicksButton.setHorizontalTextPosition(SwingConstants.CENTER);
            addTicksButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            addTicksButton.addActionListener(e -> addTicksButtonActionPerformed(e));
            toolbar.add(addTicksButton);
            toolbar.addSeparator();

            //---- jLabel2 ----
            jLabel2.setFont(new Font("Lucida Grande", Font.PLAIN, 12));
            jLabel2.setText("  Tick duration (ms): ");
            toolbar.add(jLabel2);

            //---- tickDurationSpinner ----
            tickDurationSpinner.setModel(new SpinnerNumberModel(1, 0, 1000, 1));
            tickDurationSpinner.setToolTipText("The system's current tick duration in milliseconds.");
            tickDurationSpinner.setMaximumSize(new Dimension(110, 24));
            tickDurationSpinner.setMinimumSize(new Dimension(63, 24));
            tickDurationSpinner.setPreferredSize(new Dimension(63, 24));
            tickDurationSpinner.addChangeListener(e -> tickDurationSpinnerStateChanged(e));
            toolbar.add(tickDurationSpinner);
            toolbar.addSeparator();

            //======== jPanel1 ========
            {
                jPanel1.setLayout(new FlowLayout());
            }
            toolbar.add(jPanel1);
        }
        add(toolbar, BorderLayout.CENTER);
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar toolbar;
    private JButton startPauseButton;
    private JLabel statusLabel;
    private JLabel jLabel3;
    private JTextField currentTickTextField;
    private JToggleButton ticksModeTB;
    private JTextField tiksTB;
    private JButton addTicksButton;
    private JLabel jLabel2;
    private JSpinner tickDurationSpinner;
    private JPanel jPanel1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables
}
