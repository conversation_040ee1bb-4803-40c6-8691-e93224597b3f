@font-face {
    font-family: graphxr-icons;
    src: url("graphxr-icons.woff2?d540d089") format("woff2"),
             url("graphxr-icons.woff?d540d089") format("woff"),
             url("graphxr-icons.ttf?d540d089") format("truetype"); 
    font-weight: normal;
    font-style: normal;
}

[class^="graphxr"], [class*=" graphxr"] {
    font-family: 'graphxr-icons' !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-style: normal;
}


.graphxr-accessible-icon::before {
    content: "\ea01";
}

.graphxr-accusoft::before {
    content: "\ea02";
}

.graphxr-acquisitions-incorporated::before {
    content: "\ea03";
}

.graphxr-adn::before {
    content: "\ea04";
}

.graphxr-adversal::before {
    content: "\ea05";
}

.graphxr-affiliatetheme::before {
    content: "\ea06";
}

.graphxr-airbnb::before {
    content: "\ea07";
}

.graphxr-algolia::before {
    content: "\ea08";
}

.graphxr-alipay::before {
    content: "\ea09";
}

.graphxr-amazon-pay::before {
    content: "\ea0a";
}

.graphxr-amazon::before {
    content: "\ea0b";
}

.graphxr-amilia::before {
    content: "\ea0c";
}

.graphxr-android::before {
    content: "\ea0d";
}

.graphxr-angellist::before {
    content: "\ea0e";
}

.graphxr-angrycreative::before {
    content: "\ea0f";
}

.graphxr-angular::before {
    content: "\ea10";
}

.graphxr-app-store-ios::before {
    content: "\ea11";
}

.graphxr-app-store::before {
    content: "\ea12";
}

.graphxr-apple::before {
    content: "\ea13";
}

.graphxr-artstation::before {
    content: "\ea14";
}

.graphxr-asymmetrik::before {
    content: "\ea15";
}

.graphxr-atlassian::before {
    content: "\ea16";
}

.graphxr-audible::before {
    content: "\ea17";
}

.graphxr-autoprefixer::before {
    content: "\ea18";
}

.graphxr-avianex::before {
    content: "\ea19";
}

.graphxr-bandcamp::before {
    content: "\ea1a";
}

.graphxr-battle-net::before {
    content: "\ea1b";
}

.graphxr-behance-square::before {
    content: "\ea1c";
}

.graphxr-bimobject::before {
    content: "\ea1d";
}

.graphxr-bitbucket::before {
    content: "\ea1e";
}

.graphxr-bitcoin::before {
    content: "\ea1f";
}

.graphxr-black-tie::before {
    content: "\ea20";
}

.graphxr-blackberry::before {
    content: "\ea21";
}

.graphxr-blogger-b::before {
    content: "\ea22";
}

.graphxr-blogger::before {
    content: "\ea23";
}

.graphxr-bluetooth-b::before {
    content: "\ea24";
}

.graphxr-bluetooth::before {
    content: "\ea25";
}

.graphxr-bootstrap::before {
    content: "\ea26";
}

.graphxr-btc::before {
    content: "\ea27";
}

.graphxr-buffer::before {
    content: "\ea28";
}

.graphxr-buromobelexperte::before {
    content: "\ea29";
}

.graphxr-buy-n-large::before {
    content: "\ea2a";
}

.graphxr-buysellads::before {
    content: "\ea2b";
}

.graphxr-canadian-maple-leaf::before {
    content: "\ea2c";
}

.graphxr-cc-amazon-pay::before {
    content: "\ea2d";
}

.graphxr-cc-amex::before {
    content: "\ea2e";
}

.graphxr-cc-apple-pay::before {
    content: "\ea2f";
}

.graphxr-cc-diners-club::before {
    content: "\ea30";
}

.graphxr-cc-discover::before {
    content: "\ea31";
}

.graphxr-cc-jcb::before {
    content: "\ea32";
}

.graphxr-cc-mastercard::before {
    content: "\ea33";
}

.graphxr-cc-paypal::before {
    content: "\ea34";
}

.graphxr-cc-stripe::before {
    content: "\ea35";
}

.graphxr-cc-visa::before {
    content: "\ea36";
}

.graphxr-centercode::before {
    content: "\ea37";
}

.graphxr-centos::before {
    content: "\ea38";
}

.graphxr-chrome::before {
    content: "\ea39";
}

.graphxr-chromecast::before {
    content: "\ea3a";
}

.graphxr-cloudflare::before {
    content: "\ea3b";
}

.graphxr-cloudscale::before {
    content: "\ea3c";
}

.graphxr-cloudsmith::before {
    content: "\ea3d";
}

.graphxr-cloudversify::before {
    content: "\ea3e";
}

.graphxr-codepen::before {
    content: "\ea3f";
}

.graphxr-codiepie::before {
    content: "\ea40";
}

.graphxr-confluence::before {
    content: "\ea41";
}

.graphxr-connectdevelop::before {
    content: "\ea42";
}

.graphxr-contao::before {
    content: "\ea43";
}

.graphxr-cotton-bureau::before {
    content: "\ea44";
}

.graphxr-cpanel::before {
    content: "\ea45";
}

.graphxr-creative-commons-by::before {
    content: "\ea46";
}

.graphxr-creative-commons-nc-eu::before {
    content: "\ea47";
}

.graphxr-creative-commons-nc-jp::before {
    content: "\ea48";
}

.graphxr-creative-commons-nc::before {
    content: "\ea49";
}

.graphxr-creative-commons-nd::before {
    content: "\ea4a";
}

.graphxr-creative-commons-pd-alt::before {
    content: "\ea4b";
}

.graphxr-creative-commons-pd::before {
    content: "\ea4c";
}

.graphxr-creative-commons-remix::before {
    content: "\ea4d";
}

.graphxr-creative-commons-sa::before {
    content: "\ea4e";
}

.graphxr-creative-commons-sampling-plus::before {
    content: "\ea4f";
}

.graphxr-creative-commons-sampling::before {
    content: "\ea50";
}

.graphxr-creative-commons-share::before {
    content: "\ea51";
}

.graphxr-creative-commons-zero::before {
    content: "\ea52";
}

.graphxr-creative-commons::before {
    content: "\ea53";
}

.graphxr-critical-role::before {
    content: "\ea54";
}

.graphxr-css3-alt::before {
    content: "\ea55";
}

.graphxr-css3::before {
    content: "\ea56";
}

.graphxr-cuttlefish::before {
    content: "\ea57";
}

.graphxr-d-and-d-beyond::before {
    content: "\ea58";
}

.graphxr-d-and-d::before {
    content: "\ea59";
}

.graphxr-dailymotion::before {
    content: "\ea5a";
}

.graphxr-dashcube::before {
    content: "\ea5b";
}

.graphxr-deezer::before {
    content: "\ea5c";
}

.graphxr-delicious::before {
    content: "\ea5d";
}

.graphxr-deploydog::before {
    content: "\ea5e";
}

.graphxr-deskpro::before {
    content: "\ea5f";
}

.graphxr-dev::before {
    content: "\ea60";
}

.graphxr-deviantart::before {
    content: "\ea61";
}

.graphxr-dhl::before {
    content: "\ea62";
}

.graphxr-diaspora::before {
    content: "\ea63";
}

.graphxr-digg::before {
    content: "\ea64";
}

.graphxr-digital-ocean::before {
    content: "\ea65";
}

.graphxr-discord::before {
    content: "\ea66";
}

.graphxr-discourse::before {
    content: "\ea67";
}

.graphxr-dochub::before {
    content: "\ea68";
}

.graphxr-docker::before {
    content: "\ea69";
}

.graphxr-draft2digital::before {
    content: "\ea6a";
}

.graphxr-dribbble-square::before {
    content: "\ea6b";
}

.graphxr-dribbble::before {
    content: "\ea6c";
}

.graphxr-dropbox::before {
    content: "\ea6d";
}

.graphxr-drupal::before {
    content: "\ea6e";
}

.graphxr-dyalog::before {
    content: "\ea6f";
}

.graphxr-earlybirds::before {
    content: "\ea70";
}

.graphxr-edge-legacy::before {
    content: "\ea71";
}

.graphxr-edge::before {
    content: "\ea72";
}

.graphxr-elementor::before {
    content: "\ea73";
}

.graphxr-ello::before {
    content: "\ea74";
}

.graphxr-empire::before {
    content: "\ea75";
}

.graphxr-envira::before {
    content: "\ea76";
}

.graphxr-erlang::before {
    content: "\ea77";
}

.graphxr-ethereum::before {
    content: "\ea78";
}

.graphxr-etsy::before {
    content: "\ea79";
}

.graphxr-evernote::before {
    content: "\ea7a";
}

.graphxr-expeditedssl::before {
    content: "\ea7b";
}

.graphxr-facebook-f::before {
    content: "\ea7c";
}

.graphxr-facebook-messenger::before {
    content: "\ea7d";
}

.graphxr-facebook-square::before {
    content: "\ea7e";
}

.graphxr-facebook::before {
    content: "\ea7f";
}

.graphxr-fantasy-flight-games::before {
    content: "\ea80";
}

.graphxr-fedex::before {
    content: "\ea81";
}

.graphxr-fedora::before {
    content: "\ea82";
}

.graphxr-figma::before {
    content: "\ea83";
}

.graphxr-firefox-browser::before {
    content: "\ea84";
}

.graphxr-firefox::before {
    content: "\ea85";
}

.graphxr-first-order-alt::before {
    content: "\ea86";
}

.graphxr-first-order::before {
    content: "\ea87";
}

.graphxr-firstdraft::before {
    content: "\ea88";
}

.graphxr-flickr::before {
    content: "\ea89";
}

.graphxr-flipboard::before {
    content: "\ea8a";
}

.graphxr-fly::before {
    content: "\ea8b";
}

.graphxr-font-awesome-alt::before {
    content: "\ea8c";
}

.graphxr-font-awesome-flag::before {
    content: "\ea8d";
}

.graphxr-font-awesome::before {
    content: "\ea8e";
}

.graphxr-fonticons-fi::before {
    content: "\ea8f";
}

.graphxr-fonticons::before {
    content: "\ea90";
}

.graphxr-fort-awesome-alt::before {
    content: "\ea91";
}

.graphxr-fort-awesome::before {
    content: "\ea92";
}

.graphxr-forumbee::before {
    content: "\ea93";
}

.graphxr-foursquare::before {
    content: "\ea94";
}

.graphxr-freebsd::before {
    content: "\ea95";
}

.graphxr-fulcrum::before {
    content: "\ea96";
}

.graphxr-galactic-republic::before {
    content: "\ea97";
}

.graphxr-galactic-senate::before {
    content: "\ea98";
}

.graphxr-get-pocket::before {
    content: "\ea99";
}

.graphxr-gg-circle::before {
    content: "\ea9a";
}

.graphxr-gg::before {
    content: "\ea9b";
}

.graphxr-git-alt::before {
    content: "\ea9c";
}

.graphxr-git-square::before {
    content: "\ea9d";
}

.graphxr-github-alt::before {
    content: "\ea9e";
}

.graphxr-github-square::before {
    content: "\ea9f";
}

.graphxr-github::before {
    content: "\eaa0";
}

.graphxr-gitkraken::before {
    content: "\eaa1";
}

.graphxr-gitlab::before {
    content: "\eaa2";
}

.graphxr-gitter::before {
    content: "\eaa3";
}

.graphxr-glide-g::before {
    content: "\eaa4";
}

.graphxr-glide::before {
    content: "\eaa5";
}

.graphxr-gofore::before {
    content: "\eaa6";
}

.graphxr-goodreads-g::before {
    content: "\eaa7";
}

.graphxr-goodreads::before {
    content: "\eaa8";
}

.graphxr-google-drive::before {
    content: "\eaa9";
}

.graphxr-google-play::before {
    content: "\eaaa";
}

.graphxr-google-plus-g::before {
    content: "\eaab";
}

.graphxr-google-plus-square::before {
    content: "\eaac";
}

.graphxr-google-plus::before {
    content: "\eaad";
}

.graphxr-google-wallet::before {
    content: "\eaae";
}

.graphxr-google::before {
    content: "\eaaf";
}

.graphxr-gratipay::before {
    content: "\eab0";
}

.graphxr-grav::before {
    content: "\eab1";
}

.graphxr-gripfire::before {
    content: "\eab2";
}

.graphxr-grunt::before {
    content: "\eab3";
}

.graphxr-guilded::before {
    content: "\eab4";
}

.graphxr-gulp::before {
    content: "\eab5";
}

.graphxr-hacker-news-square::before {
    content: "\eab6";
}

.graphxr-hacker-news::before {
    content: "\eab7";
}

.graphxr-hackerrank::before {
    content: "\eab8";
}

.graphxr-hips::before {
    content: "\eab9";
}

.graphxr-hire-a-helper::before {
    content: "\eaba";
}

.graphxr-hive::before {
    content: "\eabb";
}

.graphxr-hooli::before {
    content: "\eabc";
}

.graphxr-hornbill::before {
    content: "\eabd";
}

.graphxr-hotjar::before {
    content: "\eabe";
}

.graphxr-houzz::before {
    content: "\eabf";
}

.graphxr-html5::before {
    content: "\eac0";
}

.graphxr-hubspot::before {
    content: "\eac1";
}

.graphxr-ideal::before {
    content: "\eac2";
}

.graphxr-imdb::before {
    content: "\eac3";
}

.graphxr-innosoft::before {
    content: "\eac4";
}

.graphxr-instagram-square::before {
    content: "\eac5";
}

.graphxr-instagram::before {
    content: "\eac6";
}

.graphxr-instalod::before {
    content: "\eac7";
}

.graphxr-intercom::before {
    content: "\eac8";
}

.graphxr-internet-explorer::before {
    content: "\eac9";
}

.graphxr-invision::before {
    content: "\eaca";
}

.graphxr-ioxhost::before {
    content: "\eacb";
}

.graphxr-itch-io::before {
    content: "\eacc";
}

.graphxr-itunes-note::before {
    content: "\eacd";
}

.graphxr-itunes::before {
    content: "\eace";
}

.graphxr-java::before {
    content: "\eacf";
}

.graphxr-jedi-order::before {
    content: "\ead0";
}

.graphxr-jenkins::before {
    content: "\ead1";
}

.graphxr-jira::before {
    content: "\ead2";
}

.graphxr-joget::before {
    content: "\ead3";
}

.graphxr-joomla::before {
    content: "\ead4";
}

.graphxr-js-square::before {
    content: "\ead5";
}

.graphxr-js::before {
    content: "\ead6";
}

.graphxr-jsfiddle::before {
    content: "\ead7";
}

.graphxr-kaggle::before {
    content: "\ead8";
}

.graphxr-keybase::before {
    content: "\ead9";
}

.graphxr-keycdn::before {
    content: "\eada";
}

.graphxr-kickstarter-k::before {
    content: "\eadb";
}

.graphxr-kickstarter::before {
    content: "\eadc";
}

.graphxr-korvue::before {
    content: "\eadd";
}

.graphxr-laravel::before {
    content: "\eade";
}

.graphxr-lastfm-square::before {
    content: "\eadf";
}

.graphxr-lastfm::before {
    content: "\eae0";
}

.graphxr-leanpub::before {
    content: "\eae1";
}

.graphxr-line::before {
    content: "\eae2";
}

.graphxr-linkedin::before {
    content: "\eae3";
}

.graphxr-linode::before {
    content: "\eae4";
}

.graphxr-linux::before {
    content: "\eae5";
}

.graphxr-lyft::before {
    content: "\eae6";
}

.graphxr-magento::before {
    content: "\eae7";
}

.graphxr-mailchimp::before {
    content: "\eae8";
}

.graphxr-mandalorian::before {
    content: "\eae9";
}

.graphxr-markdown::before {
    content: "\eaea";
}

.graphxr-mastodon::before {
    content: "\eaeb";
}

.graphxr-medapps::before {
    content: "\eaec";
}

.graphxr-medium-m::before {
    content: "\eaed";
}

.graphxr-medium::before {
    content: "\eaee";
}

.graphxr-medrt::before {
    content: "\eaef";
}

.graphxr-meetup::before {
    content: "\eaf0";
}

.graphxr-megaport::before {
    content: "\eaf1";
}

.graphxr-mendeley::before {
    content: "\eaf2";
}

.graphxr-microblog::before {
    content: "\eaf3";
}

.graphxr-microsoft::before {
    content: "\eaf4";
}

.graphxr-mix::before {
    content: "\eaf5";
}

.graphxr-mixcloud::before {
    content: "\eaf6";
}

.graphxr-mixer::before {
    content: "\eaf7";
}

.graphxr-mizuni::before {
    content: "\eaf8";
}

.graphxr-modx::before {
    content: "\eaf9";
}

.graphxr-monero::before {
    content: "\eafa";
}

.graphxr-napster::before {
    content: "\eafb";
}

.graphxr-no-icon::before {
    content: "\eafc";
}

.graphxr-nutritionix::before {
    content: "\eafd";
}

.graphxr-octopus-deploy::before {
    content: "\eafe";
}

.graphxr-odnoklassniki-square::before {
    content: "\eaff";
}

.graphxr-odnoklassniki::before {
    content: "\eb00";
}

.graphxr-old-republic::before {
    content: "\eb01";
}

.graphxr-opencart::before {
    content: "\eb02";
}

.graphxr-opera::before {
    content: "\eb03";
}

.graphxr-optin-monster::before {
    content: "\eb04";
}

.graphxr-orcid::before {
    content: "\eb05";
}

.graphxr-osi::before {
    content: "\eb06";
}

.graphxr-page4::before {
    content: "\eb07";
}

.graphxr-pagelines::before {
    content: "\eb08";
}

.graphxr-palfed::before {
    content: "\eb09";
}

.graphxr-patreon::before {
    content: "\eb0a";
}

.graphxr-paypal::before {
    content: "\eb0b";
}

.graphxr-perbyte::before {
    content: "\eb0c";
}

.graphxr-periscope::before {
    content: "\eb0d";
}

.graphxr-phabricator::before {
    content: "\eb0e";
}

.graphxr-phoenix-framework::before {
    content: "\eb0f";
}

.graphxr-phoenix-squadron::before {
    content: "\eb10";
}

.graphxr-php::before {
    content: "\eb11";
}

.graphxr-pied-piper-alt::before {
    content: "\eb12";
}

.graphxr-pied-piper-pp::before {
    content: "\eb13";
}

.graphxr-pied-piper-square::before {
    content: "\eb14";
}

.graphxr-pied-piper::before {
    content: "\eb15";
}

.graphxr-pinterest::before {
    content: "\eb16";
}

.graphxr-playstation::before {
    content: "\eb17";
}

.graphxr-product-hunt::before {
    content: "\eb18";
}

.graphxr-pushed::before {
    content: "\eb19";
}

.graphxr-python::before {
    content: "\eb1a";
}

.graphxr-qq::before {
    content: "\eb1b";
}

.graphxr-quinscape::before {
    content: "\eb1c";
}

.graphxr-quora::before {
    content: "\eb1d";
}

.graphxr-r-project::before {
    content: "\eb1e";
}

.graphxr-raspberry-pi::before {
    content: "\eb1f";
}

.graphxr-ravelry::before {
    content: "\eb20";
}

.graphxr-react::before {
    content: "\eb21";
}

.graphxr-reacteurope::before {
    content: "\eb22";
}

.graphxr-readme::before {
    content: "\eb23";
}

.graphxr-rebel::before {
    content: "\eb24";
}

.graphxr-red-river::before {
    content: "\eb25";
}

.graphxr-reddit-alien::before {
    content: "\eb26";
}

.graphxr-reddit-square::before {
    content: "\eb27";
}

.graphxr-reddit::before {
    content: "\eb28";
}

.graphxr-redhat::before {
    content: "\eb29";
}

.graphxr-renren::before {
    content: "\eb2a";
}

.graphxr-replyd::before {
    content: "\eb2b";
}

.graphxr-researchgate::before {
    content: "\eb2c";
}

.graphxr-resolving::before {
    content: "\eb2d";
}

.graphxr-rev::before {
    content: "\eb2e";
}

.graphxr-rocketchat::before {
    content: "\eb2f";
}

.graphxr-rockrms::before {
    content: "\eb30";
}

.graphxr-rust::before {
    content: "\eb31";
}

.graphxr-safari::before {
    content: "\eb32";
}

.graphxr-salesforce::before {
    content: "\eb33";
}

.graphxr-sass::before {
    content: "\eb34";
}

.graphxr-schlix::before {
    content: "\eb35";
}

.graphxr-scribd::before {
    content: "\eb36";
}

.graphxr-searchengin::before {
    content: "\eb37";
}

.graphxr-sellcast::before {
    content: "\eb38";
}

.graphxr-sellsy::before {
    content: "\eb39";
}

.graphxr-servicestack::before {
    content: "\eb3a";
}

.graphxr-shirtsinbulk::before {
    content: "\eb3b";
}

.graphxr-shopify::before {
    content: "\eb3c";
}

.graphxr-shopware::before {
    content: "\eb3d";
}

.graphxr-simplybuilt::before {
    content: "\eb3e";
}

.graphxr-sistrix::before {
    content: "\eb3f";
}

.graphxr-sith::before {
    content: "\eb40";
}

.graphxr-sketch::before {
    content: "\eb41";
}

.graphxr-skyatlas::before {
    content: "\eb42";
}

.graphxr-skype::before {
    content: "\eb43";
}

.graphxr-slack-hash::before {
    content: "\eb44";
}

.graphxr-slack::before {
    content: "\eb45";
}

.graphxr-slideshare::before {
    content: "\eb46";
}

.graphxr-snapchat-ghost::before {
    content: "\eb47";
}

.graphxr-snapchat-square::before {
    content: "\eb48";
}

.graphxr-snapchat::before {
    content: "\eb49";
}

.graphxr-soundcloud::before {
    content: "\eb4a";
}

.graphxr-sourcetree::before {
    content: "\eb4b";
}

.graphxr-speakap::before {
    content: "\eb4c";
}

.graphxr-speaker-deck::before {
    content: "\eb4d";
}

.graphxr-spotify::before {
    content: "\eb4e";
}

.graphxr-squarespace::before {
    content: "\eb4f";
}

.graphxr-stack-exchange::before {
    content: "\eb50";
}

.graphxr-stack-overflow::before {
    content: "\eb51";
}

.graphxr-stackpath::before {
    content: "\eb52";
}

.graphxr-staylinked::before {
    content: "\eb53";
}

.graphxr-steam-square::before {
    content: "\eb54";
}

.graphxr-steam-symbol::before {
    content: "\eb55";
}

.graphxr-steam::before {
    content: "\eb56";
}

.graphxr-sticker-mule::before {
    content: "\eb57";
}

.graphxr-strava::before {
    content: "\eb58";
}

.graphxr-stripe-s::before {
    content: "\eb59";
}

.graphxr-stripe::before {
    content: "\eb5a";
}

.graphxr-studiovinari::before {
    content: "\eb5b";
}

.graphxr-stumbleupon-circle::before {
    content: "\eb5c";
}

.graphxr-stumbleupon::before {
    content: "\eb5d";
}

.graphxr-superpowers::before {
    content: "\eb5e";
}

.graphxr-supple::before {
    content: "\eb5f";
}

.graphxr-suse::before {
    content: "\eb60";
}

.graphxr-swift::before {
    content: "\eb61";
}

.graphxr-symfony::before {
    content: "\eb62";
}

.graphxr-teamspeak::before {
    content: "\eb63";
}

.graphxr-telegram-plane::before {
    content: "\eb64";
}

.graphxr-telegram::before {
    content: "\eb65";
}

.graphxr-tencent-weibo::before {
    content: "\eb66";
}

.graphxr-the-red-yeti::before {
    content: "\eb67";
}

.graphxr-themeco::before {
    content: "\eb68";
}

.graphxr-themeisle::before {
    content: "\eb69";
}

.graphxr-think-peaks::before {
    content: "\eb6a";
}

.graphxr-tiktok::before {
    content: "\eb6b";
}

.graphxr-trade-federation::before {
    content: "\eb6c";
}

.graphxr-trello::before {
    content: "\eb6d";
}

.graphxr-tripadvisor::before {
    content: "\eb6e";
}

.graphxr-tumblr-square::before {
    content: "\eb6f";
}

.graphxr-tumblr::before {
    content: "\eb70";
}

.graphxr-twitch::before {
    content: "\eb71";
}

.graphxr-twitter-square::before {
    content: "\eb72";
}

.graphxr-twitter::before {
    content: "\eb73";
}

.graphxr-typo3::before {
    content: "\eb74";
}

.graphxr-uber::before {
    content: "\eb75";
}

.graphxr-ubuntu::before {
    content: "\eb76";
}

.graphxr-uikit::before {
    content: "\eb77";
}

.graphxr-umbraco::before {
    content: "\eb78";
}

.graphxr-uncharted::before {
    content: "\eb79";
}

.graphxr-uniregistry::before {
    content: "\eb7a";
}

.graphxr-unity::before {
    content: "\eb7b";
}

.graphxr-unsplash::before {
    content: "\eb7c";
}

.graphxr-untappd::before {
    content: "\eb7d";
}

.graphxr-ups::before {
    content: "\eb7e";
}

.graphxr-usb::before {
    content: "\eb7f";
}

.graphxr-usps::before {
    content: "\eb80";
}

.graphxr-ussunnah::before {
    content: "\eb81";
}

.graphxr-vaadin::before {
    content: "\eb82";
}

.graphxr-viacoin::before {
    content: "\eb83";
}

.graphxr-viadeo-square::before {
    content: "\eb84";
}

.graphxr-viadeo::before {
    content: "\eb85";
}

.graphxr-viber::before {
    content: "\eb86";
}

.graphxr-vimeo-square::before {
    content: "\eb87";
}

.graphxr-vimeo-v::before {
    content: "\eb88";
}

.graphxr-vimeo::before {
    content: "\eb89";
}

.graphxr-vine::before {
    content: "\eb8a";
}

.graphxr-vk::before {
    content: "\eb8b";
}

.graphxr-vnv::before {
    content: "\eb8c";
}

.graphxr-vuejs::before {
    content: "\eb8d";
}

.graphxr-watchman-monitoring::before {
    content: "\eb8e";
}

.graphxr-waze::before {
    content: "\eb8f";
}

.graphxr-weebly::before {
    content: "\eb90";
}

.graphxr-weibo::before {
    content: "\eb91";
}

.graphxr-weixin::before {
    content: "\eb92";
}

.graphxr-whatsapp-square::before {
    content: "\eb93";
}

.graphxr-whatsapp::before {
    content: "\eb94";
}

.graphxr-whmcs::before {
    content: "\eb95";
}

.graphxr-wikipedia-w::before {
    content: "\eb96";
}

.graphxr-windows::before {
    content: "\eb97";
}

.graphxr-wix::before {
    content: "\eb98";
}

.graphxr-wizards-of-the-coast::before {
    content: "\eb99";
}

.graphxr-wodu::before {
    content: "\eb9a";
}

.graphxr-wolf-pack-battalion::before {
    content: "\eb9b";
}

.graphxr-wordpress-simple::before {
    content: "\eb9c";
}

.graphxr-wordpress::before {
    content: "\eb9d";
}

.graphxr-wpbeginner::before {
    content: "\eb9e";
}

.graphxr-wpexplorer::before {
    content: "\eb9f";
}

.graphxr-wpforms::before {
    content: "\eba0";
}

.graphxr-wpressr::before {
    content: "\eba1";
}

.graphxr-xbox::before {
    content: "\eba2";
}

.graphxr-xing-square::before {
    content: "\eba3";
}

.graphxr-xing::before {
    content: "\eba4";
}

.graphxr-y-combinator::before {
    content: "\eba5";
}

.graphxr-yahoo::before {
    content: "\eba6";
}

.graphxr-yammer::before {
    content: "\eba7";
}

.graphxr-yandex-international::before {
    content: "\eba8";
}

.graphxr-yandex::before {
    content: "\eba9";
}

.graphxr-yarn::before {
    content: "\ebaa";
}

.graphxr-yelp::before {
    content: "\ebab";
}

.graphxr-yoast::before {
    content: "\ebac";
}

.graphxr-youtube-square::before {
    content: "\ebad";
}

.graphxr-youtube::before {
    content: "\ebae";
}

.graphxr-zhihu::before {
    content: "\ebaf";
}

.graphxr-character-1::before {
    content: "\ebb0";
}

.graphxr-character-2::before {
    content: "\ebb1";
}

.graphxr-character-3::before {
    content: "\ebb2";
}

.graphxr-character-4::before {
    content: "\ebb3";
}

.graphxr-character-5::before {
    content: "\ebb4";
}

.graphxr-character-6::before {
    content: "\ebb5";
}

.graphxr-character-7::before {
    content: "\ebb6";
}

.graphxr-character-8::before {
    content: "\ebb7";
}

.graphxr-character-9::before {
    content: "\ebb8";
}

.graphxr-character-a::before {
    content: "\ebb9";
}

.graphxr-character-b::before {
    content: "\ebba";
}

.graphxr-character-c::before {
    content: "\ebbb";
}

.graphxr-character-d::before {
    content: "\ebbc";
}

.graphxr-character-e::before {
    content: "\ebbd";
}

.graphxr-character-f::before {
    content: "\ebbe";
}

.graphxr-character-g::before {
    content: "\ebbf";
}

.graphxr-character-h::before {
    content: "\ebc0";
}

.graphxr-character-i::before {
    content: "\ebc1";
}

.graphxr-character-j::before {
    content: "\ebc2";
}

.graphxr-character-k::before {
    content: "\ebc3";
}

.graphxr-character-l::before {
    content: "\ebc4";
}

.graphxr-character-m::before {
    content: "\ebc5";
}

.graphxr-character-n::before {
    content: "\ebc6";
}

.graphxr-character-o::before {
    content: "\ebc7";
}

.graphxr-character-p::before {
    content: "\ebc8";
}

.graphxr-character-q::before {
    content: "\ebc9";
}

.graphxr-character-r::before {
    content: "\ebca";
}

.graphxr-character-s::before {
    content: "\ebcb";
}

.graphxr-character-t::before {
    content: "\ebcc";
}

.graphxr-character-u::before {
    content: "\ebcd";
}

.graphxr-character-v::before {
    content: "\ebce";
}

.graphxr-character-w::before {
    content: "\ebcf";
}

.graphxr-character-x::before {
    content: "\ebd0";
}

.graphxr-character-y::before {
    content: "\ebd1";
}

.graphxr-character-z::before {
    content: "\ebd2";
}

.graphxr-medical02::before {
    content: "\ebd3";
}

.graphxr-medical03::before {
    content: "\ebd4";
}

.graphxr-medical04::before {
    content: "\ebd5";
}

.graphxr-medical05::before {
    content: "\ebd6";
}

.graphxr-medical06::before {
    content: "\ebd7";
}

.graphxr-medical07::before {
    content: "\ebd8";
}

.graphxr-medical08::before {
    content: "\ebd9";
}

.graphxr-medical09::before {
    content: "\ebda";
}

.graphxr-medical10::before {
    content: "\ebdb";
}

.graphxr-medical11::before {
    content: "\ebdc";
}

.graphxr-medical12::before {
    content: "\ebdd";
}

.graphxr-medical13::before {
    content: "\ebde";
}

.graphxr-medical14::before {
    content: "\ebdf";
}

.graphxr-medical15::before {
    content: "\ebe0";
}

.graphxr-medical16::before {
    content: "\ebe1";
}

.graphxr-medical17::before {
    content: "\ebe2";
}

.graphxr-medical18::before {
    content: "\ebe3";
}

.graphxr-medical19::before {
    content: "\ebe4";
}

.graphxr-medical20::before {
    content: "\ebe5";
}

.graphxr-medical21::before {
    content: "\ebe6";
}

.graphxr-medical22::before {
    content: "\ebe7";
}

.graphxr-medical23::before {
    content: "\ebe8";
}

.graphxr-medical24::before {
    content: "\ebe9";
}

.graphxr-medical25::before {
    content: "\ebea";
}

.graphxr-account-box-outline::before {
    content: "\ebeb";
}

.graphxr-account-box::before {
    content: "\ebec";
}

.graphxr-account-circle::before {
    content: "\ebed";
}

.graphxr-account::before {
    content: "\ebee";
}

.graphxr-application::before {
    content: "\ebef";
}

.graphxr-at::before {
    content: "\ebf0";
}

.graphxr-barcode-scan::before {
    content: "\ebf1";
}

.graphxr-barcode::before {
    content: "\ebf2";
}

.graphxr-camera-iris::before {
    content: "\ebf3";
}

.graphxr-camera-outline::before {
    content: "\ebf4";
}

.graphxr-camera::before {
    content: "\ebf5";
}

.graphxr-card-text-outline::before {
    content: "\ebf6";
}

.graphxr-cart-outline::before {
    content: "\ebf7";
}

.graphxr-cart-variant::before {
    content: "\ebf8";
}

.graphxr-cart::before {
    content: "\ebf9";
}

.graphxr-cellphone-iphone::before {
    content: "\ebfa";
}

.graphxr-city::before {
    content: "\ebfb";
}

.graphxr-clipboard-text-outline::before {
    content: "\ebfc";
}

.graphxr-crosshairs-gps::before {
    content: "\ebfd";
}

.graphxr-desktop-classic::before {
    content: "\ebfe";
}

.graphxr-desktop-tower-monitor::before {
    content: "\ebff";
}

.graphxr-desktop-tower::before {
    content: "\ec00";
}

.graphxr-email-outline::before {
    content: "\ec01";
}

.graphxr-email-variant::before {
    content: "\ec02";
}

.graphxr-email::before {
    content: "\ec03";
}

.graphxr-gift-outline::before {
    content: "\ec04";
}

.graphxr-home-account::before {
    content: "\ec05";
}

.graphxr-home-circle-outline::before {
    content: "\ec06";
}

.graphxr-home-city::before {
    content: "\ec07";
}

.graphxr-home-group::before {
    content: "\ec08";
}

.graphxr-home-map-marker::before {
    content: "\ec09";
}

.graphxr-home::before {
    content: "\ec0a";
}

.graphxr-image-outline::before {
    content: "\ec0b";
}

.graphxr-image-text::before {
    content: "\ec0c";
}

.graphxr-image::before {
    content: "\ec0d";
}

.graphxr-laptop::before {
    content: "\ec0e";
}

.graphxr-link-variant::before {
    content: "\ec0f";
}

.graphxr-link::before {
    content: "\ec10";
}

.graphxr-map-marker::before {
    content: "\ec11";
}

.graphxr-monitor::before {
    content: "\ec12";
}

.graphxr-newspaper-variant-outline::before {
    content: "\ec13";
}

.graphxr-note-text-outline::before {
    content: "\ec14";
}

.graphxr-office-building-marker::before {
    content: "\ec15";
}

.graphxr-office-building-outline::before {
    content: "\ec16";
}

.graphxr-office-building::before {
    content: "\ec17";
}

.graphxr-package-variant-closed::before {
    content: "\ec18";
}

.graphxr-phone-classic::before {
    content: "\ec19";
}

.graphxr-phone::before {
    content: "\ec1a";
}

.graphxr-post::before {
    content: "\ec1b";
}

.graphxr-search-web::before {
    content: "\ec1c";
}

.graphxr-server-network::before {
    content: "\ec1d";
}

.graphxr-server::before {
    content: "\ec1e";
}

.graphxr-shopping-outline::before {
    content: "\ec1f";
}

.graphxr-shopping::before {
    content: "\ec20";
}

.graphxr-store-outline::before {
    content: "\ec21";
}

.graphxr-store::before {
    content: "\ec22";
}

.graphxr-storefront-outline::before {
    content: "\ec23";
}

.graphxr-storefront::before {
    content: "\ec24";
}

.graphxr-text-box-outline::before {
    content: "\ec25";
}

.graphxr-thumb-up-outline::before {
    content: "\ec26";
}

.graphxr-thumb-up::before {
    content: "\ec27";
}

.graphxr-thumbs-up-down::before {
    content: "\ec28";
}

.graphxr-web-box::before {
    content: "\ec29";
}

.graphxr-web::before {
    content: "\ec2a";
}

.graphxr-mould1::before {
    content: "\ec2b";
}

.graphxr-mould10::before {
    content: "\ec2c";
}

.graphxr-mould12::before {
    content: "\ec2d";
}

.graphxr-mould13::before {
    content: "\ec2e";
}

.graphxr-mould14::before {
    content: "\ec2f";
}

.graphxr-mould15::before {
    content: "\ec30";
}

.graphxr-mould2::before {
    content: "\ec31";
}

.graphxr-mould3::before {
    content: "\ec32";
}

.graphxr-mould4::before {
    content: "\ec33";
}

.graphxr-mould5::before {
    content: "\ec34";
}

.graphxr-mould6::before {
    content: "\ec35";
}

.graphxr-mould7::before {
    content: "\ec36";
}

.graphxr-mould8::before {
    content: "\ec37";
}

.graphxr-mould9::before {
    content: "\ec38";
}

.graphxr-mouldl11::before {
    content: "\ec39";
}

.graphxr-building::before {
    content: "\ec3a";
}

.graphxr-email-fill::before {
    content: "\ec3b";
}

.graphxr-facebook-circle::before {
    content: "\ec3c";
}

.graphxr-house::before {
    content: "\ec3d";
}

.graphxr-man-woman::before {
    content: "\ec3e";
}

.graphxr-man::before {
    content: "\ec3f";
}

.graphxr-phone-fill::before {
    content: "\ec40";
}

.graphxr-plane::before {
    content: "\ec41";
}

.graphxr-twitter-circle::before {
    content: "\ec42";
}

.graphxr-woman::before {
    content: "\ec43";
}

.graphxr-add::before {
    content: "\ec44";
}

.graphxr-cross::before {
    content: "\ec45";
}

.graphxr-dollar::before {
    content: "\ec46";
}

.graphxr-five-star::before {
    content: "\ec47";
}

.graphxr-folder::before {
    content: "\ec48";
}

.graphxr-heart::before {
    content: "\ec49";
}

.graphxr-person::before {
    content: "\ec4a";
}

.graphxr-polygon5::before {
    content: "\ec4b";
}

.graphxr-polygon6::before {
    content: "\ec4c";
}

.graphxr-rhombus::before {
    content: "\ec4d";
}

.graphxr-six-star::before {
    content: "\ec4e";
}

.graphxr-square::before {
    content: "\ec4f";
}

.graphxr-symobl-home::before {
    content: "\ec50";
}

.graphxr-triangle_down::before {
    content: "\ec51";
}

.graphxr-triangle_up::before {
    content: "\ec52";
}

