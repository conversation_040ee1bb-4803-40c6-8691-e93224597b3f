'********** induction on events 

'<PERSON> is holding key_101 now
<(*,<PERSON>,key_101) --> hold>. :|:  %1.00;0.90% 

6

' irrelevant  'outputMustContain('<John --> (/,hold,_,key_101)>. :\: %1.00;0.90%') 
' irrelevant  'outputMustContain('<key_101 --> (/,hold,<PERSON>,_)>. :\: %1.00;0.90%') 
' irrelevant  'outputMustContain('<John --> (/,hold,_,key_101)>. :\: %1.00;0.90%') 
' irrelevant  'outputMustContain('<key_101 --> (/,hold,<PERSON>,_)>. :\: %1.00;0.90%')

'If <PERSON> open door_101, he will enter room_101
<<(*,<PERSON>,door_101) --> open> =/> <(*,<PERSON>,room_101) --> enter>>. :|:  %1.00;0.90% 

20

'If <PERSON> hold key_101 and open door_101 (after 6 steps), he will enter room_101
''outputMustContain('<(&/,<(*,<PERSON>,key_101) --> hold>,+6,<(*,<PERSON>,door_101) --> open>) =/> <(*,<PERSON>,room_101) --> enter>>. :!6: %1.00;0.45%')
'changed fomr +2 to +4 due to changes in interval calculations
'this one is working, just throwing exception
