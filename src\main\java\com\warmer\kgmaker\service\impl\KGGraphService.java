package com.warmer.kgmaker.service.impl;

import com.warmer.kgmaker.dal.*;
import com.warmer.kgmaker.entity.*;
import com.warmer.kgmaker.query.*;
import com.warmer.kgmaker.service.*;
import com.warmer.kgmaker.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class KGGraphService implements IKGGraphService {

    @Autowired
    @Qualifier("KGraphRepository")
    private IKGraphRepository kgRepository;
    
    @Override
	public GraphPageRecord<Map<String, Object>> getPageDomain(GraphQuery queryItem) {
		return kgRepository.getPageDomain(queryItem);
	}
    @Override
    public void deleteKGdomain(String domain) {
        kgRepository.deleteKGdomain(domain);
    }

    @Override
    public Map<String, Object> getdomaingraph(GraphQuery query) {
        return kgRepository.getdomaingraph(query);
    }

    @Override
    public Map<String, Object> getdomainnodes(String domain, Integer pageIndex, Integer pageSize) {
        return kgRepository.getdomainnodes(domain, pageIndex, pageSize);
    }

    @Override
    public long getrelationnodecount(String domain, long nodeid) {
        return kgRepository.getrelationnodecount(domain, nodeid);
    }

    @Override
    public void createdomain(String domain) {
        kgRepository.createdomain(domain);
    }

    @Override
    public Map<String, Object> getmorerelationnode(String domain, String nodeid) {
        return kgRepository.getmorerelationnode(domain, nodeid);
    }

    @Override
    public Map<String, Object> updatenodename(String domain, String nodeid, String nodename) {
        return kgRepository.updatenodename(domain, nodeid, nodename);
    }

    @Override
    public Map<String, Object> createnode(String domain, QAEntityItem entity) {
        return kgRepository.createnode(domain, entity);
    }

    @Override
    public Map<String, Object> batchcreatenode(String domain, String sourcename, String relation,
                                                   String[] targetnames) {
        return kgRepository.batchcreatenode(domain, sourcename, relation, targetnames);
    }

    @Override
    public Map<String, Object> batchcreatechildnode(String domain, String sourceid, Integer entitytype,
                                                        String[] targetnames, String relation) {
        return kgRepository.batchcreatechildnode(domain, sourceid, entitytype, targetnames, relation);
    }

    @Override
    public List<Map<String, Object>> batchcreatesamenode(String domain, Integer entitytype, String[] sourcenames) {
        return kgRepository.batchcreatesamenode(domain, entitytype, sourcenames);
    }

    @Override
    public Map<String, Object> createlink(String domain, long sourceid, long targetid, String ship) {
        return kgRepository.createlink(domain, sourceid, targetid, ship);
    }

    @Override
    public Map<String, Object> updatelink(String shipid, Map attrs) {
        return kgRepository.updatelink(shipid, attrs);
    }

    @Override
    public Map<String, Object> updatenode(String id, Map attrs) {
        return kgRepository.updatenode(id, attrs);
    }

    @Override
    public Map<String, Object> changelink(String domain, long shipid) {
        return kgRepository.changelink(domain, shipid);
    }
    @Override
    public void deletenode(String domain, long nodeid) {
        kgRepository.deletenode(domain, nodeid);
    }

    @Override
    public void deletelink(String domain, long shipid) {
        kgRepository.deletelink(domain, shipid);
    }

    @Override
    public Map<String, Object> createGraphByText(String domain, Integer entitytype, Integer operatetype,
                                                     Integer sourceid, String[] rss) {
        return kgRepository.createGraphByText(domain, entitytype, operatetype, sourceid, rss);
    }

    @Override
    public void batchcreateGraph(String domain, List<Map<String, Object>> params) {
        kgRepository.batchcreateGraph(domain, params);
    }

    @Override
    public void updateNodeFileStatus(String domain, long nodeId, int status) {
        kgRepository.updateNodeFileStatus(domain,nodeId,status);
    }

    @Override
    public void updateCorrdOfNode(String domain, String id, Double fx, Double fy) {
        kgRepository.updateCorrdOfNode(domain,id,fx,fy);
    }

    @Override
	public void batchInsertByCSV(String domain, String csvUrl, int status) {
		kgRepository.batchInsertByCSV(domain, csvUrl, status);
	}

	

}
