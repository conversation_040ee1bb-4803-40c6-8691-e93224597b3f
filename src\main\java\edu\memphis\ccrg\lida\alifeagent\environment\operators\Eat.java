package edu.memphis.ccrg.lida.alifeagent.environment.operators;

import edu.memphis.ccrg.lida.actionselection.Action;
import edu.memphis.ccrg.lida.actionselection.ActionImpl;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.sensorymotormemory.BasicSensoryMotorMemory;
import edu.memphis.ccrg.lida.sensorymotormemory.ProcessActionTask;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;

import org.opennars.operator.Operation;
import org.opennars.operator.Operator;
import edu.memphis.ccrg.linars.*;

import java.util.List;

import static edu.memphis.ccrg.lida.framework.FrameworkModuleImpl.taskSpawner;

public class Eat extends Operator {
    private BasicSensoryMotorMemory basicSensoryMotorMemory;
    public Eat(BasicSensoryMotorMemory basicSensoryMotorMemory, String name) {
        super(name);
        this.basicSensoryMotorMemory = basicSensoryMotorMemory;
    }

    @Override
    public List<Task> execute(Operation operation, Term[] args, Memory memory, Timable time) {
        AgentStarter.actionId = 2;
        Action action = new ActionImpl("eat");
        action.setId(121);
        ProcessActionTask t = new ProcessActionTask(basicSensoryMotorMemory, action);
        taskSpawner.addTask(t);
        memory.allowExecution = false;
        System.out.println("lidars decide Eat");
        AgentStarter.nar.addInput("<{SELF} --> [morehealthy]>. :|:");
        AgentStarter.nar.addInput("<{SELF} --> [satisfied]>. :|:");
//        AgentStarter.nar.addInput("<{SELF} --> [cango]>. :|:");
        return null;
    }
}
