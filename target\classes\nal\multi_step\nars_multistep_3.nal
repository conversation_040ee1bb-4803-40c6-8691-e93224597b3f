*** Fuzzy concepts
<{<PERSON>} --> (/,taller_than,{<PERSON>},_)>.
<{<PERSON>} --> boy>.
5
<{<PERSON>} --> (/,taller_than,_,boy)>? 
''outputMustContain('<boy --> (/,taller_than,{<PERSON>},_)>. %1.00;0.45%')
6
<{<PERSON>} --> boy>. 
(--,<{<PERSON>} --> (/,taller_than,{<PERSON>},_)>).

1000

//''outputMustContain('<boy --> (/,taller_than,{<PERSON>},_)>. %0.00;0.40%')

//''outputMustContain('<{<PERSON>} --> (/,taller_than,_,boy)>. %1.00;0.45%')
1
<{<PERSON>} --> (/,taller_than,{<PERSON>},_)>.
<{Karl} --> boy>. 
500000
''outputMustContain('<boy --> (/,taller_than,{<PERSON>},_)>. %0.67;0.71%')
