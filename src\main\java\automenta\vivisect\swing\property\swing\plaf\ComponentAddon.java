/**
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package automenta.vivisect.swing.property.swing.plaf;

/**
 * Each new component type of the library will contribute an addon to
 * the LookAndFeelAddons. A <code>ComponentAddon</code> is the
 * equivalent of a {@link javax.swing.LookAndFeel}but focused on one
 * component. <br>
 * 
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 */
public interface ComponentAddon {

  /**
   * @return the name of this addon
   */
  String getName();

  /**
   * Initializes this addon (i.e register UI classes, colors, fonts,
   * borders, any UIResource used by the component class). When
   * initializing, the addon can register different resources based on
   * the addon or the current look and feel.
   * 
   * @param addon the current addon
   */
  void initialize(LookAndFeelAddons addon);

  /**
   * Uninitializes this addon.
   * 
   * @param addon
   */
  void uninitialize(LookAndFeelAddons addon);

}