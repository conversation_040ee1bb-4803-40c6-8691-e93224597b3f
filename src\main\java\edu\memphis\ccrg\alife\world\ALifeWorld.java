package edu.memphis.ccrg.alife.world;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.elements.Cell;
import edu.memphis.ccrg.alife.elements.properties.Attributable;
import edu.memphis.ccrg.alife.opreations.Updateable;
import edu.memphis.ccrg.alife.opreations.WorldOperation;
import java.util.Collection;

public interface ALifeWorld extends Attributable, Updateable {
    void addAction(String str, WorldOperation worldOperation);

    boolean addObject(ALifeObject aLifeObject, int i, int i2);

    Cell getCell(int i, int i2);

    Cell[][] getCells();

    int getHeight();

    ALifeObject getObject(int i);

    ALifeObject getObject(String str);

    Collection<ALifeObject> getObjects();

    int getWidth();

    Object performAction(WorldOperation worldOperation, ALifeObject aLifeObject, ALifeObject[] aLifeObjectArr, Object... objArr);

    Object performOperation(String str, ALifeObject aLifeObject, ALifeObject[] aLifeObjectArr, Object... objArr);

    void registerObject(ALifeObject aLifeObject);

    void updateWorldState();
}
