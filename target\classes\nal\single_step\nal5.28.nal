'********** conditional abduction

'If robin can fly and it has wings, then robin is living.
<(&&,<robin --> [flying]>,<robin --> [with-wings]>) ==> <robin --> [living]>>. %0.9%

'If robin can fly and robin is a bird then robin is living.
<(&&,<robin --> [flying]>,<robin --> bird>) ==> <robin --> [living]>>. 

18

'I guess if robin is a bird, then robin has wings.
''outputMustContain('<<robin --> bird> ==> <robin --> [with-wings]>>. %1.00;0.42%')

'I guess if robin has wings, then robin is a bird.
''outputMustContain('<<robin --> [with-wings]> ==> <robin --> bird>>. %0.90;0.45%')

