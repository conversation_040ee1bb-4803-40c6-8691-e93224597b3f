package com.warmer.kgmaker.controller;

import com.alibaba.fastjson.JSONObject;
import com.warmer.kgmaker.KgmakerApplication;
import com.warmer.kgmaker.service.IKnowledgegraphService;
import com.warmer.kgmaker.util.AESUtil;
import com.warmer.kgmaker.util.Neo4jUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Controller
@RequestMapping("/webim")
public class ImControlDetector{

    @Autowired
    private IKnowledgegraphService kgservice;	// mysql

    @Autowired
    private Neo4jUtil neo4jUtil;

//    private static final Logger logger = Logger.getLogger(ImControlDetector.class.getCanonicalName());
//
//    private PerceptualAssociativeMemory pam00 = new PerceptualAssociativeMemoryImpl();
//    private PerceptualAssociativeMemory pam0;
//
//    private SensoryMemory sensoryMemory0;
//

//
//    public ImControlDetector(){
//
//    }
//
//    @Override
//    protected void runThisFrameworkTask() {
//
//    }
//
//    @Override
//    public void init(){
//        super.init();
//    }
//
//    @Override
//    public void setAssociatedModule(FrameworkModule module, String moduleUsage){
//        if(module instanceof PerceptualAssociativeMemory){
//            pam0 = (PerceptualAssociativeMemory) module;
//        }else if(module instanceof SensoryMemory){
//            sensoryMemory0 = (SensoryMemory) module;
//        }else{
//            logger.log(Level.WARNING, "Cannot set associated module {1}",
//                    new Object[]{TaskManager.getCurrentTick(),module});
//        }
//    }

    @GetMapping("/base")
    @ResponseBody
    public JSONObject base(Model model){
        JSONObject res = new JSONObject();
//        String base = "{\n" +
//                "  \"code\": 0\n" +
//                "  ,\"msg\": \"\"\n" +
//                "  ,\"data\": {\n" +
//                "    \"mine\": {\n" +
//                "      \"username\": \"柯东\"\n" +
//                "      ,\"id\": \"100000\"\n" +
//                "      ,\"status\": \"online\"\n" +
//                "      ,\"sign\": \"精神物质是一体的\"\n" +
//                "      ,\"avatar\": \"/1.jpg\"\n" +
//                "    }\n" +
//                "    ,\"friend\": [{\n" +
//                "      \"groupname\": \"球长助理\"\n" +
//                "      ,\"id\": 0\n" +
//                "      ,\"list\": [{\n" +
//                "        \"username\": \"mos\"\n" +
//                "        ,\"id\": \"100001\"\n" +
//                "        ,\"avatar\": \"/2.jpg\"\n" +
//                "        ,\"sign\": \"世界那么大，我们要拿下\"\n" +
//                "        ,\"status\": \"online\"\n" +
//                "      }]\n" +
//                "    }]\n" +
//                "  }\n" +
//                "}";
//        res.put("data",base);
        res.put("code",0);
        return res;
    }

    @GetMapping(value = "/token")
    @ResponseBody
    public JSONObject getToken() throws Exception{
        JSONObject res = new JSONObject();
        String key = String.format("%d_%d", 000, System.currentTimeMillis());

        String token = AESUtil.encyrpt(key);
        res.put("",res);
        return res;
    }

    @GetMapping(value = "/send")
    @ResponseBody
    public JSONObject getMessage(HttpServletRequest req) throws Exception{
        JSONObject res = new JSONObject();

        // username=%E6%9F%AF%E4%B8%9C&avatar=%2Fimages%2Fme.png&id=100001&type=friend&
        // content=%E7%94%B5%E9%A5%AD%E9%94%85%E7%94%B5%E9%A5%AD%E9%94%85%E7%9A%84
        // &timestamp=1578289804026&mine=true&_=1578280683008

        String message0 = req.getQueryString();
        System.out.println("收到主人信息：" + message0);

        String[] messages = req.getQueryString().split("=");
        String message = URLDecoder.decode(messages[5],"utf-8").replace("&timestamp","");

//        Map<String,Object> msgMap = new HashMap<>();
//        msgMap.put("from0","主人");
//        msgMap.put("message",message);
//        msgMap.put("time0", TaskManager.getCurrentTick());
//        kgservice.saveMessage(msgMap);
//        message = "花，去，哪里，了，？";
//        message = "花，去，哪里，了，？";
        String[] mms = message.split("，");
        List<String> pairs;

        if (!"".equals(message) ) {
            KgmakerApplication.message = new ArrayList<>(Arrays.asList(mms));
        }

//        for (int i = 0; i < mms.length; i++) {
//            for (int j = i + 1; j < mms.length; j++) {
//                pairs = new ArrayList<>();
//                pairs.add(mms[i]);
//                pairs.add(mms[j]);
////                doReco(pairs);
//            }
//        }

        res.put("code",0);
        return res;
    }

    @GetMapping(value = "/getMessage") // agent收到信息，agent要回复
    @ResponseBody
    public JSONObject SendMessage(HttpServletRequest req) throws Exception{
        JSONObject res = new JSONObject();

        System.out.println("发信息给主人啦啦啦！");

        res.put("content","运算，25，加，8");
        res.put("name","mos");
        res.put("username","mos");
        res.put("id","100001");
        res.put("type","friend");
        res.put("avatar","/images/logo/logo-3.jpg");

        return res;
    }
}
