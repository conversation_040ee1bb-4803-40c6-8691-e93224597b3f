.ul-context-menu {
	list-style: none;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 99999999;
	padding: 5px 0;
	min-width: 80px;
	margin: 0;
	display: none;
	font-family: "微软雅黑";
	font-size: 14px;
	background-color: #fff;
	border: 1px solid rgba(0, 0, 0, .15);
	box-sizing: border-box;
	border-radius: 4px;
	-webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	-moz-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	-ms-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	-o-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ul-context-menu .ui-context-menu-item {
	margin: 0;
	padding: 0;
}

.ul-context-menu .ui-context-menu-item a {
	display: block;
	padding: 0 10px;
	color: #333;
	white-space: nowrap;
	text-decoration: none;
}

.ul-context-menu .ui-context-menu-item a:hover {
	text-decoration: none;
	color: #262626;
}

.ul-context-menu .ui-context-menu-item .icon {
	width: 16px;
	height: 16px;
	margin-right: 8px;
	vertical-align: sub;
	border: 0;
}
/*这里编辑自定义样式*/
.move:hover ul{
	display: block;
    width: 110px;
    position: absolute;
    background: #d9d9d9;
    left: 112px;
    bottom: 34px;
    margin-top: -30px;
    border: 1px solid rgba(0, 0, 0, .15);	
}

.gag ul,.move ul{
	display: none;
}
.gag:hover ul{
	display: block;
    width: 110px;
    position: absolute;
    background: #d9d9d9;
    right: 112px;
    margin-top: -30px;
    border: 1px solid rgba(0, 0, 0, .15);	
}
.gag ul li a,.move ul li a{
	display: block;
    padding: 0 10px;
    white-space: nowrap;
    text-decoration: none;
    color: rgb(51, 51, 51);
    font-size: 15px;
    height: 30px;
    line-height: 30px;
    background-color: rgb(255, 255, 255);    
}
.gag ul li a:hover,.move ul li a:hover {
	text-decoration: none;
	background-color: rgb(0, 155, 221);
}

