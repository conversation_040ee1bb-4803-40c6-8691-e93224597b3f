path.link{fill:none;stroke:#7f8c8d;stroke-width:2px}
text{font-family:sans-serif;pointer-events:none}
marker{fill:#d8dadc}
text .faded{fill:#333}
.node .outline{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);-ms-transform:scale(1);transform:scale(1);-webkit-transition:all .15s;-moz-transition:all .15s;-o-transition:all .15s;-ms-transition:all .15s;transition:all .15s;-webkit-transition-timing-function:cubic-bezier(.694,.0482,.335,1);-moz-transition-timing-function:cubic-bezier(.694,.0482,.335,1);-o-transition-timing-function:cubic-bezier(.694,.0482,.335,1);-ms-transition-timing-function:cubic-bezier(.694,.0482,.335,1);transition-timing-function:cubic-bezier(.694,.0482,.335,1)}
.node:hover .outline{-webkit-transform:scale(1.4);-moz-transform:scale(1.4);-o-transform:scale(1.4);-ms-transform:scale(1.4);transform:scale(1.4)}
.relationship:hover{stroke:#3498db}
.outline,.overlay{cursor:pointer}
g.node text[stroke="#DFE1E3"] {text-shadow: -1px -1px 2px #DFE1E3, -1px 1px 2px #DFE1E3, -1px 0 2px #DFE1E3, 1px -1px 2px #DFE1E3, 1px 1px 2px #DFE1E3, 1px 0 2px #DFE1E3, 0 -1px 2px #DFE1E3, 0 1px 2px #DFE1E3}
g.node text[stroke="#F25A29"] {text-shadow: -1px -1px 2px #F25A29, -1px 1px 2px #F25A29, -1px 0 2px #F25A29, 1px -1px 2px #F25A29, 1px 1px 2px #F25A29, 1px 0 2px #F25A29, 0 -1px 2px #F25A29, 0 1px 2px #F25A29}
g.node text[stroke="#AD62CE"] {text-shadow: -1px -1px 2px #AD62CE, -1px 1px 2px #AD62CE, -1px 0 2px #AD62CE, 1px -1px 2px #AD62CE, 1px 1px 2px #AD62CE, 1px 0 2px #AD62CE, 0 -1px 2px #AD62CE, 0 1px 2px #AD62CE}
g.node text[stroke="#30B6AF"] {text-shadow: -1px -1px 2px #30B6AF, -1px 1px 2px #30B6AF, -1px 0 2px #30B6AF, 1px -1px 2px #30B6AF, 1px 1px 2px #30B6AF, 1px 0 2px #30B6AF, 0 -1px 2px #30B6AF, 0 1px 2px #30B6AF}
g.node text[stroke="#FCC940"] {text-shadow: -1px -1px 2px #FCC940, -1px 1px 2px #FCC940, -1px 0 2px #FCC940, 1px -1px 2px #FCC940, 1px 1px 2px #FCC940, 1px 0 2px #FCC940, 0 -1px 2px #FCC940, 0 1px 2px #FCC940}
g.node text[stroke="#4356C0"] {text-shadow: -1px -1px 2px #4356C0, -1px 1px 2px #4356C0, -1px 0 2px #4356C0, 1px -1px 2px #4356C0, 1px 1px 2px #4356C0, 1px 0 2px #4356C0, 0 -1px 2px #4356C0, 0 1px 2px #4356C0}
g.node text[stroke="#FF6C7C"] {text-shadow: -1px -1px 2px #FF6C7C, -1px 1px 2px #FF6C7C, -1px 0 2px #FF6C7C, 1px -1px 2px #FF6C7C, 1px 1px 2px #FF6C7C, 1px 0 2px #FF6C7C, 0 -1px 2px #FF6C7C, 0 1px 2px #FF6C7C}
g.node text[stroke="#a2cf81"] {text-shadow: -1px -1px 2px #a2cf81, -1px 1px 2px #a2cf81, -1px 0 2px #a2cf81, 1px -1px 2px #a2cf81, 1px 1px 2px #a2cf81, 1px 0 2px #a2cf81, 0 -1px 2px #a2cf81, 0 1px 2px #a2cf81}
g.node text[stroke="#f79235"] {text-shadow: -1px -1px 2px #f79235, -1px 1px 2px #f79235, -1px 0 2px #f79235, 1px -1px 2px #f79235, 1px 1px 2px #f79235, 1px 0 2px #f79235, 0 -1px 2px #f79235, 0 1px 2px #f79235}
g.node text[stroke="#785cc7"] {text-shadow: -1px -1px 2px #785cc7, -1px 1px 2px #785cc7, -1px 0 2px #785cc7, 1px -1px 2px #785cc7, 1px 1px 2px #785cc7, 1px 0 2px #785cc7, 0 -1px 2px #785cc7, 0 1px 2px #785cc7}
g.node text[stroke="#d05e7c"] {text-shadow: -1px -1px 2px #d05e7c, -1px 1px 2px #d05e7c, -1px 0 2px #d05e7c, 1px -1px 2px #d05e7c, 1px 1px 2px #d05e7c, 1px 0 2px #d05e7c, 0 -1px 2px #d05e7c, 0 1px 2px #d05e7c}
g.node text[stroke="#3986b7"] {text-shadow: -1px -1px 2px #3986b7, -1px 1px 2px #3986b7, -1px 0 2px #3986b7, 1px -1px 2px #3986b7, 1px 1px 2px #3986b7, 1px 0 2px #3986b7, 0 -1px 2px #3986b7, 0 1px 2px #3986b7}
