body, p, dl, dd, pre, h1, h2, h3, h4, h5, h6, ul, ol {
    margin: 0;
    padding: 0;
    list-style: none;
}

input, button, option, textarea, select, fieldset {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    outline: none;
    vertical-align: middle;
    font-family: "\5FAE\8F6F\96C5\9ED1"
}

input[type="text"], input[type="password"] {
    padding-left: 5px;
    padding-right: 5px;
}

a {
    color: inherit;
    text-decoration: none;
}

img, iframe {
    border: none;
}

:focus {
    outline: none;
}

html, body, #app {
    /*height: 100%;*/
}

.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.clearfix:after {
    content: '\200B';
    display: block;
    height: 0;
    clear: both;
}

.clearfix {
    *zoom: 1;
}

body {
    /*min-width: 1300px;*/
    overflow-x: auto;
    --mc: #193446;
    --c: #156498;
    --lc: #bde4ff;
    --bj: #e9eff4;
    font: 14px/1.45 "\5FAE\8F6F\96C5\9ED1";
    color: #333;
    background: #f7f9fc;
}

.top {
    height: 84px;
    padding: 0 28px 0 20px;
    line-height: 82px;
    text-align: center;
    background: url(../images/top-bj.png) no-repeat center right;
    background-color: #193446;
    background-color: var(--mc);
    box-shadow: 0 0 29px rgba(53, 152, 219, .19);
}

.top-t {
    float: left;
    font-size: 24px;
    font-weight: normal;
    color: #fff;
}

.top-user {
    float: right;
    color: var(--lc);
}

.head-pic {
    display: inline-block;
    width: 40px;
    height: 40px;
    margin-right: 8px;
    vertical-align: middle;
    border-radius: 50%;
}

.logout {
    margin-left: 27px;
    vertical-align: middle;
}

.logout svg {
    font-size: 16px;
}

.search {
    position: relative;
    width: 220px;
    height: 32px;
    border-radius: 32px;
    overflow: hidden;
}

.search .el-input__inner {
    box-sizing: border-box;
    padding-left: 15px;
    height: 32px;
    line-height: 32px;
    padding-right: 40px;
    background: transparent;
    border-radius: 32px;
    border: none;
    transition: background .3s;
}

.search .el-button--default {
    position: absolute;
    right: 1px;
    float: right;
    padding: 0 10px;
    font-size: 22px;
    line-height: 29px;
    color: #7c9cb2;
    background: transparent;
    border: none;
    z-index: 1;
}

.search .el-input__inner:focus {
}

.search .el-button--default:hover {
    color: #156498;
    background: transparent;
    border: none;
}

.top .search {
    margin-left: 30px;
    background: rgba(0, 0, 0, .25);
    display: none;
}

.footer {
    line-height: 65px;
    margin-top: 120px;
    text-align: center;
    color: #5d5d5d;
    background: #282828;
}

.el-menu {
    background: transparent;
    border: none;
}

.el-menu--horizontal > .el-menu-item, .el-menu--horizontal > .el-submenu .el-submenu__title, .el-submenu__title i {
    color: #fff;
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-submenu:focus .el-submenu__title, .el-menu--horizontal > .el-submenu:hover .el-submenu__title {
    outline: 0;
    color: #bde4ff;
    background: transparent;
}

.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover {
    border-bottom: 3px solid #ff8a00 !important;
}

.el-menu--horizontal > .el-submenu:focus, .el-menu--horizontal > .el-submenu:hover .el-submenu__title i {
    color: #bde4ff;
}

.el-menu--popup {
    min-width: 120px;
    margin-top: -2px;
    padding: 0;
    background: #193446;
    border-top: 3px solid #ff8a00;
}

.el-menu--horizontal .el-menu .el-menu-item, .el-menu--horizontal .el-menu .el-submenu__title {
    float: none;
    height: 48px;
    line-height: 48px;
    text-align: center;
    color: #fff;
    background-color: transparent;
    border-bottom: 1px solid #204056;
}

.el-el-menu--popup .el-menu-item:not(.is-disabled):focus, .el-el-menu--popup .el-menu--popup .el-menu-item:not(.is-disabled):hover, .el-el-menu--popup .el-dropdown-menu__item:not(.is-disabled):hover {
    color: #fff;
    background: #132734;
}

.el-menu--horizontal > .el-submenu .el-submenu__title, .el-menu--horizontal > .el-menu-item {
    height: 82px;
    line-height: 82px;
}

.el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
    border-bottom: 2px solid #ff8a00;
    color: #bde4ff;
}

.el-menu--horizontal > .el-menu-item a {
    display: block;
}

.el-menu--horizontal > .el-menu-item.is-active {
    border-bottom: 3px solid #ff8a00 !important;
    color: #bde4ff;
}

body .btn-bo:hover {
    color: #fff;
    background: #ee8407;
    border-color: #ff8a00;
}

.dvm, .search {
    display: inline-block;
    vertical-align: middle;
    line-height: 1;
}

@-webkit-keyframes load-dh {
    0% {
        margin-left: -300px;
    }
    100% {
        margin-left: 0;
    }
}

@-webkit-keyframes svg-box {
    0% {
        margin-top: -20px;
        opacity: 0;
    }
    100% {
        margin-top: 0;
        opacity: 1;
    }
}

.mind-box {
    /*height: calc(100vh - 85px);*/
    overflow: hidden
}

.mind-l {
    /*width: 300px;*/
    /*float: left;*/
    /*background: #f7f9fc;*/
    /*height: 100%;*/
    /*border-right: 1px solid #d3e2ec;*/
    top:20px;
    position: absolute;
    z-index: 100;

}

.ml-ht {
    padding-top: 20px;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #333;
    border-bottom: 1px solid #d3e2ec
}

.ml-a-box {
    margin: 10px
}

.ml-a {
    display: inline-block;
    min-width: 46px;
    line-height: 1;
    padding: 6px 8px 6px 8px;
    margin: 0 4px 5px 0;
    background: #fff;
    border: 1px solid #e3e3e3;
    box-sizing: border-box;
    transition: .3s;
}

.ml-a span {
    max-width: 190px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle
}

.ml-a-all {
    display: block;
    margin: 10px 10px 0;
    text-align: center;
}

.ml-a span:empty:before {
    content: "閺堫亜鎳￠崥锟�";
    color: #adadad;
}

.ml-a small {
    color: #999
}

.ml-a:hover {
    background: #f4f4f4
}

.ml-a.cur, .ml-a.cur small {
    background: #156498;
    color: #fff
}

.ml-btn-box {
    text-align: right;
    padding: 0 10px;
    margin-bottom: 20px
}

.ml-btn {
    padding: 0 5px;
    color: #156498
}

.mind-con {
    height: 100%;
    /*overflow: hidden;*/
    background: #fff;
    display: -webkit-flex;
    display: flex;
    flex-direction: column
}

.mind-top {
    /*line-height: 70px;*/
    /*height: 70px;*/
    padding: 0 22px;
    border-bottom: 1px solid #ededed
}

.mt-m {
    color: #666;
    margin-right: 30px
}

.mt-m i {
    font-size: 18px;
    color: #333;
    font-weight: 700;
    font-style: normal
}

.mb-con .search, .mind-top .search {
    border: 1px solid #e2e2e2
}

.svg-a-sm {
    font-size: 14px;
    color: #156498;
    margin-right: 30px
}

.mind-cen {
    height: calc(100% - 70px);
}

.half-auto {
    height: 40%;
}

.mind-bottom {
    height: 490px;
    box-sizing: border-box;
    border-top: 1px solid #ededed;
}

.ss-d {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    border-radius: 50%;
    background: #dedede
}

.sd {
    margin: 2px
}

.sd-active {
    color: red !important
}

.btn-line + .btn-line {
    margin-left: 10px;
}

.co {
    color: #ee8407 !important
}

.a {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.tl {
    text-align: left;
}

/* loading */
.x-load-box {
    text-align: center;
    margin: 40px 0;
}

.x-load {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    width: 40px;
    height: 40px;
    margin: 0 10px 0 0;
    vertical-align: middle;
    border: 2px solid #409eff;
    border-radius: 50%
}

.x-load:after {
    content: "";
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    width: 44px;
    height: 44px;
    background: #fff;
    border-radius: 50%;
    transform: rotate(-761deg);
    transform-origin: 8px 8px;
    animation: fadeInUp 2s infinite;
    -webkit-animation: fadeInUp 2s infinite
}

@-webkit-keyframes fadeInUp {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

img {
    border: 0;
    display: block
}

ul, li {
    list-style: none;
}

a {
    text-decoration: none;
    color: #555
}

a:hover {
    text-decoration: none;
    color: #00A7EB;
}

.clear {
    clear: both;
}

.blank {
    height: 20px;
    overflow: hidden;
    width: 100%;
    margin: auto;
    clear: both
}

.blank100 {
    height: 100px;
    overflow: hidden;
    width: 100%;
    margin: auto;
    clear: both
}

.f_l {
    float: left
}

.f_r {
    float: right
}

.mt20 {
    margin-top: 20px
}

.logo {
    float: left;
    margin-left: 70px;
    width: 260px;
    font-size: 26px;
}

.logo a {
    color: #00A7EB
}

.menu {
    height: 80px;
    line-height: 80px;
    width: 100%;
    background-color: #000;
}

.nav {
    height: 80px;
    width: 100%;
    margin: 0 auto;
}

.nav li {
    float: left;
    position: relative;
}

.nav li a {
    color: #bdbdbd;
    padding: 0 20px;
    display: inline-block;
}

.nav li a:hover {
    color: #fff;
}

.nav li .sub-nav {
    position: absolute;
    top: 80px;
    width: 200px;
    background: #FFF;
    left: -20px; /* display: none;  */
}

.nav li .sub-nav li {
    clear: left;
    height: 20px;
    line-height: 35px;
    position: relative;
    width: 200px;
    padding: 5px 20px
}

.nav li .sub-nav li a {
    font-size: 15px;
    font-weight: 400;
    color: #404040;
    line-height: 28px;
}

.nav li .sub-nav li a:hover {
    color: #000;
    border-left: 2px solid #000;
}

#topnav_current {
    color: #00A7EB;
}

.a_active {
    color: #00A7EB !important;
}

.hometitle {
    font-size: 18px;
    color: #282828;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    padding-bottom: 15px;
    margin-bottom: 25px;
    position: relative;
}

.hometitle:after {
    content: "";
    background-color: #282828;
    left: 0;
    width: 50px;
    height: 2px;
    bottom: 0;
    position: absolute;
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -ms-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}

/*guanzhu*/
.guanzhu ul li {
    font-size: 12px;
    margin-bottom: 10px;
    background: #fff;
    color: #525252;
    line-height: 40px;
    padding: 0 0 0 34px;
    border: 1px solid #DDD;
    border-radius: 2px;
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.guanzhu .sina {
    border: #ec3d51 1px solid;
    background: url(/images/gzbg.jpg) no-repeat 0 10px
}

.guanzhu .tencent {
    border: #68a6d6 1px solid;
    background: url(/images/gzbg.jpg) no-repeat 0 -43px
}

.guanzhu .qq {
    border: #2ab39a 1px solid;
    background: url(/images/gzbg.jpg) no-repeat 0 -98px
}

.guanzhu .email {
    border: #12aae8 1px solid;
    background: url(/images/gzbg.jpg) no-repeat 0 -150px
}

.guanzhu .wxgzh {
    border: #199872 1px solid;
    background: url(/images/gzbg.jpg) no-repeat 0 -200px
}

.guanzhu .wx {
    overflow: hidden;
    padding: 0
}

.guanzhu .wx img {
    width: 100%;
}

.guanzhu ul li span {
    float: right;
    text-align: center;
    width: 100px;
    -moz-transition: all .5s ease;
    -webkit-transition: all .5s ease;
    -ms-transition: all .5s ease;
    -o-transition: all .5s ease;
    transition: all .5s ease;
    transition: all 0.5s;
}

.guanzhu .sina span {
    background: #ec3d51;
}

.guanzhu .tencent span {
    background: #68a6d6;
}

.guanzhu .qq span {
    background: #2ab39a;
}

.guanzhu .email span {
    background: #12aae8;
}

.guanzhu .wxgzh span {
    background: #199872;
}

.guanzhu a span {
    color: #FFF
}

.guanzhu ul li:hover span {
    width: 120px;
}