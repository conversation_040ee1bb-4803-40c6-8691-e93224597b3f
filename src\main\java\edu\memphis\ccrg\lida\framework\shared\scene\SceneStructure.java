package edu.memphis.ccrg.lida.framework.shared.scene;

import edu.memphis.ccrg.lida.framework.shared.Node;
import org.opennars.entity.Stamp;
import org.opennars.io.Symbols;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class SceneStructure extends CompoundTerm0 {

    private static int idCounter = 0;// TODO Factory support for 这个

    // 画中画嵌套，递归，可能多个
    // 当前场景就是真实体验场景，真实画中画 = 涉及到的其他场景 = 如探讨其他场景
    private ConcurrentMap<Integer,SceneStructure> subScene = new ConcurrentHashMap<>();

    // 脑子想象的其他场景，可能与真实和画中画都无关.只有一个？
    // 画中画与想象可能互相切换，在广播单元内可能只有一个，甚至两个都无
    private SceneStructure unrealScene;

    // 唯一id，一定程度代表场景数
    private Integer id;
    private String name;
    private String status;
    // todo 持续时间、广播id
//    private long startTime;
//    private long endTime;

    private Stamp stamp;

    private SSite sSite;    // 实际地点
    private SSite asSite;   // 常规地点

    private SIntent sIntent;

    // 施受事者都包含
    private List<SPerson> personList = new ArrayList<>();
    // 方法论
    private List<SMethod> methodList;
    private SWorldView sWorldView;

    // 每个场景对应一个情感情绪集，词项实际上也是通过场景激活情绪？
    // 理论上可细化到每个元组绑定情绪情感，不局限于场景？
    private SEmotion sEmotion;

    private SRequest sRequest;
    private SResponse sResponse;

    // 上下文，时间轴，空间体
    private SContext sContext;
    private SOrder sOrder;

    // 双id = 串联父id+并联id = 确定上下位+唯一id
    // 110213，双位个数代表层数，双位数代表真实虚拟，单位数=平行场景id
    private Integer dadId;
    private Integer brotherId;

    private List<SAction> sActionList;
    private String sTime;

    private List<Entity> entityList;

    // 场景内环境属性，世界观，区别于场景外知识体系
    private WorldStructure worldStructure;

//    private Map<String,List<Record>> unionMap;
//    private Map<String,List<Record>> methodMap;
//    private Map<String,List<Record>> anwserMap;

    public SceneStructure(){
        this.id = idCounter++;
        this.personList.add(new SPerson("agent"));
    }

    @Override
    public Symbols.NativeOperator operator() {
        return null;
    }

    @Override
    public CompoundTerm clone() {
        return null;
    }

    @Override
    public Term clone(Term[] replaced) {
        return null;
    }

    public Node addSceneAttr(Node n){
//        Node node = nodes.get(n.getId());

        return n;
    }

    public ConcurrentMap<Integer, SceneStructure> getSubScene() {
        return subScene;
    }

    public void setSubScene(ConcurrentMap<Integer, SceneStructure> subScene) {
        this.subScene = subScene;
    }

    public SceneStructure getUnrealScene() {
        return unrealScene;
    }

    public void setUnrealScene(SceneStructure unrealScene) {
        this.unrealScene = unrealScene;
    }

    public int getId0() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName0() {
        return name;
    }

    public void setName0(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

//    public long getStartTime() {
//        return startTime;
//    }
//
//    public void setStartTime(long startTime) {
//        this.startTime = startTime;
//    }
//
//    public long getEndTime() {
//        return endTime;
//    }
//
//    public void setEndTime(long endTime) {
//        this.endTime = endTime;
//    }

    public SSite getsSite() {
        return this.sSite;
    }

    public void setsSite(SSite sSite) {
        this.sSite = sSite;
    }

    public SIntent getsIntent() {
        return sIntent;
    }

    public void setsIntent(SIntent sIntent) {
        this.sIntent = sIntent;
    }

    public List<SPerson> getPersonList() {
        return personList;
    }

    public void setPersonList(List<SPerson> personList) {
        this.personList = personList;
    }

    public List<SMethod> getMethodList() {
        return methodList;
    }

    public void setMethodList(List<SMethod> methodList) {
        this.methodList = methodList;
    }

    public SWorldView getsWorldView() {
        return sWorldView;
    }

    public void setsWorldView(SWorldView sWorldView) {
        this.sWorldView = sWorldView;
    }

    public SEmotion getsEmotion() {
        return sEmotion;
    }

    public void setsEmotion(SEmotion sEmotion) {
        this.sEmotion = sEmotion;
    }

    public SRequest getsRequest() {
        return sRequest;
    }

    public void setsRequest(SRequest sRequest) {
        this.sRequest = sRequest;
    }

    public SResponse getsResponse() {
        return sResponse;
    }

    public void setsResponse(SResponse sResponse) {
        this.sResponse = sResponse;
    }

    public SContext getsContext() {
        return sContext;
    }

    public void setsContext(SContext sContext) {
        this.sContext = sContext;
    }

    public SOrder getsOrder() {
        return sOrder;
    }

    public void setsOrder(SOrder sOrder) {
        this.sOrder = sOrder;
    }

    public Integer getDadId() {
        return dadId;
    }

    public void setDadId(Integer dadId) {
        this.dadId = dadId;
    }

    public Integer getBrotherId() {
        return brotherId;
    }

    public void setBrotherId(Integer brotherId) {
        this.brotherId = brotherId;
    }

    public List<SAction> getsActionList() {
        return sActionList;
    }

    public void setsActionList(List<SAction> sActionList) {
        this.sActionList = sActionList;
    }

    public String getsTime() {
        return sTime;
    }

    public void setsTime(String sTime) {
        this.sTime = sTime;
    }

    public List<Entity> getEntityList() {
        return entityList;
    }

    public void setEntityList(List<Entity> entityList) {
        this.entityList = entityList;
    }

    public WorldStructure getWorldStructure() {
        return worldStructure;
    }

    public void setWorldStructure(WorldStructure worldStructure) {
        this.worldStructure = worldStructure;
    }
}
