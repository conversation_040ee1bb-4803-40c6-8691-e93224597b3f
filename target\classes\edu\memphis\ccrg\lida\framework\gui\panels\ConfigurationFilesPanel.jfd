JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {seq {comp jToolBar1::::390:x, space :::p}, comp threadPane::l::400:x}"
			"$verticalGroup": "par l {seq l {comp jToolBar1:::p:25:p, space :::p, comp threadPane::::215:x}}"
		} ) {
			name: "this"
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "jToolBar1"
				"rollover": true
				add( new FormComponent( "javax.swing.JTextField" ) {
					name: "fileNameTextField"
					"editable": false
				} )
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "threadPane"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "PropertiesTable"
					auxiliary() {
						"JavaCodeGenerator.preInitCode": "${field}.setModel(new PropertiesTableModel());"
					}
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
		} )
	}
}
