/**
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package automenta.vivisect.face;

import java.awt.Graphics;
import java.awt.Panel;


public class DrawGraphAppPanel extends Panel {

    private static final long serialVersionUID = 1L;
    private final GraphApp outer;

    public DrawGraphAppPanel(GraphApp graphanim, int i, final GraphApp outer) {
        this.outer = outer;
        rent = graphanim;
        type = i;
    }

    public void paint(Graphics g) {
        outer.draw(type);
    }
    private int type;
    private GraphApp rent;
}
