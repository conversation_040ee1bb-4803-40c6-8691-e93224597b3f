JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {comp jToolBar1::l:::x, comp jScrollPane1::l:::x}"
			"$verticalGroup": "par l {seq l {comp jToolBar1:::p:25:p, space :::p, comp jScrollPane1:::::x}}"
		} ) {
			name: "this"
			"minimumSize": new java.awt.Dimension( 200, 150 )
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "jToolBar1"
				"rollover": true
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "refreshButton"
					"text": "Refresh"
					"focusable": false
					"horizontalTextPosition": 0
					"maximumSize": new java.awt.Dimension( 50, 25 )
					"minimumSize": new java.awt.Dimension( 50, 25 )
					"preferredSize": new java.awt.Dimension( 50, 25 )
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "refreshButtonActionPerformed", true ) )
				} )
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "relaxButton"
					"text": "Relax"
					"toolTipText": "Relaxes the graph"
					"focusable": false
					"horizontalTextPosition": 0
					"maximumSize": new java.awt.Dimension( 50, 25 )
					"minimumSize": new java.awt.Dimension( 50, 25 )
					"preferredSize": new java.awt.Dimension( 50, 25 )
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "relaxButtonActionPerformed", true ) )
				} )
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "jScrollPane1"
				add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
					"$horizontalGroup": "par l {space :0:398:x}"
					"$verticalGroup": "par l {space :0:267:x}"
				} ) {
					name: "jPanel1"
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
