JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class java.awt.BorderLayout ) ) {
			name: "this"
			"defaultCloseOperation": 3
			"title": "LIDA Framework"
			"minimumSize": new java.awt.Dimension( 200, 100 )
			"$sizePolicy": 1
			"$locationPolicy": 1
			auxiliary() {
				"JavaCodeGenerator.preInitCode": "${field}.setExtendedState(MAXIMIZED_BOTH);"
			}
			add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
				name: "jSplitPane1"
				"orientation": 0
				auxiliary() {
					"JavaCodeGenerator.preInitCode": "${field}.setDividerLocation(GraphicsEnvironment.getLocalGraphicsEnvironment().getMaximumWindowBounds().height/2);"
				}
				add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
					name: "jSplitPane2"
					"minimumSize": new java.awt.Dimension( 5, 5 )
					"preferredSize": new java.awt.Dimension( 600, 300 )
					auxiliary() {
						"JavaCodeGenerator.preInitCode": "${field}.setDividerLocation(GraphicsEnvironment.getLocalGraphicsEnvironment().getMaximumWindowBounds().width/2);"
					}
					add( new FormContainer( "javax.swing.JTabbedPane", new FormLayoutManager( class javax.swing.JTabbedPane ) ) {
						name: "jTabbedPaneL"
						"preferredSize": new java.awt.Dimension( 300, 100 )
					}, new FormLayoutConstraints( class java.lang.String ) {
						"value": "left"
					} )
					add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
						name: "splitPane3"
						add( new FormContainer( "javax.swing.JTabbedPane", new FormLayoutManager( class javax.swing.JTabbedPane ) ) {
							name: "jTabbedPaneR"
							"preferredSize": new java.awt.Dimension( 150, 100 )
						}, new FormLayoutConstraints( class java.lang.String ) {
							"value": "left"
						} )
						add( new FormContainer( "javax.swing.JTabbedPane", new FormLayoutManager( class javax.swing.JTabbedPane ) ) {
							name: "tabbedPane1"
						}, new FormLayoutConstraints( class java.lang.String ) {
							"value": "right"
						} )
					}, new FormLayoutConstraints( class java.lang.String ) {
						"value": "right"
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "top"
				} )
				add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
					name: "splitPane1"
					add( new FormContainer( "javax.swing.JTabbedPane", new FormLayoutManager( class javax.swing.JTabbedPane ) ) {
						name: "chatPanel"
					}, new FormLayoutConstraints( class java.lang.String ) {
						"value": "left"
					} )
					add( new FormContainer( "javax.swing.JTabbedPane", new FormLayoutManager( class javax.swing.JTabbedPane ) ) {
						name: "principalTabbedPanel"
						"preferredSize": new java.awt.Dimension( 350, 150 )
						"minimumSize": new java.awt.Dimension( 250, 90 )
					}, new FormLayoutConstraints( class java.lang.String ) {
						"value": "right"
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "right"
				} )
			}, new FormLayoutConstraints( class java.lang.String ) {
				"value": "Center"
			} )
			menuBar: new FormContainer( "javax.swing.JMenuBar", new FormLayoutManager( class javax.swing.JMenuBar ) ) {
				name: "menuBar"
				"preferredSize": new java.awt.Dimension( 500, 21 )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "fileMenu"
					"text": "File"
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "guiRefreshRateMunuItem"
						"text": "Gui Refresh Rate..."
						addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "guiRefreshRateMunuItemActionPerformed", true ) )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "exitMenuItem"
						"text": "Exit"
						addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "exitMenuItemActionPerformed", true ) )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "panelsMenu"
					"text": "Panels"
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "addPanelMenuItem"
						"text": "Add new panel"
						addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "addPanelMenuItemActionPerformed", true ) )
					} )
					add( new FormComponent( "javax.swing.JPopupMenu$Separator" ) {
						name: "jSeparator1"
					} )
					add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
						name: "areaAPanelsMenu"
						"text": "Area A"
					} )
					add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
						name: "areaBPanelsMenu"
						"text": "Area B"
					} )
					add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
						name: "areaCPanelsMenu"
						"text": "Area C"
					} )
					add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
						name: "areaDPanelsMenu"
						"text": "Area D"
					} )
					add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
						name: "areaEPanelsMenu"
						"text": "Area E"
					} )
					add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
						name: "areaOthersPanelsMenu"
						"text": "Others"
					} )
					add( new FormComponent( "javax.swing.JPopupMenu$Separator" ) {
						name: "jSeparator2"
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "helpMenu"
					"text": "Help"
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "contentsMenuItem"
						"text": "Help"
						addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "contentsMenuItemActionPerformed", true ) )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "aboutMenuItem"
						"text": "About"
						addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "aboutMenuItemActionPerformed", true ) )
					} )
				} )
			}
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 745, 445 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
