package edu.memphis.ccrg.alife.gui;

import edu.memphis.ccrg.alife.elements.Cell;

import javax.swing.*;

/* access modifiers changed from: private */
public class AttrListModel extends AbstractListModel {
    private Cell cell;
    private Object[] objects;

    AttrListModel() {
    }

    public int getSize() {
        if (this.cell != null) {
            return this.objects.length;
        }
        return 0;
    }

    public Object getElementAt(int index) {
        return this.objects[index];
    }

    public void setCell(Cell cell2) {
        this.cell = cell2;
        this.objects = cell2.getObjects().toArray();
        fireContentsChanged(this, 0, this.objects.length - 1);
    }
}
