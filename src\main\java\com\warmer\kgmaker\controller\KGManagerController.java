package com.warmer.kgmaker.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.csvreader.CsvWriter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.warmer.kgmaker.config.*;
import com.warmer.kgmaker.entity.*;
import com.warmer.kgmaker.query.GraphQuery;
import com.warmer.kgmaker.service.*;
import com.warmer.kgmaker.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.Charset;
import java.util.*;

@Controller
@RequestMapping(value = "/")
public class KGManagerController extends BaseController {
	@Autowired
	private Neo4jUtil neo4jUtil;
	@Autowired
	private WebAppConfig config;
	@Autowired
	private IKGGraphService KGGraphService;
	@Autowired
	private IKnowledgegraphService kgservice;	// mysql

	@GetMapping("/")
	public String home(Model model) {
		return "kg/home";
//		return "kg/index";
//		return "index";
//		return "web-desktop/index";
	}

	@GetMapping("/2d")
	public String index(Model model) {
		return "kg/home";
	}

	@GetMapping("/3d")
	public String index0(Model model) {
		return "kg/index";
	}

	@ResponseBody
	@RequestMapping(value = "/getgraph") // call db.labels
	public R<GraphPageRecord<Map<String, Object>>> getgraph(GraphQuery queryItem) {
		R<GraphPageRecord<Map<String, Object>>> result = new R<>();
		GraphPageRecord<Map<String, Object>> resultRecord = new GraphPageRecord<>();

		Map<String,Object> visconfig = new HashMap<>();
		try {
			String name = "tc";
			PageHelper.startPage(queryItem.getPageIndex(), queryItem.getPageSize(), true);
			List<Map<String, Object>> domainList = kgservice.getDomainList(queryItem.getDomain(), name);
			PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(domainList);
			long total = pageInfo.getTotal();
			resultRecord.setPageIndex(queryItem.getPageIndex());
			resultRecord.setPageSize(queryItem.getPageSize());
			resultRecord.setTotalCount(new Long(total).intValue());
			resultRecord.setNodeList(pageInfo.getList());

			getConfig(visconfig);

			resultRecord.setVisbles(visconfig);

			result.code = 200;
			result.setData(resultRecord);
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/getdomaingraph")
	public R<Map<String, Object>> getDomainGraph(GraphQuery query) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		try {
			Map<String, Object> graphData = KGGraphService.getdomaingraph(query);
			result.code = 200;
			result.data = graphData;
			
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}
	@ResponseBody
	@RequestMapping(value = "/getcypherresult")
	public R<Map<String, Object>> getcypherresult(String cypher) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		String error="";
		try {
			Map<String, Object> graphData = neo4jUtil.excuteCypherSql(cypher,"GetGraphNodeAndShip").get(0);
			result.code = 200;
			result.data = graphData;
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			error=e.getMessage();
			result.setMsg("服务器错误");
		}
		finally {
			if(StringUtil.isNotBlank(error)){
				result.code = 500;
				result.setMsg(error);
			}
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/getrelationnodecount")
	public R<String> getrelationnodecount(String domain, long nodeid) {
		R<String> result = new R<String>();
		try {
			long totalcount = 0;
			if (!StringUtil.isBlank(domain)) {
				totalcount = KGGraphService.getrelationnodecount(domain, nodeid);
				result.code = 200;
				result.setData(String.valueOf(totalcount));
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/createdomain")
	public R<String> createdomain(String domain) {
		R<String> result = new R<String>();
		try {
			if (!StringUtil.isBlank(domain)) {
				List<Map<String, Object>> domainItem = kgservice.getDomainByName(domain);
				if (!domainItem.isEmpty()) {
					result.code = 300;
					result.setMsg("领域已存在");
				} else {
					saveMydm(domain);
					KGGraphService.createdomain(domain);// 保存到图数据
					result.code = 200;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	private void saveMydm(String domain) {
		String name = "tc";
		Map<String, Object> maps = new HashMap<String, Object>();
		maps.put("name", domain);
		maps.put("nodecount", 1);
		maps.put("shipcount", 0);
		maps.put("status", 1);
		maps.put("createuser", name);
		kgservice.saveDomain(maps);// 保存到mysql
	}

	// 查询图谱的所有标签列表，并更新到mysql
	@ResponseBody
	@RequestMapping(value = "/getlabellist")
	public R<List<Map>> getLabelList() {
		R<List<Map>> result = new R<List<Map>>();
		try {
			String cypherSql = "MATCH (n:Label) RETURN n";
			cypherSql = "CALL db.labels()";
			List<Map<String, Object>> labelList = neo4jUtil.excuteCypherSql(cypherSql, "excuteCypherSql");
			if (labelList != null && !labelList.isEmpty()) {
				result.code = 200;
				// 更新到mysql，保存
				Object obj = labelList.get(0).get("labels");
				List<String> labelStr = (ArrayList) obj;
				for (String label : labelStr) {
					List<Map<String, Object>> domainItem = kgservice.getDomainByName(label);
					if (domainItem.isEmpty()) {
						saveMydm(label);
					}
				}
			} else {
				result.code = 500;
				result.setMsg("服务器错误");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/getmorerelationnode")
	public R<Map<String, Object>> getmorerelationnode(String domain, String nodeid) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		try {
			if (!StringUtil.isBlank(domain)) {
				Map<String, Object> graphModel = KGGraphService.getmorerelationnode(domain, nodeid);
				if (graphModel != null) {
					result.code = 200;
					result.setData(graphModel);
					return result;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/updatenodename")
	public R<Map<String, Object>> updatenodename(String domain, String nodeid, String nodename) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		Map<String, Object> graphNodeList = new HashMap<String, Object>();
		try {
			if (!StringUtil.isBlank(domain)) {
				graphNodeList = KGGraphService.updatenodename(domain, nodeid, nodename);
				if (graphNodeList.size() > 0) {
					result.code = 200;
					result.setData(graphNodeList);
					return result;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/updateCorrdOfNode")
	public R<String> updateCorrdOfNode(String domain, String id, Double fx, Double fy) {
		R<String> result = new R<String>();
		try {
			KGGraphService.updateCorrdOfNode(domain, id, fx, fy);
			result.code = 200;
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/createnode")
	public R<Map<String, Object>> createnode(QAEntityItem entity, HttpServletRequest request, HttpServletResponse response) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		Map<String, Object> graphNode = new HashMap<String, Object>();
		try {
			// 对应目前所在标签面板，不理会修改后标签
			String domain = request.getParameter("domain");
			if ("".equals(entity.getName())) {
				entity.setName("00");
			}
			if("".equals(domain)){
				domain = "test";
			}
			graphNode = KGGraphService.createnode(domain, entity);
			if (graphNode!=null&&graphNode.size() > 0) {
				result.code = 200;
				result.setData(graphNode);
				return result;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}


	@ResponseBody
	@RequestMapping(value = "/updatenode")
	public R<Map<String, Object>> updatenode(String id, HttpServletRequest request) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		Map<String, Object> graphNode = new HashMap<String, Object>();
		try {
			// 不理会修改后标签.标签用cypher，这里只修改属性，批量
//			String id = request.getParameter("id");
			Map<String, String[]> attrs = request.getParameterMap();

			graphNode = KGGraphService.updatenode(id, attrs);
			if (graphNode!=null&&graphNode.size() > 0) {
				result.code = 200;
				result.setData(graphNode);
				return result;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}

	private String dir = "D:\\lida\\kgmaker-xr-master\\src\\main\\resources\\config";
	private String dir0 = "D:\\lida\\kgmaker-xr-master\\target\\classes\\config";
	private File saveFilePath = new File(dir + "\\visbles.txt");
	private File saveFilePath0 = new File(dir0 + "\\visbles.txt");

	@ResponseBody
	@RequestMapping(value = "/makevisible")
	public R<Map<String, Object>> makevisible(HttpServletRequest request, HttpServletResponse response) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
//		Map<String, Map<String, Object>> visconfig = new HashMap<>();
		Map<String, Object> visconfig = new HashMap<>();

		try {
			String domain = request.getParameter("domain");
			String color = request.getParameter("color");
			String r = request.getParameter("r");

			if (r == null)  r = "30";
			if(color == null) color = "#ff4555";

			if("".equals(domain)){
				domain = "test";
			}

			getConfig(visconfig);

			Map vis;
			BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(saveFilePath)));
			// 暂时妥协，直接改target
			BufferedWriter writer0 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(saveFilePath0)));
			boolean isexis = false;
			for (String key : visconfig.keySet()) {

				if (key.equals(domain)) {
					writer.write(key + "_" + color + "_" + r + "\r\n");
					writer0.write(key + "_" + color + "_" + r + "\r\n");
					isexis = true;
				}else {
					vis = (Map) visconfig.get(key);
					writer.write(key + "_" + vis.get("color") + "_" + vis.get("r") + "\r\n");
					writer0.write(key + "_" + vis.get("color") + "_" + vis.get("r") + "\r\n");
				}
			}
			// 没有现成设定要新加
			if (!isexis){
				writer.write(domain + "_" + color + "_" + r + "\r\n");
				writer0.write(domain + "_" + color + "_" + r + "\r\n");
			}


			writer.flush();
			writer.close();

			writer0.flush();
			writer0.close();

			result.code = 200;
			result.setData(visconfig);
			return result;

		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}

	private void getConfig(Map<String, Object> visconfig) throws IOException {
		Map<String, Object> domain;

		if (!saveFilePath.exists()){
			saveFilePath.createNewFile();
		}else {
			BufferedReader buffReader = new BufferedReader(new InputStreamReader(new FileInputStream(saveFilePath)));
			String strTmp = "";

			while((strTmp = buffReader.readLine())!=null){
				domain = new HashMap<>();
				String[] vis = strTmp.split("_");
//				domain.put("domain",vis[0]);
				domain.put("color",vis[1]);
				// vue 前端检验，必须符合类型
				domain.put("r",Integer.valueOf(vis[2]));
				visconfig.put(vis[0],domain);
			}
			buffReader.close();
		}
	}

	@ResponseBody
	@RequestMapping(value = "/batchcreatenode")
	public R<Map<String, Object>> batchcreatenode(String domain, String sourcename, String[] targetnames, String relation) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		Map<String, Object> rss = new HashMap<String, Object>();
		try {
			rss= KGGraphService.batchcreatenode(domain, sourcename, relation, targetnames);
			result.code = 200;
			result.setData(rss);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/batchcreatechildnode")
	public R<Map<String, Object>> batchcreatechildnode(String domain, String sourceid, Integer entitytype, String[] targetnames, String relation) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		Map<String, Object> rss = new HashMap<String, Object>();
		try {
			rss= KGGraphService.batchcreatechildnode(domain, sourceid, entitytype, targetnames, relation);
			result.code = 200;
			result.setData(rss);
			return result;

		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/batchcreatesamenode")
	public R<List<Map<String, Object>>> batchcreatesamenode(String domain, Integer entitytype, String[] sourcenames) {
		R<List<Map<String, Object>>> result = new R<List<Map<String, Object>>>();
		List<Map<String, Object>> rss = new ArrayList<Map<String, Object>>();
		try {
			rss=KGGraphService.batchcreatesamenode(domain, entitytype, sourcenames);
			result.code = 200;
			result.setData(rss);
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/createlink")
	public R<Map<String, Object>> createlink(String domain, long sourceid, long targetid, String ship,HttpServletRequest request) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		try {
			Map<String, Object> cypherResult = KGGraphService.createlink(domain, sourceid, targetid, ship);
			result.code = 200;
			result.setData(cypherResult);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}

		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/updatelink")
	public R<Map<String, Object>> updatelink(String id, HttpServletRequest request) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		Map<String, Object> cypherResult = new HashMap<>();
		Map<String, String[]> attrs = request.getParameterMap();
		try {
			cypherResult = KGGraphService.updatelink(id, attrs);
			result.code = 200;

		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		result.setData(cypherResult);
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/changelink")
	public R<Map<String, Object>> changelink(String type, long id, HttpServletRequest request) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		Map<String, Object> cypherResult = new HashMap<>();
//		String oldtype = request.getParameter("oldtype");
//		String oldname = request.getParameter("oldname");
		try {
			cypherResult = KGGraphService.changelink(type, id);
			result.code = 200;

		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		result.setData(cypherResult);
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/deletenode")
	public R<List<Map<String, Object>>> deletenode(String domain, long nodeid) {
		R<List<Map<String, Object>>> result = new R<List<Map<String, Object>>>();
		try {
			KGGraphService.deletenode(domain, nodeid);
			result.code = 200;
//			result.setData(rList);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/deletedomain")
	public R<List<Map<String, Object>>> deletedomain(Integer domainid, String domain) {
		R<List<Map<String, Object>>> result = new R<List<Map<String, Object>>>();
		try {
			kgservice.deleteDomain(domainid);
			KGGraphService.deleteKGdomain(domain);
			result.code = 200;
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/deletelink")
	public R<Map<String, Object>> deletelink(String domain, long shipid) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		try {
			KGGraphService.deletelink(domain, shipid);
			result.code = 200;
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/importgraph")
	public JSONObject importgraph(@RequestParam(value = "file", required = true) MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
		JSONObject res = new JSONObject();
		if (file == null) {
			res.put("code", "500");
			res.put("msg", "请先选择有效的文件");
			return res;
		}
		// 领域不能为空
		String label = request.getParameter("domain");
		if (StringUtil.isBlank(label)) {
			res.put("code", "500");
			res.put("msg", "请先选择领域");
			return res;
		}
		List<Map<String, Object>> dataList = getFormatData(file);
		try {
			List<List<String>> list = new ArrayList<>();
			for (Map<String, Object> item : dataList) {
				List<String> lst = new ArrayList<>();
				lst.add(item.get("sourcenode").toString());
				lst.add(item.get("targetnode").toString());
				lst.add(item.get("relationship").toString());
				list.add(lst);
			}
			String savePath = config.getLocation();
			String filename = "tc" + System.currentTimeMillis() + ".csv";
			CSVUtil.createCsvFile(list, savePath,filename);
			String serverUrl=request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
			String csvUrl = "http://"+serverUrl+ "/download/" + filename;
			//String csvUrl = "https://neo4j.com/docs/cypher-manual/3.5/csv/artists.csv";
			KGGraphService.batchInsertByCSV(label, csvUrl, 0);
			res.put("code", 200);
			res.put("message", "success!");
			return res;
		} catch (Exception e) {
			e.printStackTrace();
			res.put("code", 500);
			res.put("message", "服务器错误!");
		}
		return res;
	}
	private List<Map<String, Object>> getFormatData(MultipartFile file) throws Exception {
		List<Map<String, Object>> mapList = new ArrayList<>();
		try {
			String fileName = file.getOriginalFilename();
			if (!fileName.endsWith(".csv")) {
				Workbook workbook = null;
				if (ExcelUtil.isExcel2007(fileName)) {
					workbook = new XSSFWorkbook(file.getInputStream());
				} else {
					workbook = new HSSFWorkbook(file.getInputStream());
				}
				// 有多少个sheet
				int sheets = workbook.getNumberOfSheets();
				for (int i = 0; i < sheets; i++) {
					Sheet sheet = workbook.getSheetAt(i);
					int rowSize = sheet.getPhysicalNumberOfRows();
					for (int j = 0; j < rowSize; j++) {
						Row row = sheet.getRow(j);
						int cellSize = row.getPhysicalNumberOfCells();
						if (cellSize != 3) continue; //只读取3列
						row.getCell(0).setCellType(Cell.CELL_TYPE_STRING);
						Cell cell0 = row.getCell(0);//节点1
						row.getCell(1).setCellType(Cell.CELL_TYPE_STRING);
						Cell cell1 = row.getCell(1);//节点2
						row.getCell(2).setCellType(Cell.CELL_TYPE_STRING);
						Cell cell2 = row.getCell(2);//关系
						if (null == cell0 || null == cell1 || null == cell2) {
							continue;
						}
						String sourceNode = cell0.getStringCellValue();
						String targetNode = cell1.getStringCellValue();
						String relationShip = cell2.getStringCellValue();
						if (StringUtil.isBlank(sourceNode) || StringUtils.isBlank(targetNode) || StringUtils.isBlank(relationShip))
							continue;
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("sourcenode", sourceNode);
						map.put("targetnode", targetNode);
						map.put("relationship", relationShip);
						mapList.add(map);
					}
				}
			} else if (fileName.endsWith(".csv")) {
				List<List<String>> list = CSVUtil.readCsvFile(file);
				for (int i = 0; i < list.size(); i++) {
					List<String> lst = list.get(i);
					if (lst.size() != 3) continue;
					String sourceNode = lst.get(0);
					String targetNode = lst.get(1);
					String relationShip = lst.get(2);
					if (StringUtil.isBlank(sourceNode) || StringUtils.isBlank(targetNode) || StringUtils.isBlank(relationShip))
						continue;
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("sourcenode", sourceNode);
					map.put("targetnode", targetNode);
					map.put("relationship", relationShip);
					mapList.add(map);
				}
			}
		} catch (Exception ex) {
			throw new Exception(ex);
		}
		return mapList;
	}

	@ResponseBody
	@RequestMapping(value = "/exportgraph")// neo4j 导出图数据
	public JSONObject exportgraph(HttpServletRequest request, HttpServletResponse response) throws Exception {
		JSONObject res = new JSONObject();
		String label = request.getParameter("domain");
		String filePath = config.getLocation();
		String fileName = UUID.randomUUID() + ".csv";
		String fileUrl = File.separator + filePath + File.separator + fileName;
		String cypher = "";
		if (label == null || label.equals("")) {
//			res.put("code", 500);
//			res.put("message", "请选择要导出的领域!");
//			return res;
			cypher = "MATCH (n) -[r]->(m) return n.name as source,m.name as target,type(r) as relation";
		}else {
			cypher = String.format(
					"MATCH (n:%s) -[r]->(m:%s) return n.name as source,m.name as target,type(r) as relation", label, label);
		}
		List<Map<String, Object>> list = neo4jUtil.excuteCypherSql(cypher,"GetGraphItem");
		File file = new File(fileUrl);
		try {
			if (!file.exists()) {
				file.createNewFile();
				System.out.println("文件不存在，新建成功！");
			} else {
				System.out.println("文件存在！");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		CsvWriter csvWriter = new CsvWriter(fileUrl, ',', Charset.forName("UTF-8"));
		String[] header = { "source", "target", "relation" };
		csvWriter.writeRecord(header);
		for (Map<String, Object> hashMap : list) {
			int colSize = hashMap.size();
			String[] cntArr = new String[colSize];
			cntArr[0] = hashMap.get("source").toString().replace("\"", "");
			cntArr[1] = hashMap.get("target").toString().replace("\"", "");
			cntArr[2] = hashMap.get("relation").toString().replace("\"", "");
			try {
				csvWriter.writeRecord(cntArr);
			} catch (IOException e) {
				log.error("CSVUtil->createFile: 文件输出异常" + e.getMessage());
			}
		}
		csvWriter.close();
		String serverUrl=request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
		String csvUrl = serverUrl + "/kg/download/" + fileName;
		res.put("code", 200);
		res.put("csvurl", csvUrl);
		res.put("message", "success!");
		return res;

	}

	// 文件下载相关代码
	@GetMapping(value = "/download/{filename}")
	public String download(@PathVariable("filename") String filename, HttpServletRequest request, HttpServletResponse response) {
		String filePath = config.getLocation();
		String fileUrl = filePath + filename;
		if (fileUrl != null) {
			File file = new File(fileUrl);
			if (file.exists()) {
				//response.setContentType("application/force-download");// 设置强制下载不打开
				response.addHeader("Content-Disposition", "attachment;fileName=" + filename+".csv");// 设置文件名
				byte[] buffer = new byte[1024];
				FileInputStream fis = null;
				BufferedInputStream bis = null;
				try {
					fis = new FileInputStream(file);
					bis = new BufferedInputStream(fis);
					OutputStream os = response.getOutputStream();
					int i = bis.read(buffer);
					while (i != -1) {
						os.write(buffer, 0, i);
						i = bis.read(buffer);
					}
					System.out.println("success");
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					if (bis != null) {
						try {
							bis.close();
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
					if (fis != null) {
						try {
							fis.close();
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}
			}
		}
		return null;
	}

	@ResponseBody
	@RequestMapping(value = "/getnodeimage")
	public R<List<Map<String, Object>>> getNodeImagelist(int domainid, int nodeid) {
		R<List<Map<String, Object>>> result = new R<List<Map<String, Object>>>();
		try {
			List<Map<String, Object>> images = kgservice.getNodeImageList(domainid, nodeid);
			result.code = 200;
			result.setData(images);
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/getnodecontent")
	public R<Map<String, Object>> getNodeContent(int domainid, int nodeid) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		try {
			List<Map<String, Object>> contents = kgservice.getNodeContent(domainid, nodeid);
			if (contents != null && contents.size() > 0) {
				result.code = 200;
				result.setData(contents.get(0));
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/getnodedetail")
	public R<Map<String, Object>> getNodeDetail(int domainid, int nodeid) {
		R<Map<String, Object>> result = new R<Map<String, Object>>();
		try {
			Map<String, Object> res = new HashMap<String, Object>();
			res.put("content", "");
			res.put("imagelist", new String[] {});
			List<Map<String, Object>> contents = kgservice.getNodeContent(domainid, nodeid);
			if (contents != null && contents.size() > 0) {
				res.replace("content", contents.get(0).get("Content"));
			}
			List<Map<String, Object>> images = kgservice.getNodeImageList(domainid, nodeid);
			if (images != null && images.size() > 0) {
				res.replace("imagelist", images);
			}
			result.code = 200;
			result.setData(res);
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/savenodeimage")
	public R<String> saveNodeImage(@RequestBody Map<String, Object> params) {
		R<String> result = new R<String>();
		try {
			String username = "tc";
			int domainid = (int) params.get("domainid");
			String nodeid = params.get("nodeid").toString();
			String imagelist = params.get("imagelist").toString();
			List<Map<String, Object>> domainList = kgservice.getDomainById(domainid);
			if (domainList != null && domainList.size() > 0) {
				String domainName = domainList.get(0).get("name").toString();
				kgservice.deleteNodeImage(domainid, Integer.parseInt(nodeid));
				List<Map> imageItems = JSON.parseArray(imagelist, Map.class);
				List<Map<String, Object>> submitItemList = new ArrayList<Map<String, Object>>();
				if (!imageItems.isEmpty()) {
					for (Map<String, Object> item : imageItems) {
						String file = item.get("fileurl").toString();
						int sourcetype = 0;
						Map<String, Object> sb = new HashMap<String, Object>();
						sb.put("file", file);
						sb.put("imagetype", sourcetype);
						sb.put("domainid", domainid);
						sb.put("nodeid", nodeid);
						sb.put("status", 1);
						sb.put("createuser", username);
						sb.put("createtime", DateUtil.getDateNow());
						submitItemList.add(sb);
					}
				}
				if (submitItemList != null && submitItemList.size() > 0) {
					kgservice.saveNodeImage(submitItemList);
					// 更新到图数据库,表明该节点有附件,加个标识,0=没有,1=有
					KGGraphService.updateNodeFileStatus(domainName, Long.parseLong(nodeid), 1);
					result.code = 200;
					result.setMsg("操作成功");
				} else {
					KGGraphService.updateNodeFileStatus(domainName, Long.parseLong(nodeid), 0);
					result.code = 200;
					result.setMsg("操作成功");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}

	@ResponseBody
	@RequestMapping(value = "/savenodecontent")
	public R<String> savenodecontent(@RequestBody Map<String, Object> params) {
		R<String> result = new R<String>();
		try {
			String username = "tc";
			int domainid = (int) params.get("domainid");
			String nodeid = params.get("nodeid").toString();
			String content = params.get("content").toString();
			List<Map<String, Object>> domainList = kgservice.getDomainById(domainid);
			if (domainList != null && domainList.size() > 0) {
				String domainName = domainList.get(0).get("name").toString();
				// 检查是否存在
				List<Map<String, Object>> items = kgservice.getNodeContent(domainid, Integer.parseInt(nodeid));
				if (items != null && items.size() > 0) {
					Map<String, Object> olditem = items.get(0);
					Map<String, Object> item = new HashMap<String, Object>();
					item.put("domainid", olditem.get("DomainId"));
					item.put("nodeid", olditem.get("NodeId"));
					item.put("content", content);
					item.put("modifyuser", username);
					item.put("modifytime", DateUtil.getDateNow());
					kgservice.updateNodeContent(item);
					result.code = 200;
					result.setMsg("更新成功");
				} else {
					Map<String, Object> sb = new HashMap<String, Object>();
					sb.put("content", content);
					sb.put("domainid", domainid);
					sb.put("nodeid", nodeid);
					sb.put("status", 1);
					sb.put("createuser", username);
					sb.put("createtime", DateUtil.getDateNow());
					if (sb != null && sb.size() > 0) {
						kgservice.saveNodeContent(sb);
						result.code = 200;
						result.setMsg("保存成功");
					}
				}
				// 更新到图数据库,表明该节点有附件,加个标识,0=没有,1=有
				KGGraphService.updateNodeFileStatus(domainName, Long.parseLong(nodeid), 1);
			}

		} catch (Exception e) {
			e.printStackTrace();
			result.code = 500;
			result.setMsg("服务器错误");
		}
		return result;
	}
	
}
