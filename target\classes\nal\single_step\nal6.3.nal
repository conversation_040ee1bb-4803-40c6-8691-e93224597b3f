'********** variable unification

'What can be said about bird can also be said about robin.
<<bird --> $x> ==> <robin --> $x>>.

'What can be said about swimmer usually can also be said about robin.
<<swimmer --> $y> ==> <robin --> $y>>. %0.70;0.90%  

3

'What can be said about bird and swimmer can also be said about robin.
''outputMustContain('<(&&,<bird --> $1>,<swimmer --> $1>) ==> <robin --> $1>>. %1.00;0.81%')

'What can be said about bird or swimmer can also be said about robin.
''outputMustContain('<(||,<bird --> $1>,<swimmer --> $1>) ==> <robin --> $1>>. %0.70;0.81%')

'I guess what can be said about bird can also be said about swimmer.
''outputMustContain('<<bird --> $1> ==> <swimmer --> $1>>. %1.00;0.36%')

'I guess what can be said about swimmer can also be said about bird.
''outputMustContain('<<swimmer --> $1> ==> <bird --> $1>>. %0.70;0.45%')

'I guess bird and swimmer share most properties.
''outputMustContain('<<bird --> $1> <=> <swimmer --> $1>>. %0.70;0.45%')

