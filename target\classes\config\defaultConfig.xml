<?xml version="1.0" encoding="utf-8" ?>

<config>
    <!-- NarParameters -->
    <conf name="NOVELTY_HORIZON" value="100000"/>
    <conf name="DECISION_THRESHOLD" value="0.51"/>
    <conf name="CONCEPT_BAG_SIZE" value="5000"/>
    <conf name="CONCEPT_BAG_LEVELS" value="500"/>
    
    <conf name="DURATION" value="5"/>
    <conf name="HORIZON" value="1"/>
    
    <conf name="TRUTH_EPSILON" value="0.01"/>
    <conf name="BUDGET_EPSILON" value="0.0001"/>
    <conf name="BUDGET_THRESHOLD" value="0.01"/>
    
    <conf name="DEFAULT_CONFIRMATION_EXPECTATION" value="0.6"/>
    <conf name="ALWAYS_CREATE_CONCEPT" value="true"/>
    <conf name="DEFAULT_CREATION_EXPECTATION" value="0.66"/>
    <conf name="DEFAULT_CREATION_EXPECTATION_GOAL" value="0.6"/>
    
    <conf name="DEFAULT_JUDGMENT_CONFIDENCE" value="0.9"/>
    <conf name="DEFAULT_JUDGMENT_PRIORITY" value="0.8"/>
    <conf name="DEFAULT_JUDGMENT_DURABILITY" value="0.5"/>
    
    <conf name="DEFAULT_QUESTION_PRIORITY" value="0.9"/>
    <conf name="DEFAULT_QUESTION_DURABILITY" value="0.9"/>
    
    <conf name="DEFAULT_GOAL_CONFIDENCE" value="0.9"/>
    <conf name="DEFAULT_GOAL_PRIORITY" value="0.9"/>
    <conf name="DEFAULT_GOAL_DURABILITY" value="0.9"/>
    <conf name="DEFAULT_QUEST_PRIORITY" value="0.9"/>
    <conf name="DEFAULT_QUEST_DURABILITY" value="0.9"/>
    
    <conf name="BAG_THRESHOLD" value="1.0"/>
    <conf name="FORGET_QUALITY_RELATIVE" value="0.3"/>
    <conf name="REVISION_MAX_OCCURRENCE_DISTANCE" value="10"/>
    
    <conf name="TASK_LINK_BAG_SIZE" value="100"/>
    <conf name="TASK_LINK_BAG_LEVELS" value="10"/>
    
    <conf name="TERM_LINK_BAG_SIZE" value="100"/>
    <conf name="TERM_LINK_BAG_LEVELS" value="10"/>
    <conf name="TERM_LINK_MAX_MATCHED" value="10"/>
    
    <conf name="NOVEL_TASK_BAG_SIZE" value="1000"/>
    <conf name="NOVEL_TASK_BAG_LEVELS" value="100"/>
    <conf name="NOVEL_TASK_BAG_SELECTIONS" value="100"/>
    
    <conf name="SEQUENCE_BAG_SIZE" value="500"/>
    <conf name="SEQUENCE_BAG_LEVELS" value="50"/>
    
    <conf name="OPERATION_BAG_SIZE" value="10"/>
    <conf name="OPERATION_BAG_LEVELS" value="10"/>
    <conf name="OPERATION_SAMPLES" value="6"/>
    
    <conf name="PROJECTION_DECAY" value="0.1"/>
    
    <conf name="MAXIMUM_EVIDENTAL_BASE_LENGTH" value="20000"/>
    
    <conf name="TERMLINK_MAX_REASONED" value="3"/>
    <conf name="TERM_LINK_RECORD_LENGTH" value="10"/>
    
    <conf name="CONCEPT_BELIEFS_MAX" value="28"/>
    <conf name="CONCEPT_QUESTIONS_MAX" value="5"/>
    <conf name="CONCEPT_GOALS_MAX" value="7"/>
    
    <conf name="reliance" value="0.9"/>
    <conf name="DISCOUNT_RATE" value="0.5"/>
    
    <conf name="IMMEDIATE_ETERNALIZATION" value="true"/>
    <conf name="SEQUENCE_BAG_ATTEMPTS" value="10"/>
    <conf name="CONDITION_BAG_ATTEMPTS" value="10"/>
    
    <conf name="DERIVATION_PRIORITY_LEAK" value="0.4"/>
    <conf name="DERIVATION_DURABILITY_LEAK" value="0.4"/>
    
    <conf name="CURIOSITY_DESIRE_CONFIDENCE_MUL" value="0.1"/>
    <conf name="CURIOSITY_DESIRE_PRIORITY_MUL" value="0.1"/>
    <conf name="CURIOSITY_DESIRE_DURABILITY_MUL" value="0.3"/>
    <conf name="CURIOSITY_FOR_OPERATOR_ONLY" value="false"/>
    
    <conf name="BREAK_NAL_HOL_BOUNDARY" value="false"/>
    
    <conf name="QUESTION_GENERATION_ON_DECISION_MAKING" value="false"/>
    <conf name="HOW_QUESTION_GENERATION_ON_DECISION_MAKING" value="false"/>
    
    <conf name="ANTICIPATION_CONFIDENCE" value="0.1"/>
    <conf name="ANTICIPATION_TOLERANCE" value="100.0"/>
    <conf name="RETROSPECTIVE_ANTICIPATIONS" value="false"/>
    
    <conf name="SATISFACTION_TRESHOLD" value="0.0"/>
    <conf name="COMPLEXITY_UNIT" value="1.0"/>
    
    <conf name="INTERVAL_ADAPT_SPEED" value="4.0"/>
    <conf name="TASKLINK_PER_CONTENT" value="4"/>
    
    <conf name="DEFAULT_FEEDBACK_PRIORITY" value="0.9"/>
    <conf name="DEFAULT_FEEDBACK_DURABILITY" value="0.5"/>
    
    <conf name="CONCEPT_FORGET_DURATIONS" value="2.0"/>
    <conf name="TERMLINK_FORGET_DURATIONS" value="10.0"/>
    <conf name="TASKLINK_FORGET_DURATIONS" value="4.0"/>
    <conf name="EVENT_FORGET_DURATIONS" value="4.0"/>
    
    <conf name="VARIABLE_INTRODUCTION_COMBINATIONS_MAX" value="8"/>
    <conf name="VARIABLE_INTRODUCTION_CONFIDENCE_MUL" value="0.9"/>
    <conf name="ANTICIPATIONS_PER_CONCEPT_MAX" value="8"/>
    <conf name="MOTOR_BABBLING_CONFIDENCE_THRESHOLD" value="0.8"/>
    
    <conf name="THREADS_AMOUNT" value="1"/>
    <conf name="VOLUME" value="100"/>
    <conf name="MILLISECONDS_PER_STEP" value="0"/>
    <conf name="STEPS_CLOCK" value="true"/>

    <conf name="DERIVATION_DURABILITY_LEAK" value="0.4"/>
    <conf name="DERIVATION_PRIORITY_LEAK" value="0.4"/>
    
    <!-- plugins -->
    <plugins>
        <!-- plugins -->
        <plugin classpath="org.opennars.plugin.mental.Emotions">
            <arg type="float.class" value="0.25" name="HAPPY_EVENT_LOWER_THRESHOLD"/>
            <arg type="float.class" value="0.75" name="HAPPY_EVENT_HIGHER_THRESHOLD"/>
            <arg type="float.class" value="0.1" name="BUSY_EVENT_LOWER_THRESHOLD"/>
            <arg type="float.class" value="0.9" name="BUSY_EVENT_HIGHER_THRESHOLD"/>
            <arg type="int.class" value="1000" name="CHANGE_STEPS_DEMANDED"/>
        </plugin>
        <plugin classpath="org.opennars.operator.mental.Anticipate">
            <arg type="float.class" value="0.1" name="ANTICIPATION_DURABILITY_MUL"/>
            <arg type="float.class" value="0.1" name="ANTICIPATION_PRIORITY_MUL"/>
        </plugin>
        <plugin classpath="org.opennars.plugin.mental.InternalExperience">
            <arg type="float.class" value="0.3" name="MINIMUM_PRIORITY_TO_CREATE_WANT_BELIEVE_ETC"/>
            <arg type="float.class" value="0.3" name="MINIMUM_PRIORITY_TO_CREATE_WONDER_EVALUATE"/>
            <arg type="float.class" value="0.0001" name="INTERNAL_EXPERIENCE_PROBABILITY"/>
            <arg type="float.class" value="0.000025" name="INTERNAL_EXPERIENCE_RARE_PROBABILITY"/>
            <arg type="float.class" value="0.1" name="INTERNAL_EXPERIENCE_DURABILITY_MUL"/>
            <arg type="float.class" value="0.1" name="INTERNAL_EXPERIENCE_PRIORITY_MUL"/>
            <arg type="boolean.class" value="true" name="ALLOW_WANT_BELIEF"/>
            <arg type="boolean.class" value="false" name="OLD_BELIEVE_WANT_EVALUATE_WONDER_STRATEGY"/>
            <arg type="boolean.class" value="false" name="FULL_REFLECTION"/>
        </plugin>

        <plugin classpath="org.opennars.plugin.perception.VisionChannel">
            <arg type="String.class" value="BRIGHT" name="label"/>
            <arg isReasoner="nar" name="Nar instance"/>
            <arg isReasoner="nar" name="reportResultsTo"/>
            <arg type="int.class" value="5" name="width"/>
            <arg type="int.class" value="5" name="height"/>
            <arg type="int.class" value="25" name="duration"/>
            <arg type="float.class" value="0.1" name="defaultOutputConfidence"/>
            <arg type="int.class" value="0" name="nPrototypes"/>
        </plugin>

        <!-- example Operators -->
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^break"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^drop"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^go-to"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^open"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^pick"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^strike"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^throw"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^activate"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^deactivate"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^lighter"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^reshape"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^escape"/>
        </plugin>
<!--        <plugin classpath="org.opennars.operator.misc.Say">-->
<!--            <arg type="String.class" value="^say"/>-->
<!--        </plugin>-->
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^right"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^left"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^run"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^feel"/>
        </plugin>
        <plugin classpath="org.opennars.operator.NullOperator">
            <arg type="String.class" value="^system"/>
        </plugin>

        <plugin classpath="org.opennars.operator.NullOperator"/>
        
        <plugin classpath="org.opennars.operator.mental.Believe"/>
        <plugin classpath="org.opennars.operator.mental.Want"/>
        <plugin classpath="org.opennars.operator.mental.Wonder"/>
        <plugin classpath="org.opennars.operator.mental.Evaluate"/>

        <plugin classpath="org.opennars.operator.mental.Remind"/>
        <plugin classpath="org.opennars.operator.mental.Consider"/>
        <plugin classpath="org.opennars.operator.mental.Name"/>
        <plugin classpath="org.opennars.operator.mental.Register"/>

        <plugin classpath="org.opennars.operator.mental.Doubt"/>
        <plugin classpath="org.opennars.operator.mental.Hesitate"/>

        <plugin classpath="org.opennars.operator.misc.Reflect"/>

        <plugin classpath="org.opennars.operator.mental.FeelSatisfied"/>
        <plugin classpath="org.opennars.operator.mental.FeelBusy"/>

        <plugin classpath="org.opennars.operator.misc.Count"/>
        <plugin classpath="org.opennars.operator.misc.Add"/>
    </plugins>
</config>

