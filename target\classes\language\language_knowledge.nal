// === Basic subject predicate
// ex:"tim eats ice"
<(&&,<$V --> VERB>,(#,$N1,$V,$N2)) ==> <(*,(/,REPRESENT,$N1,_),(/,REPRESENT,$N2,_)) --> (/,REPRESENT,$V,_)>>.
//For determiners:
<(&&,<#a --> DET>,(#,$N1,$V,#a,$N2)) ==> <(*,(/,REPRESENT,$N1,_),(/,REPRESENT,$N2,_)) --> (/,REPRESENT,$V,_)>>.
<(&&,<#a --> DET>,(#,#a,$N1,$V,$N2)) ==> <(*,(/,REPRESENT,$N1,_),(/,REPRESENT,$N2,_)) --> (/,REPRESENT,$V,_)>>.
//With prepositions:
$0.1;0.1$ <(&&,<#in --> PLACE_PREP>,(#,#in,$PLACE)) ==> <(/,REPRESENT,$PLACE,_) --> PLACE>>.
$0.1;0.1$ <(&&,<#at --> TIME_PREP>,(#,#at,$TIME)) ==> <(/,REPRESENT,$TIME,_) --> TIME>>.
//Subject or object:
$0.1;0.1$ <(&&,<#a --> DET>,(#,#a,$NOUN)) ==> <(/,REPRESENT,$NOUN,_) --> IT>>.
//Determiners:
<"a" --> DET>.
<"an" --> DET>.
<"the" --> DET>.
//Prepositions:
<"in" --> PLACE_PREP>.
<"at" --> TIME_PREP>.
<"at" --> PLACE_PREP>.
<"on" --> PLACE_PREP>.
<"to" --> PLACE_PREP>.
//Verbs:
<"is" --> VERB>.
<"are" --> VERB>.
<"were" --> VERB>.
<"was" --> VERB>.
<"likes" --> VERB>.
<"hates" --> VERB>.
<"eats" --> VERB>.
<"walks" --> VERB>.
<"goes" --> VERB>.
<"drinks" --> VERB>.
//Question words
<"what" --> QWORD>.
<"who" --> QWORD>.
<"where" --> QWORD>.
<"when" --> QWORD>.
