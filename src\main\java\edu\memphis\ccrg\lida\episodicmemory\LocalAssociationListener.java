/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.episodicmemory;

import edu.memphis.ccrg.lida.framework.ModuleListener;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;

/**
 * Listen to response from Episodic memory to a previous cue.
 * This cue need not originate from this same Listener. Any class can
 * originate the cue even if it does not implement this interface.
 *
 * 聆听情景记忆对先前提示的反应。 *此提示不必源自同一侦听器。即使没有实现此接口，任何类都可以*发出提示
 * <AUTHOR> J. McCall
  */
public interface LocalAssociationListener extends ModuleListener{
	
	/**
	 * @param association The response generated from the Episodic Memory to a previous cue.
	 *                    从情节记忆生成的对先前提示的响应
	 */
	public void receiveLocalAssociation(NodeStructure association);

}
