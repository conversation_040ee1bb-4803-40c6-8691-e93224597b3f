'********** compound composition, two premises

'If robin is a type of bird then robin is a type of animal. 
<<robin --> bird> ==> <robin --> animal>>. 

'If robin can fly then robin is a type of animal. 
<<robin --> [flying]> ==> <robin --> animal>>. %0.9% 

19

'If robin can fly and is a type of bird then robin is a type of animal. 
''outputMustContain('<(&&,<robin --> [flying]>,<robin --> bird>) ==> <robin --> animal>>. %1.00;0.81%')

'If robin can fly or is a type of bird then robin is a type of animal. 
''outputMustContain('<(||,<robin --> [flying]>,<robin --> bird>) ==> <robin --> animal>>. %0.90;0.81%')

