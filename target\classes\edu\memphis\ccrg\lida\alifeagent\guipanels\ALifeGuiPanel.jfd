JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {comp aLifePanel1::l::375:x}"
			"$verticalGroup": "par l {comp aLifePanel1::l::285:x}"
		} ) {
			name: "this"
			"preferredSize": new java.awt.Dimension( 400, 300 )
			add( new FormComponent( "edu.memphis.ccrg.alife.gui.ALifePanel" ) {
				name: "aLifePanel1"
				"preferredSize": new java.awt.Dimension( 400, 300 )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 375, 285 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
