package edu.memphis.ccrg.lida.data;

import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkCategory;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.pam.PamLinkImpl;
import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.pam.PamNodeImpl;
import edu.memphis.ccrg.linars.Term;
import org.jetbrains.annotations.NotNull;
import org.opennars.entity.Sentence;
import org.opennars.language.Statement;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.pam;

public class TermUtil {
    // 将term转为link
    public static Link termToLink(Term term0) {
        Link link = new PamLinkImpl();
        Statement term = (Statement) term0;
        link.setSource(term.getSubject());
        link.setSink(term.getPredicate());
        link.setCategory(new LinkCategory() {
            @Override
            public String getName() {
                return "term2link";
            }

            @Override
            public int getNodeId() {
                return 10000;
            }
        });
        return link;
    }

    public static Link termToLink0(Sentence fromGoal, Term term, String s) {
        Link link = new PamLinkImpl();
        link.setSource(fromGoal.getTerm());
        link.setSink(term);
        link.setCategory(new PamNodeImpl(s));
        return link;
    }
    // todo 直接term转回报错，id问题，节点也需要转pam点。目前也只预设测试，原需neo查到真实点边
    public static Link termToLink1(Term fromGoal, Term term, String s) {
        PamLink link;
        link = new PamLinkImpl();
        PamNode category = new PamNodeImpl();
        category.setNodeName(s);
        category.setNodeId(10000);
        PamNode fromNode = null;
        fromNode = getPamNode(fromGoal.toString(), 10001);
        link.setSource(fromNode);
        PamNode toNode = null;
        toNode = getPamNode(term.toString(), 10002);
        link.setSink(toNode);
        link.setCategory(category);
        return link;
    }

    @NotNull
    public static PamNode getPamNode(String str,int id) {
        PamNode toNode;
//        String toStr = term.toString();
        toNode = (PamNode) pam.getNode(str);
        if (toNode == null) {
            toNode = (PamNode) pam.getPamNodeStructure().getNeoNode(str);
            if (toNode == null) {
                toNode = new PamNodeImpl();
                toNode.setNodeName(str);
                toNode.setNodeId(id);
            }
        }
        return toNode;
    }

    public static int countStr(String varname, String s) {
        int count = 0;
        int index = 0;
        while ((index = varname.indexOf(s, index)) != -1) {
            index = index + s.length();
            count++;
        }
        return count;
    }
}
