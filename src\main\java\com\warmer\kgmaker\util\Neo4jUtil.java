package com.warmer.kgmaker.util;

import com.alibaba.fastjson.JSONObject;
import org.neo4j.graphdb.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;

@Component
public class Neo4jUtil {
//	public static DatabaseManagementService managementService = null;

//	public static String filename0 = "lida-language\\graph.db";
//	public static String filename1 = "data-20210618\\graph.db";

//	public static String filename = "D:\\data\\" + filename1;
//	public static String filename = "D:\\soft\\neo4j-community-4.3.2\\data\\databases\\graph.db";
//
//	public static final File databaseDirectory = new File(filename);
//
////	public static GraphDatabaseService graphDb = new GraphDatabaseFactory().newEmbeddedDatabase(databaseDirectory);
////	public static GraphDatabaseService graphDb = new DatabaseManagementServiceBuilder(databaseDirectory)
////														.build().database(DEFAULT_DATABASE_NAME);
//
//	private DatabaseManagementService managementService = new DatabaseManagementServiceBuilder(databaseDirectory).build();
//
//	public GraphDatabaseService graphDb = managementService.database( DEFAULT_DATABASE_NAME );

//	registerShutdownHook( managementService );

//	private void registerShutdownHook(final DatabaseManagementService managementService) {
//		// Registers a shutdown hook for the Neo4j instance so that it
//		// shuts down nicely when the VM exits (even if you "Ctrl-C" the
//		// running example before it's completed)
//		// 为 Neo4j 实例注册一个关闭钩子，以便它在 VM 退出时很好地关闭（即使你在它完成之前“Ctrl-C”运行示例）
//		Runtime.getRuntime().addShutdownHook(new Thread() {
//			@Override
//			public void run() {
//				managementService.shutdown();
//			}
//		});
//	}

    public static Map<String, Object> parameters = new HashMap<String, Object>();

    public List<Map<String, Object>> excuteCypherSql(String cypherSql, String method) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> mapList = new ArrayList<>();

        switch (method) {
            case "GetGraphNode":
                mapList = GetGraphNode(cypherSql);
                break;
            case "GetGraphRelationShip":
                mapList = GetGraphRelationShip(cypherSql);
                break;
            case "GetGraphItem":
                mapList = GetGraphItem(cypherSql);
                break;
            case "GetEntityList":
                mapList = GetEntityList(cypherSql);
                break;
            case "GetGraphNodeAndShip":
                mapList = GetGraphNodeAndShip(cypherSql);
                break;
            case "excuteCypherSql":
//				System.out.println(cypherSql);
//				try (Transaction tx = graphDb.beginTx()) {
//					try (Result result = tx.execute(cypherSql, parameters )) {
//						// todo something
//					}
//					tx.commit();
//				}

                mapList = GetGraphNodeAndShip(cypherSql);
                break;

            default:
                System.out.println("Invalid grade");
        }
        return mapList;
    }

    // 返回所有：match(n) OPTIONAL MATCH (n)-[r]-(m) RETURN *

    public List<Map<String, Object>> GetGraphNode(String cypherSql) {
        List<Map<String, Object>> ents = new ArrayList<Map<String, Object>>();
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(cypherSql, parameters)) {
                System.out.println(cypherSql);
                Map<String, Object> row;
                Node node;
                while (result.hasNext()) {
                    row = result.next();
                    for (String key : result.columns()) {
                        getNode(ents, row, key);
                    }
                }
            }
            tx.commit();
        }
        return ents;
    }

    private void getNode(List<Map<String, Object>> ents, Map<String, Object> row, String key) {
        Node node;
        node = (Node) row.get(key);
        String id = String.valueOf(node.getId());
        Map<String, Object> map = node.getAllProperties();
        map.put("id", id);
        int num = 0;
        for (Label label : node.getLabels()) {
            map.put("label" + num, label.name());
            num++;
        }

        ents.add(map);
    }

    public List<Map<String, Object>> GetGraphRelationShip(String cypherSql) {
        List<Map<String, Object>> ents = new ArrayList<Map<String, Object>>();
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(cypherSql, parameters)) {
                System.out.println(cypherSql);
                Map<String, Object> row;
                Relationship re;
                while (result.hasNext()) {
                    row = result.next();
                    for (String key : result.columns()) {
                        re = (Relationship) row.get(key);
                        String id = String.valueOf(re.getId());
                        String sourceid = String.valueOf(re.getStartNodeId());
                        String targetid = String.valueOf(re.getEndNodeId());
                        Map<String, Object> map = re.getAllProperties();
                        removeUndefined(map);

                        map.put("type", re.getType().name());
//						map.put("name",re.getProperty("name"));
                        map.put("id", id);
                        map.put("sourceid", sourceid);
                        map.put("targetid", targetid);
                        ents.add(map);
                    }
                }
            }
            tx.commit();
        }
        return ents;
    }

    // 如果object为undefined，则删掉这个属性
    public static void removeUndefined(Map<String, Object> map) {
        Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Object> entry = it.next();
            if (entry.getValue() == null || entry.getValue().equals("undefined")) {
                it.remove();
            }
        }
    }

    public List<Map<String, Object>> GetGraphItem(String cypherSql) {
        List<Map<String, Object>> ents = new ArrayList<Map<String, Object>>();
        List<String> nodeids = new ArrayList<String>();
        List<String> shipids = new ArrayList<String>();
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(cypherSql, parameters)) {
                System.out.println(cypherSql);
                Map<String, Object> row;
                Relationship re;
                while (result.hasNext()) {
                    row = result.next();
                    for (String key : result.columns()) {

//						List<Pair<String, Valu>> f = recordItem.fields();
//						Map<String, Object> rss = new HashMap<String, Object>();
//						for (Pair<String, Valu> pair : f) {
//							String typeName = pair.value().type().name();
//							if (typeName.equals("NODE")) {
//								Node node = pair.value().asNode();
//								String id = String.valueOf(node.getId());
//								if(!nodeids.contains(id)) {
//									Map<String, Object> map = node.asMap();
//									map.put("id", id);
//								}
//							}else if (typeName.equals("RELATIONSHIP")) {
//								Relationship re = pair.value().asRelationship();
//								String id = String.valueOf(re.getId());
//								if (!shipids.contains(id)) {
//									String sourceid = String.valueOf(re.getStartNodeId());
//									String targetid = String.valueOf(re.getEndNodeId());
//									Map<String, Object> map = re.getAllProperties();;
//									map.put("id", id);
//									map.put("sourceid", sourceid);
//									map.put("targetid", targetid);
//								}
//							}else {
//								rss.put(pair.key(),pair.value().toString());
//							}
//							ents.add(map);
//						}

                    }
                }
            }
            tx.commit();
        }
        return ents;
    }

    /*
     * 获取图谱节点数量
     */
    public long GetGraphValue(String cypherSql) {
        long val = 0;
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(cypherSql, parameters)) {
                Map<String, Object> row;
                if (result.hasNext()) {
                    row = result.next();
                    val = (Long) row.get(result.columns().get(0));
                }
            }
            tx.commit();
        }
        return val;
    }

    public List<Map<String, Object>> GetGraphNodeAndShip(String cypherSql) {
        List<Map<String, Object>> ents0 = new ArrayList<Map<String, Object>>();
        Map<String, Object> mo = new HashMap<String, Object>();
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(cypherSql, parameters)) {
                System.out.println(cypherSql);
                Map<String, Object> row;
                Relationship re;
                Node node;
                Node node0;
                Path path;
                Map<String, Object> map;
                List<Map<String, Object>> ents;
                List<Map<String, Object>> ships;

                Set<String> labels = new HashSet<>(); // 新增标签集合
                Set<Map<String, Object>> ents1 = new HashSet<>();
                Set<Map<String, Object>> ships1 = new HashSet<>();
                String id;
                while (result.hasNext()) {
                    row = result.next();
                    JSONObject jj;
                    for (String key : result.columns()) {
                        if (row.get(key) instanceof Relationship) {
                            re = (Relationship) row.get(key);
                            getRelateSet(re, ships1);

                            node = re.getStartNode();
                            getNodeSet(node, ents1);

                            node0 = re.getEndNode();
                            getNodeSet(node0, ents1);
                        } else if (row.get(key) instanceof Node) {
                            node = (Node) row.get(key);
                            getNodeSet(node, ents1);
                            // 获取标签 输出
                            for (Label label : node.getLabels()) {
                                labels.add(label.name());
                            }
                        } else if (row.get(key) instanceof Path) {
                            path = (Path) row.get(key);
                            for (Relationship rrr : path.relationships()) {
                                getRelateSet(rrr, ships1);

                                node = rrr.getStartNode();
                                getNodeSet(node, ents1);

                                node0 = rrr.getEndNode();
                                getNodeSet(node0, ents1);
                            }
                        } else if (key.equals("label")) {
                            String label = (String) row.get(key);
                            System.out.println("Label类型---" + label);
                            labels.add(label);
                        } else {
                            jj = new JSONObject(row);
                            System.out.println("其他类型---" + jj.size());
                            mo.put(key, row.get(key));
                        }
                    }

                    ents = new ArrayList<>(ents1);
                    ships = new ArrayList<>(ships1);
                    // 注意这里我们将 Set 转换为 List
                    mo.put("labels", new ArrayList<>(labels));

                    mo.put("nodes", ents);
                    mo.put("relationships", ships);
                }
                ents0.add(mo);
            }
            tx.commit();
        }
        return ents0;
    }

    private void getRelateSet(Relationship re, Set<Map<String, Object>> ships1) {
        String id;
        Map<String, Object> map = new HashMap<>();
        id = String.valueOf(re.getId());
        String sourceid = String.valueOf(re.getStartNodeId());
        String targetid = String.valueOf(re.getEndNodeId());
        map = re.getAllProperties();
        map.put("id", id);
        map.put("startNodeId", sourceid);
        map.put("endNodeId", targetid);
        map.put("type", re.getType().name());
//		map.put("properties",re.getAllProperties());
//		map.put("name",re.getProperty("name"));
        ships1.add(map);
    }

    private void getNodeSet(Node node, Set<Map<String, Object>> ents1) {
        String id;
        Map<String, Object> map = new HashMap<>();
        id = String.valueOf(node.getId());
        map = node.getAllProperties();
//		Map<String, Object> properties = node.getAllProperties();
        removeUndefined(map);
//		map.put("properties",properties);
        map.put("id", id);
        int num = 0;
        List labels = new ArrayList<>();
        for (Label label : node.getLabels()) {
//			map.put("label" + num,label.name());
//			num ++;
            labels.add(label.name());
        }
        map.put("labels", labels);
        ents1.add(map);
    }

    /**
     * 获取实体列表
     *
     * @param cypherSql
     * @return
     */
    public List<Map<String, Object>> GetEntityList(String cypherSql) {
        List<Map<String, Object>> ents = new ArrayList<Map<String, Object>>();
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(cypherSql, parameters)) {
                System.out.println(cypherSql);
                Map<String, Object> row;
                Relationship re;
                while (result.hasNext()) {
                    row = result.next();
                    for (String key : result.columns()) {
//						Map<String, Object> rss = new HashMap<String, Object>();
//						List<Pair<String, Valu>> f = recordItem.fields();
//						for (Pair<String, Valu> pair : f) {
//							String typeName = pair.value().type().name();
//							if (typeName.equals("NULL")) {
//								continue;
//							} else if (typeName.equals("NODE")) {
//								Node node = pair.value().asNode();
//								Map<String, Object> map = node.asMap();
//
//							} else if (typeName.equals("RELATIONSHIP")) {
//								Relationship re = pair.value().asRelationship();
//								Map<String, Object> map = re.getAllProperties();;
//
//							} else if (typeName.equals("PATH")) {
//
//							} else if (typeName.contains("LIST")) {
//								rss.put(pair.key(), pair.value().asList());
//							} else if (typeName.contains("MAP")) {
//								rss.put(pair.key(), pair.value().asMap());
//							} else {
//								rss.put(pair.key(), pair.value().toString());
//							}
//						}
//						ents.add(map);
                    }
                }

            }
        }
        return ents;
    }

//	public <T> List<T> GetEntityItemList(String cypherSql, Class<T> type) {
//		List<Map<String, Object>> ents=GetGraphNode(cypherSql,"");
//		List<T> model = HashMapToObject(ents, type);
//		return model;
//	}

    public Integer executeScalar(String cypherSql) {
        Integer count = 0;
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(cypherSql, parameters)) {

                System.out.println(cypherSql);
                Map<String, Object> row;
                Relationship re;
                while (result.hasNext()) {
                    row = result.next();
                    count = (Integer) row.get(result.columns().get(0));
                }
            }
        }

        return count;
    }
    // 获得关系的数量，用于判断关系是否存在，RelevantEntity意思是相关实体
//	public Map<String, Object> GetRelevantEntity(String cypherSql) {
//		Map<String, Object> rss = new HashMap<String, Object>();
//		try {
//			Result result = (Result) excuteCypherSql(cypherSql).get("result");
//			       Map<String, Object> row;
//			   Relationship re;
//			   while ( result.hasNext() ) {
//				row = result.next();
//				for (String key : result.columns()) {
//					Map<String, Object> r = recordItem.asMap();
//					System.out.println(JSON.toJSONString(r));
//					String key0 = r.get("key").toString();
//					if (rss.containsKey(key)) {
//						String oldValu = rss.get(key).toString();
//						String newValu = oldValu + "," + r.get("value");
//						rss.replace(key, newValu);
//					} else {
//						rss.put(key0, r.get("value"));
//					}
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return rss;
//	}

    public String getFilterPropertiesJson(String jsonStr) {
        String propertiesString = jsonStr.replaceAll("\"(\\w+)\"(\\s*:\\s*)", "$1$2"); // ȥ��key������
        // 如果字符串不包含“weight”，则添加weight属性
        if (!propertiesString.contains("weight")) {
            // 用（",\"weight\":\"0.8\"}"）替换右大括号
            propertiesString = propertiesString.replaceAll("}$", ",weight:\"0.8\"}");
//			propertiesString = propertiesString + ",\"weight\":\"0.8\"";
        }
        return propertiesString;
    }

    public <T> String getkeyvalCyphersql(T obj) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<String> sqlList = new ArrayList<String>();
        // 创建一个Class对象，获取类的名称
        Class userCla = obj.getClass();
        // 获取类中的所有属性集合
        Field[] fs = userCla.getDeclaredFields();
        for (int i = 0; i < fs.length; i++) {
            Field f = fs[i];
            Class type = f.getType();
            // 即使private属性也可以访问
            f.setAccessible(true);
            Object val = new Object();
            try {
                val = f.get(obj);
                if (val == null) {
                    val = "";
                }
                String sql = "";
                String key = f.getName();
                System.out.println("key:" + key + "type:" + type);
                if (val instanceof Integer) {
                    // 将Object对象强转为Integer对象
                    map.put(key, val);
                    sql = "n." + key + "=" + val;
                } else if (val instanceof String[]) {
                    // 创建一个String数组，强转为String数组
                    String[] arr = (String[]) val;
                    String v = "";
                    for (int j = 0; j < arr.length; j++) {
                        arr[j] = "'" + arr[j] + "'";
                    }
                    v = String.join(",", arr);
                    sql = "n." + key + "=[" + val + "]";
                } else if (val instanceof List) {
                    // 创建一个ArrayList，强转为ArrayList
                    List<String> arr = (ArrayList<String>) val;
                    List<String> aa = new ArrayList<String>();
                    String v = "";
                    for (String s : arr) {
                        s = "'" + s + "'";
                        aa.add(s);
                    }
                    v = String.join(",", aa);
                    sql = "n." + key + "=[" + v + "]";
                } else {
                    // 将Object对象强转为String对象
                    map.put(key, val);
                    sql = "n." + key + "='" + val + "'";
                }
                sqlList.add(sql);
            } catch (IllegalArgumentException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        String finasql = String.join(",", sqlList);
        // 如果字符串不包含“weight”，则添加weight属性
        if (!finasql.contains("weight")) {
            finasql = finasql + ",n.weight = ‘0.8’";
        }
        System.out.println("finasql-----:" + finasql);
        return finasql;
    }

    public <T> List<T> HashMapToObject(List<Map<String, Object>> maps, Class<T> type) {
        try {
            List<T> list = new ArrayList<T>();
            for (Map<String, Object> r : maps) {
                T t = type.newInstance();
                Iterator iter = r.entrySet().iterator();// name,age...等的迭代器
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();// 遍历hashmap转换为Iterator的entry
                    String key0 = entry.getKey().toString(); // 从iterator中获取key
                    Object value = entry.getValue(); // 从hashmap中获取value
                    if ("serialVersionUID".toLowerCase().equals(key0.toLowerCase())) continue;
                    Field field = type.getDeclaredField(key0);// 获取field对象
                    if (field != null) {
                        field.setAccessible(true);
                        if (field.getType() == int.class || field.getType() == Integer.class) {
                            if (value == null || StringUtil.isBlank(value.toString())) {
                                field.set(t, 0);// 赋值
                            } else {
                                field.set(t, Integer.parseInt(value.toString()));
                            }
                        } else if (field.getType() == long.class || field.getType() == Long.class) {
                            if (value == null || StringUtil.isBlank(value.toString())) {
                                field.set(t, 0);
                            } else {
                                field.set(t, Long.parseLong(value.toString()));
                            }
                        } else {
                            field.set(t, value);
                        }
                    }
                }
                list.add(t);
            }
            return list;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
