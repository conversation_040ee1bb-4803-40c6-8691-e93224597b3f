//package com.warmer.kgmaker;
//
////import apoc.export.xls.ExportXls;
//
////import apoc.export.csv.ExportCSV;
//
////import org.junit.jupiter.api.Test;
//
//import org.neo4j.dbms.api.DatabaseManagementService;
//import org.neo4j.dbms.api.DatabaseManagementServiceBuilder;
//import org.neo4j.graphdb.GraphDatabaseService;
//import org.neo4j.graphdb.Result;
//import org.neo4j.graphdb.Transaction;
//import org.neo4j.graphdb.traversal.Traverser;
//import org.neo4j.kernel.api.procedure.GlobalProcedures;
//import org.neo4j.kernel.impl.coreapi.TransactionImpl;
//import org.neo4j.kernel.internal.GraphDatabaseAPI;
//import org.testng.annotations.Test;
////import org.testng.annotations.Test;
//
//import java.io.File;
//import java.util.HashMap;
//import java.util.Map;
//
//import static org.neo4j.configuration.GraphDatabaseSettings.DEFAULT_DATABASE_NAME;
//
//public class TestSome {
//    public static String filename = "D:\\soft\\neo4j-community-4.4.7\\";
//
//    public static final File databaseDirectory = new File(filename);
//
//    //    public static Path path = Path.of( "D:\\soft\\neo4j-community-4.4.7\\");
//    public static DatabaseManagementService managementService = new DatabaseManagementServiceBuilder(databaseDirectory).build();
//
////    public static DatabaseManagementService managementService = new DatabaseManagementServiceBuilder(path).build();
//
//    public static GraphDatabaseService graphDb = managementService.database(DEFAULT_DATABASE_NAME);
//
//    @Test
//    public void testexport() {
////  public static void main(String[] args) {
//
////        GlobalProcedures procedures = ((GraphDatabaseAPI) graphDb).getDependencyResolver().resolveDependency(GlobalProcedures.class);
////        Set<Class<? extends BaseProc>> procedures = new Reflections("org.neo4j.graphalgo").getSubTypesOf(BaseProc.class);
////        procedures.addAll(new Reflections("org.neo4j.gds.embeddings").getSubTypesOf(BaseProc.class));
////
////
////        List<Class<?>> apocProcedures = asList(Coll.class, Maps.class, Json.class, Create.class, apoc.date.Date.class, FulltextIndex.class, apoc.lock.Lock.class, LoadJson.class,
////                Xml.class, PathExplorer.class, Meta.class, GraphRefactoring.class);
////        apocProcedures.forEach((proc) -> {
////            try {
////                procedures(proc);
////            } catch (KernelException e) {
////                throw new RuntimeException("Error registering "+proc,e);
////            }
////
////        ExportXls exportXls = new ExportXls();
////        exportXls.all(graphDb);
////
////		String cypherSql = "CALL apoc.export.csv.all(\"D:\\lida\\kgmaker\\csv\\lida-all.csv\", {}) ";
////		System.out.println(cypherSql);
////		Map<String, Object > parameters = new HashMap<String, Object>();
////		try (Transaction tx = graphDb.beginTx()) {
////			try (Result result = tx.execute(cypherSql, parameters)) {
////				// todo something
////			}
////			tx.commit();
////		}
////		System.out.println("done");
////
////    }
//
//
//        GlobalProcedures procedures = ((GraphDatabaseAPI) graphDb).getDependencyResolver().resolveDependency(GlobalProcedures.class);
////        List<Class<?>> apocProcedures = asList(ExportCSV.class);
////        apocProcedures.forEach((proc) -> {
////            try {
////                procedures.registerProcedure(proc,true);
////                procedures.registerFunction(proc,true);
////                procedures.registerAggregationFunction(proc,true);
////            } catch (KernelException e) {
////                throw new RuntimeException("Error registering "+proc,e);
////            }
////        });
//
//        String cypherSql = "CALL apoc.export.csv.all(\"D:\\lida\\kgmaker\\csv\\lida-all.csv\", {}) ";
//        System.out.println(cypherSql);
//        Map<String, Object> parameters = new HashMap<String, Object>();
//        try (Transaction tx = graphDb.beginTx()) {
//            try (Result result = tx.execute(cypherSql, parameters)) {
//                // todo something
//            }
//            tx.commit();
//        }
//        System.out.println("done");
//    }
//
//    @Test
//    public void testexpand() {
//        Transaction ss = null;
//        Traverser tt = ss.traversalDescription().traverse();
//        String exports0 = "precision highp float;\n" +
//                "attribute vec3 previous;\n" +
//                "attribute vec3 next;\n" +
//                "attribute float side;\n" +
//                "attribute float width;\n" +
//                "attribute float counters;\n" +
//                "attribute float lineWidth;\n" +
//                "\n" +
//                "attribute vec3 translation;\n" +
//                "attribute vec4 quaternion;\n" +
//                "attribute vec3 scale;\n" +
//                "attribute vec3 position;\n" +
//                "attribute float rotation;\n" +
//                "attribute float alpha;\n" +
//                "attribute float useDash;\n" +
//                "attribute float dashArray;\n" +
//                "attribute float dashOffset;\n" +
//                "attribute vec3 color;\n" +
//                "attribute vec3 idColor;\n" +
//                "\n" +
//                "uniform vec2 resolution;\n" +
//                "uniform vec2 uv;\n" +
//                "uniform float opacity;\n" +
//                "uniform float near;\n" +
//                "uniform float far;\n" +
//                "uniform float sizeAttenuation;\n" +
//                "\n" +
//                "uniform mat4 modelViewMatrix;\n" +
//                "uniform mat4 projectionMatrix;\n" +
//                "uniform mat3 normalMatrix;\n" +
//                "\n" +
//                "varying vec2 vUV;\n" +
//                "varying vec4 vColor;\n" +
//                "varying float vCounters;\n" +
//                "varying float vAlpha;\n" +
//                "varying float fogDepth;\n" +
//                "varying float vUseDash;\n" +
//                "varying float vDashArray;\n" +
//                " varying float v_pz;\n" +
//                " varying float vDashOffset;\n" +
//                "\n" +
//                "const float PI = 3.1415926535897932384626433832795;\n" +
//                "\n" +
//                "vec2 fix( vec4 i,float aspect ) {\n" +
//                "\n" +
//                "    vec2 res = i.xy / i.w;\n" +
//                "    res.x *= aspect;\n" +
//                "        vCounters = counters;\n" +
//                "    return res;\n" +
//                "\n" +
//                "}\n" +
//                "\n" +
//                "mat4 rotationMatrix(vec3 axis, float angle)\n" +
//                "{\n" +
//                "    axis = normalize(axis);\n" +
//                "    float s = sin(angle);\n" +
//                "    float c = cos(angle);\n" +
//                "    float oc = 1.0 - c;\n" +
//                "    \n" +
//                "    return mat4(oc * axis.x * axis.x + c,           oc * axis.x * axis.y - axis.z * s,  oc * axis.z * axis.x + axis.y * s,  0.0,\n" +
//                "                oc * axis.x * axis.y + axis.z * s,  oc * axis.y * axis.y + c,           oc * axis.y * axis.z - axis.x * s,  0.0,\n" +
//                "                oc * axis.z * axis.x - axis.y * s,  oc * axis.y * axis.z + axis.x * s,  oc * axis.z * axis.z + c,           0.0,\n" +
//                "                0.0,                                0.0,                                0.0,                                1.0);\n" +
//                "}\n" +
//                "\n" +
//                "vec3 transform( inout vec3 position, vec3 T, vec4 Q, vec3 S ) {\n" +
//                "    // applies the scale\n" +
//                "    position *= S;\n" +
//                "    // computes the quaternion where Q is a (vec4) quaternion\n" +
//                "    position += 2.0 * cross( Q.xyz, cross( Q.xyz, position ) + Q.w * position );\n" +
//                "    position += T;\n" +
//                "    return position;\n" +
//                "}\n" +
//                "\n" +
//                "\n" +
//                "void main() {\n" +
//                "\n" +
//                "    float aspect = resolution.x / resolution.y;\n" +
//                "    // float pixelWidthRatio = 1. / (resolution.x * projectionMatrix[0][0]);\n" +
//                "\n" +
//                "    vColor = vec4( color,opacity );\n" +
//                "    vUV = uv;\n" +
//                "    vAlpha = alpha;\n" +
//                "    vUseDash = useDash;\n" +
//                "    vDashArray = dashArray;\n" +
//                "    vDashOffset = dashOffset;\n" +
//                "\n" +
//                "    mat4 m = projectionMatrix * modelViewMatrix;\n" +
//                "    mat4 rotationM = rotationMatrix(vec3(0.0,1.0,0.0),rotation);\n" +
//                "\n" +
//                "    vec4 positionRef = rotationM * vec4(position,1.0);\n" +
//                "    vec4 prevRef = rotationM * vec4(previous,1.0);\n" +
//                "    vec4 nextRef = rotationM * vec4(next,1.0);\n" +
//                "\n" +
//                "    transform(prevRef.xyz, translation, quaternion, scale );\n" +
//                "    transform(positionRef.xyz, translation, quaternion, scale );\n" +
//                "    transform(nextRef.xyz, translation, quaternion, scale );\n" +
//                "\n" +
//                "    vec4 finalPosition = m * positionRef;\n" +
//                "    vec4 prevPos = m * prevRef;\n" +
//                "    vec4 nextPos = m * nextRef;\n" +
//                "\n" +
//                "    vec2 currentP = fix( finalPosition,aspect );\n" +
//                "    vec2 prevP = fix( prevPos,aspect );\n" +
//                "    vec2 nextP = fix( nextPos,aspect );\n" +
//                "\n" +
//                "    float pixelWidth = finalPosition.w * 1.0;\n" +
//                "    float w = 1.8 * pixelWidth * lineWidth * width;\n" +
//                "\n" +
//                "    if( sizeAttenuation == 1. ) {\n" +
//                "        w = 1.8 * lineWidth * width;\n" +
//                "    }\n" +
//                "   \n" +
//                "    //For Picker\n" +
//                "    #ifdef PICKER\n" +
//                "       w = w * 2.0;\n" +
//                "    #endif\n" +
//                "\n" +
//                "    if(vAlpha > 1.0){\n" +
//                "        w = w * vAlpha;\n" +
//                "    }\n" +
//                "\n" +
//                "    vec2 dir;\n" +
//                "    if( nextP == currentP ) dir = normalize( currentP - prevP );\n" +
//                "    else if( prevP == currentP ) dir = normalize( nextP - currentP );\n" +
//                "    else {\n" +
//                "        vec2 dir1 = normalize( currentP - prevP );\n" +
//                "        vec2 dir2 = normalize( nextP - currentP );\n" +
//                "        dir = normalize( dir1 + dir2 );\n" +
//                "\n" +
//                "        vec2 perp = vec2( -dir1.y,dir1.x );\n" +
//                "        vec2 miter = vec2( -dir.y,dir.x );\n" +
//                "        //w = clamp( w / dot( miter perp ) 0. 4. * lineWidth * width );\n" +
//                "    }\n" +
//                "\n" +
//                "    //vec2 normal = ( cross( vec3( dir 0. ) vec3( 0. 0. 1. ) ) ).xy;\n" +
//                "    vec2 normal = vec2( -dir.y,dir.x );\n" +
//                "    normal.x /= aspect;\n" +
//                "    normal *= .5 * w;\n" +
//                "\n" +
//                "    vec4 offset = vec4( normal * side, 0.0, 1.0 );\n" +
//                "    finalPosition.xy += offset.xy;\n" +
//                "    fogDepth = finalPosition.z;\n" +
//                "    gl_Position = finalPosition;\n" +
//                "  v_pz = position.y;\n" +
//                "}";
//        String ssd = "precision highp float;attribute vec3 previous;attribute vec3 next;attribute float side;attribute float width;attribute float counters;attribute float lineWidth;attribute vec3 translation;attribute vec4 quaternion;attribute vec3 scale;attribute vec3 position;attribute float rotation;attribute float alpha;attribute float useDash;attribute float dashArray;attribute float dashOffset;attribute vec3 color;attribute vec3 idColor;uniform vec2 resolution;uniform vec2 uv;uniform float opacity;uniform float near;uniform float far;uniform float sizeAttenuation;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;uniform mat3 normalMatrix;varying vec2 vUV;varying vec4 vColor;varying float vCounters;varying float vAlpha;varying float fogDepth;varying float vUseDash;varying float vDashArray; varying float v_pz; varying float vDashOffset;const float PI = 3.1415926535897932384626433832795;vec2 fix( vec4 i,float aspect ) {    vec2 res = i.xy / i.w;    res.x *= aspect;        vCounters = counters;    return res;}mat4 rotationMatrix(vec3 axis, float angle){    axis = normalize(axis);    float s = sin(angle);    float c = cos(angle);    float oc = 1.0 - c;        return mat4(oc * axis.x * axis.x + c,           oc * axis.x * axis.y - axis.z * s,  oc * axis.z * axis.x + axis.y * s,  0.0,                oc * axis.x * axis.y + axis.z * s,  oc * axis.y * axis.y + c,           oc * axis.y * axis.z - axis.x * s,  0.0,                oc * axis.z * axis.x - axis.y * s,  oc * axis.y * axis.z + axis.x * s,  oc * axis.z * axis.z + c,           0.0,                0.0,                                0.0,                                0.0,                                1.0);}vec3 transform( inout vec3 position, vec3 T, vec4 Q, vec3 S ) {    // applies the scale    position *= S;    // computes the quaternion where Q is a (vec4) quaternion    position += 2.0 * cross( Q.xyz, cross( Q.xyz, position ) + Q.w * position );    position += T;    return position;}void main() {    float aspect = resolution.x / resolution.y;    // float pixelWidthRatio = 1. / (resolution.x * projectionMatrix[0][0]);    vColor = vec4( color,opacity );    vUV = uv;    vAlpha = alpha;    vUseDash = useDash;    vDashArray = dashArray;    vDashOffset = dashOffset;    mat4 m = projectionMatrix * modelViewMatrix;    mat4 rotationM = rotationMatrix(vec3(0.0,1.0,0.0),rotation);    vec4 positionRef = rotationM * vec4(position,1.0);    vec4 prevRef = rotationM * vec4(previous,1.0);    vec4 nextRef = rotationM * vec4(next,1.0);    transform(prevRef.xyz, translation, quaternion, scale );    transform(positionRef.xyz, translation, quaternion, scale );    transform(nextRef.xyz, translation, quaternion, scale );    vec4 finalPosition = m * positionRef;    vec4 prevPos = m * prevRef;    vec4 nextPos = m * nextRef;    vec2 currentP = fix( finalPosition,aspect );    vec2 prevP = fix( prevPos,aspect );    vec2 nextP = fix( nextPos,aspect );    float pixelWidth = finalPosition.w * 1.0;    float w = 1.8 * pixelWidth * lineWidth * width;    if( sizeAttenuation == 1. ) {        w = 1.8 * lineWidth * width;    }       //For Picker    #ifdef PICKER       w = w * 2.0;    #endif    if(vAlpha > 1.0){        w = w * vAlpha;    }    vec2 dir;    if( nextP == currentP ) dir = normalize( currentP - prevP );    else if( prevP == currentP ) dir = normalize( nextP - currentP );    else {        vec2 dir1 = normalize( currentP - prevP );        vec2 dir2 = normalize( nextP - currentP );        dir = normalize( dir1 + dir2 );        vec2 perp = vec2( -dir1.y,dir1.x );        vec2 miter = vec2( -dir.y,dir.x );        //w = clamp( w / dot( miter perp ) 0. 4. * lineWidth * width );    }    //vec2 normal = ( cross( vec3( dir 0. ) vec3( 0. 0. 1. ) ) ).xy;    vec2 normal = vec2( -dir.y,dir.x );    normal.x /= aspect;    normal *= .5 * w;    vec4 offset = vec4( normal * side, 0.0, 1.0 );    finalPosition.xy += offset.xy;    fogDepth = finalPosition.z;    gl_Position = finalPosition;  v_pz = position.y;}";
//    }
//}
