'********** induction on events 

'<PERSON> is opening door_101
<John --> (/,open,_,door_101)>. :|: 

6

'<PERSON> is entering room_101
<John --> (/,enter,_,room_101)>. :|: 

20

'If <PERSON> enter room_101, he should open door_101 before
''outputMustContain('<<John --> (/,enter,_,room_101)> =\> (&/,<John --> (/,open,_,door_101)>,+6)>. :!6: %1.00;0.45%')

'new: variable introduction also in time:

'If someone enter room_101, he should open door_101 before
''outputMustContain('<<$1 --> (/,enter,_,room_101)> =\> (&/,<$1 --> (/,open,_,door_101)>,+6)>. :!6: %1.00;0.45%')

'adjusted +2 to +3 in both conditions

10
