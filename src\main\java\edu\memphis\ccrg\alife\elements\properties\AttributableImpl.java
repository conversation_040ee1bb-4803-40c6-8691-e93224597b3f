package edu.memphis.ccrg.alife.elements.properties;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AttributableImpl implements Attributable {
    private Map<String, Object> attributes = new ConcurrentHashMap();

    @Override // edu.memphis.ccrg.alife.elements.properties.Attributable
    public void setAttribute(String name, Object attribute) {
        this.attributes.put(name, attribute);
    }

    @Override // edu.memphis.ccrg.alife.elements.properties.Attributable
    public void clearAttributes() {
        this.attributes.clear();
    }

    @Override // edu.memphis.ccrg.alife.elements.properties.Attributable
    public Object getAttribute(String name) {
        return this.attributes.get(name);
    }

    @Override // edu.memphis.ccrg.alife.elements.properties.Attributable
    public void removeAttribute(String name) {
        this.attributes.remove(name);
    }

    @Override // edu.memphis.ccrg.alife.elements.properties.Attributable
    public boolean containsAttribute(String name) {
        return this.attributes.contains<PERSON>ey(name);
    }

    @Override // edu.memphis.ccrg.alife.elements.properties.Attributable
    public Object getAttribute(String name, Object defaultValue) {
        Object value = this.attributes.get(name);
        return value == null ? defaultValue : value;
    }

    @Override // edu.memphis.ccrg.alife.elements.properties.Attributable
    public Map<String, Object> getAttributes() {
        return Collections.unmodifiableMap(this.attributes);
    }
}
