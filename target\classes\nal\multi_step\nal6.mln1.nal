' MLN example by patrick

' Facts:
<(*,{<PERSON>},{<PERSON>}) --> Friends>. %1.00;0.99%
<(*,{<PERSON>},{<PERSON>}) --> Friends>. %1.00;0.99%
<(*,{<PERSON>},{<PERSON>}) --> Friends>. %1.00;0.99%
<(*,{<PERSON>},{<PERSON>}) --> Friends>. %1.00;0.99%
<(*,{<PERSON>},{<PERSON>}) --> Friends>. %1.00;0.99%
(--,<(*,{<PERSON>},{<PERSON>}) --> Friends>). %1.00;0.99%
<{<PERSON>} --> [Drinks]>. %1.00;0.99%
<{<PERSON>} --> [Smokes]>. %1.00;0.99%

' Rule0:
' p=0.8, Drinks(x) => Cancer(x)
<<$1 --> [Drinks]> ==> <$1 --> [Cancer]>>. %0.8;0.9%

' Rule1:
' p=0.8, Smokes(x) => Cancer(x)
<<$1 --> [Smokes]> ==> <$1 --> [Cancer]>>. %0.8;0.9%

' Rule2:
' p=0.6 Friends(x, y) => (Smokes(x) <=> Smokes(y))
<<(*,$1,$2) --> Friends> ==>     (||,    (&&,<$1 --> [Smokes]>,<$2 --> [Smokes]>),    (&&,(--,<$1 --> [Smokes]>),(--,<$2 --> [Smokes]>)))>. %0.6;0.9%

<?who --> [Cancer]>?
1000

''outputMustContain('<{Edward} --> [Cancer]>. %0.80;0.71%')
