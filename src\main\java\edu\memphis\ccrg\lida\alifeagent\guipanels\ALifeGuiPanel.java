/*
 * Created by JFormDesigner on Sat Dec 21 09:18:19 CST 2019
 */

package edu.memphis.ccrg.lida.alifeagent.guipanels;

import edu.memphis.ccrg.alife.gui.ALifePanel;
import edu.memphis.ccrg.alife.gui.ALifeWorldRenderer;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import edu.memphis.ccrg.alife.world.WorldLoader;
import edu.memphis.ccrg.lida.environment.Environment;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.gui.panels.GuiPanelImpl;

import javax.swing.*;
import java.awt.*;
import java.io.FileReader;
import java.io.IOException;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ALifeGuiPanel extends GuiPanelImpl {
    private static final Logger logger = Logger.getLogger(ALifeGuiPanel.class.getCanonicalName());
    private Environment environment;

    public ALifeGuiPanel() {
        initComponents();
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        aLifePanel1 = new ALifePanel();

        //======== this ========
        setPreferredSize(new Dimension(400, 300));

        //---- aLifePanel1 ----
        aLifePanel1.setPreferredSize(new Dimension(400, 300));

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addComponent(aLifePanel1, GroupLayout.DEFAULT_SIZE, 375, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addComponent(aLifePanel1, GroupLayout.DEFAULT_SIZE, 285, Short.MAX_VALUE)
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private ALifePanel aLifePanel1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    @Override
    public void initPanel(String[] param) {
        Properties iconsProperties = new Properties();
        environment = (Environment) agent.getSubmodule(ModuleName.Environment);
        if (environment != null) {
            ALifeWorld world = (ALifeWorld) environment.getModuleContent();
            try {
                iconsProperties.load(new FileReader((String) param[0]));
            } catch (IOException ex) {
                logger.log(Level.WARNING, "icon properties file can not be loaded.", ex);
            }
            if (world != null) {
                ALifeWorldRenderer renderer = WorldLoader.createRenderer(world, iconsProperties);
                if (renderer != null) {
                    int scalingFactor = 100;
                    if (param.length>1){
                        try{
                            scalingFactor = Integer.parseInt(param[1]);
                        }catch(NumberFormatException e){
                            logger.log(Level.WARNING, " Invalid Scaling Factor in param 1",0L);
                        }
                    }
                    aLifePanel1.init(renderer, world,scalingFactor);
                    return;
                }
            }
        }

        logger.log(Level.WARNING, "Unable to create the AlifeGuiPanel", 0L);

    }

    @Override
    public void refresh() {
        aLifePanel1.refresh();
    }
}
