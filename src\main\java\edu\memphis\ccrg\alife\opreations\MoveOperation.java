package edu.memphis.ccrg.alife.opreations;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.elements.Cell;
import edu.memphis.ccrg.alife.elements.ObjectContainer;
import edu.memphis.ccrg.alife.world.ALifeWorld;

public class MoveOperation implements WorldOperation {
    private static final char[] directions = {'N', 'S', 'W', 'E'};

    @Override // edu.memphis.ccrg.alife.opreations.WorldOperation
    public Object performOperation(ALifeWorld world, ALifeObject subject, ALifeObject[] object, Object... params) {
        return Boolean.valueOf(move(world, subject, directions[(int) (Math.random() * 4.0d)]));
    }

    /* access modifiers changed from: protected */
    public boolean move(ALifeWorld world, ALifeObject subject, char direction) {
        ObjectContainer currentCell = subject.getContainer();
        Cell destination = null;
        if (currentCell instanceof Cell) {
            Cell cell = (Cell) currentCell;
            switch (direction) {
                case 'E':
                    if (cell.getXCoordinate() < world.getWidth() - 1) {
                        destination = world.getCell(cell.getXCoordinate() + 1, cell.getYCoordinate());
                        break;
                    }
                    break;
                case 'N':
                    if (cell.getYCoordinate() > 0) {
                        destination = world.getCell(cell.getXCoordinate(), cell.getYCoordinate() - 1);
                        break;
                    }
                    break;
                case 'S':
                    if (cell.getYCoordinate() < world.getHeight() - 1) {
                        destination = world.getCell(cell.getXCoordinate(), cell.getYCoordinate() + 1);
                        break;
                    }
                    break;
                case 'W':
                    if (cell.getXCoordinate() > 0) {
                        destination = world.getCell(cell.getXCoordinate() - 1, cell.getYCoordinate());
                        break;
                    }
                    break;
            }
        }
        if (destination == null || destination.getContainerOccupancy() + subject.getSize() > destination.getCapacity()) {
            return false;
        }
        synchronized (subject) {
            currentCell.removeObject(subject);
            destination.addObject(subject);
        }
        return true;
    }
}
