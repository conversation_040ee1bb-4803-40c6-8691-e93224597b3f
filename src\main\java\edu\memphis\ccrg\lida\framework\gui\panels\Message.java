package edu.memphis.ccrg.lida.framework.gui.panels;

import java.awt.*;

public class Message {
    public Point getMessagePaintLeftTop() {
        return new Point();
    }

    public Integer getSenderHeadImageID() {
        return 0;
    }

    public boolean isSelfSender() {
        return false;
    }

    public int getMessagePaintHeight() {
        return 0;
    }

    public int getMessagePaintWidth() {
        return 0;
    }
}
