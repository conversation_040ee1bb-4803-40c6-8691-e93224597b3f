JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {seq {comp jToolBar1::::394:x, space :::p}, comp jSplitPane1::t::400:x}"
			"$verticalGroup": "par l {seq l {comp jToolBar1:::p:25:p, space :::p, comp jSplitPane1::::269:x}}"
		} ) {
			name: "this"
			"preferredSize": new java.awt.Dimension( 500, 320 )
			"minimumSize": new java.awt.Dimension( 480, 320 )
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "jToolBar1"
				"rollover": true
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "refreshButton"
					"text": "Refresh"
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "refreshButtonActionPerformed", true ) )
				} )
			} )
			add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
				name: "jSplitPane1"
				"dividerLocation": 150
				"orientation": 0
				"preferredSize": new java.awt.Dimension( 454, 310 )
				add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
					name: "winnersPane"
					add( new FormComponent( "javax.swing.JTable" ) {
						name: "winnersTable"
						auxiliary() {
							"JavaCodeGenerator.preInitCode": "${field}.setModel(new WinnerCoalitionsTableModel());"
						}
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "right"
				} )
				add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
					name: "coalitionsPane1"
					add( new FormComponent( "javax.swing.JTable" ) {
						name: "coalitionsTable"
						auxiliary() {
							"JavaCodeGenerator.preInitCode": "${field}.setModel(new CoalitionsTableModel());"
						}
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "left"
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
