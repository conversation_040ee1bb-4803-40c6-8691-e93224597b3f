/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.alifeagent.featuredetectors;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.ConcurrentHashSet;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLinkable;
import edu.memphis.ccrg.lida.pam.tasks.BasicDetectionAlgorithm;
import edu.memphis.ccrg.lida.pam.tasks.MultipleDetectionAlgorithm;
import edu.memphis.ccrg.lida.sensorymemory.SensoryMemory;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class ObjectDetector extends MultipleDetectionAlgorithm {
	private Map<String, Object> detectorParams = new HashMap<String, Object>();
	private Set<ALifeObject> originObjects = new ConcurrentHashSet<ALifeObject>();
	private Set<ALifeObject> nextCellObjects = new ConcurrentHashSet<ALifeObject>();

	/**
	 * Default constructor. Associated {@link Linkable},
	 * {@link PAMemory} and {@link SensoryMemory} must be set using setters.
	 */
	public ObjectDetector() {
	}

	@Override
	public void init() {
		super.init();// 并不是单纯视觉探测，object范围广

//		Integer position = getParam("position", 0);
//		detectorParams.put("position", position);
//		String obj = getParam("object", "");
//		detectorParams.put("object", obj);
	}

	@Override
	public void detectLinkables() {
//		String object = "";
//		sensorParam.put("mode","seethis");
//		originObjects.clear();
//		originObjects.addAll((Set<ALifeObject>) environment.getState(sensorParam));
//
//		if (originObjects.size() > 1){
//			for(ALifeObject o: originObjects){
//				if(!o.getName().equals("agent")){
////					excite("get", 0.62, "see");
//					// 重叠则表示拿到。各体感本应用各具身模态表示，这里抽象了一层，各词项符号意象一一对应=可分可组
//					// 需要区分和绑定激活，表示主体+模态+对象+时态+虚实，我现在真实看到食物。都用底层表示，如时间戳
//					// 符号层不用绑定，可发散激活
////					excite(o.getName(), 0.73, "get");
//
//					excite("(&&,food,get)", 0.73, "get");
//
////					AgentStarter.nar.addInput("(&&,food,get). :|:");
//				}
//			}
//		}
//		sensorParam.put("mode","seenext");
//		nextCellObjects.clear();
//
//		Set<ALifeObject> nextObjects = (Set<ALifeObject>) environment.getState(sensorParam);
//		if(nextObjects != null && nextObjects.size() > 0){
//			for(ALifeObject o: nextObjects){
////				excite("see", 0.62, "see");
////				excite(o.getName(), 0.73, "see");
//
//				if (o.getName().equals("边界")) {
//					excite("(&&,see,边界)", 0.73, "see");
////					AgentStarter.nar.addInput("(&&,see,边界). :|:");
//				}else {
//					excite("(&&," + o.getName() + ",see)", 0.73, "see");
////					AgentStarter.nar.addInput("(&&, " + o.getName() + ",see). :|:");
//				}
//			}
//		}else {
////			excite("see", 0.62, "see");
////			excite("empty", 0.73, "see");
//
//			excite("(&&,empty,see)", 0.73, "see");
////			AgentStarter.nar.addInput("(&&,empty,see). :|:");
//		}

		// todo 如果长时间没有新信息，触发好奇心动机，想要获取新刺激


	}

}
