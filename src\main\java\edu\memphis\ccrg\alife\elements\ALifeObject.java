package edu.memphis.ccrg.alife.elements;

import edu.memphis.ccrg.alife.elements.properties.Attributable;
import edu.memphis.ccrg.alife.opreations.Updateable;

public interface ALifeObject extends Attributable, Updateable {
    void decreaseHealth(double d);

    ObjectContainer getContainer();

    double getHealth();

    int getIconId();

    int getId();

    String getName();

    int getSize();

    void increaseHealth(double d);

    boolean isRemovable();

    void setContainer(ObjectContainer objectContainer);

    void setHealth(double d);

    void setIconId(int i);

    void setName(String str);

    void setRemovable(boolean z);

    void setSize(int i);
}
