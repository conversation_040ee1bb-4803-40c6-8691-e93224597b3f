<!--[#escape x as (x)!?html]-->

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>发现</title>
  <link rel="stylesheet" href="../res.layui.com/layui/src/css/layui.css">
  <link rel="stylesheet" href="../res.layui.com/layui/src/css/layui.demo.css">

    <script type='text/javascript' src='../res.layui.com/mods/webim.config.js'></script>
    <script type='text/javascript' src='../res.layui.com/mods/strophe-1.2.8.min.js'></script>
    <script type='text/javascript' src='../res.layui.com/mods/websdk.js'></script>
    <script src="../res.layui.com/layui/src/layui.js"></script>

  <script>
      layui.config({
          base: '../res.layui.com/mods/'
      }).extend({
          socket: 'socket'
      });
      layui.use([ 'layim','laypage','form','socket',], function(socket){
          var layim = layui.layim
              , layer = layui.layer
              ,laytpl = layui.laytpl
              ,form = layui.form
              ,$ = layui.jquery
              ,laypage = layui.laypage;
          var cache = parent.layui.layim.cache();
          var url = cache.base.getRecommend.url || {};  //获得URL参数。

          $(function(){getRecommend(); });
          function getRecommend(){
              $.get(url,{type:'get'},function(res){
                  var data = eval('(' + JSON.stringify(res) + ')');
                  var html = laytpl(LAY_tpl.value).render({
                      data: data.data,
                      legend:'推荐好友',
                      type:'friend'
                  });
                  $('#LAY_view').html(html);
              });
          }
          $('body').on('click', '.add', function () {//添加好友
              var othis = $(this), type = othis.data('type');
              parent.layui.im.addFriendGroup(othis,type);
              // type == 'friend' ? parent.layui.im.addFriend(othis,type) : parent.layui.im.addGroup(othis);
          });

          $('body').on('click', '.add1', function () {//加入群
              var othis = $(this), type = othis.data('type');

              var li = othis.parents('li') || othis.parent()
                  , infoid = li.data("infoid");
              var infoid0 = $("#infoNaotuId",parent.document).val();
              if(infoid != infoid0){
                  top.window.location.href="/info/" + infoid;
              }else {
                  layer.msg("喵，现在就在这里哦！");
              }
          });


          $('body').on('click', '.createGroup', function () {//创建群
              var othis = $(this);
//              parent.layui.im.createGroup(othis);

              var diag = new top.Dialog();

              diag.zIndex=100000000;
              diag.Drag=true;
              diag.Title ="新增";
              diag.URL = '/my/naotucreate';
              diag.Width = 600;
              diag.Height = 400;
//              diag.Modal = true;				//有无遮罩窗口
//              diag.ShowMaxButton = true;		//最大化按钮
//              diag.ShowMinButton = true;		//最小化按钮
              diag.CancelEvent = function(){ //关闭事件
                  diag.close();
              };
              diag.show();

          });
          $('body').on('click', '.back', function () {//返回推荐好友
              getRecommend();
              $("#LAY_page").css("display","none");
          });

          $("body").keydown(function(event){
              if(event.keyCode==13){
                  $(".find").click();
              }
          });
          $('body').on('click', '.find', function () {
              $("#LAY_page").css("display","block");
              var othis = $(this),input = othis.parents('.layui-col-space3').find('input').val();
              var addType = $('input:radio:checked').val();
              if (input) {
                  var url = cache.base.findFriendTotal.url || {};
                  $.get(url,{type:addType,value:input}, function(data){
                      var res = eval('(' + JSON.stringify(data) + ')');
                      if(res.code != 0){
                          return layer.msg(res.msg);
                      }
                      laypage.render({
                          elem: 'LAY_page'
                          ,count: res.data.count
                          ,limit: res.data.limit
                          ,prev: '<i class="layui-icon">&#58970;</i>'
                          ,next: '<i class="layui-icon">&#xe65b;</i>'
                          ,layout: ['prev', 'next']
                          ,curr: res.data.limit
                          ,jump: function(obj, first){
                              //obj包含了当前分页的所有参数，比如：
                              var url = cache.base.findFriend.url || {};
                              //首次不执行
                              if(first){
                                  var page = res.data.limit;
                              }else{
                                  var page = obj.curr;
                              }
                              $.get(url,{type:addType,value:input,page: obj.curr || 1},function(res){
                                  var data = eval('(' +  JSON.stringify(res) + ')');
                                  var html = laytpl(LAY_tpl.value).render({
                                      data: data.data,
                                      legend:'<a class="back"><i class="layui-icon">&#xe65c;</>返回</a> 查找结果',
                                      type:addType
                                  });
                                  $('#LAY_view').html(html);
                              });
                          }
                      });
                  });
              }
          });
      });

      function reflect0() {
          var othis = $(this);
//          var infoid = this.parent().data("infoid");
//          top.window.location.href="${ctx}/info/" + infoid;
      }

  </script>
</head>
<body>
<div class="layui-form">
  <div class="layui-container" style="padding:0">
    <div class="layui-row layui-col-space3">
      <div class="layui-col-xs6 mt15">
        <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="请输入爱猫号/昵称/手机号/邮箱" class="layui-input">
      </div>
      <div class="layui-col-xs1 mt15" >
        <button class="layui-btn btncolor find">查找</button>
      </div>

      <div class="layui-col-xs4 mt15">
        <input type="radio" name="add" value="friend" title="找人" checked="">
        <input type="radio" name="add" value="group" title="找群">
        <button class="layui-btn layui-btn-xs btncolor createGroup" >我要创作</button>
      </div>
    </div>
    <div id="LAY_view"></div>
    <textarea title="消息模版" id="LAY_tpl" style="display:none;">
			<fieldset class="layui-elem-field layui-field-title">
			  <legend>{{ d.legend}}</legend>
			</fieldset>
			<div class="layui-row ">
				{{# if(d.type == 'friend'){ }}
					{{#  layui.each(d.data, function(index, item){ }}
					<div class="layui-col-xs3 layui-find-list">
						<li layim-event="add" data-type="friend" data-index="0" data-uid="{{ item.id }}" data-name="{{item.username}}">
							<img class="img0" src="../../../../../../uploads/person/{{item.id}}.jpg " >
							<span>{{item.username}}({{item.id}})</span>
							<p>{{item.signature}}  {{#  if(item.signature == ''){ }}我很懒，懒得写签名{{#  } }} </p>
							<button class="layui-btn layui-btn-xs btncolor add" data-type="friend"><i class="layui-icon">&#xe654;</i>加好友</button>
						</li>
					</div>
					{{#  }); }}
				{{# }else{ }}
					{{#  layui.each(d.data, function(index, item){ }}
					<div class="layui-col-xs13 layui-find-list">

						<li layim-event="add1" data-type="group" data-approval="{{ item.approval }}" data-index="0" data-uid="{{ item.groupIdx }}" data-name="{{item.groupname}}" data-infoid="{{item.infoid}}">

                            <div class="ltfm_160x220 add1">
                                <a href="JavaScript:reflect0()" target="_blank">

                                <div class="li_txt_tagsbox">
                                <span>{{item.groupname}}({{item.id}})</span>
                                <p>{{item.des}}  {{#  if(item.des == ''){ }}无{{#  } }} </p>
                                </div>

                                <div class="yao"><span class="ver" style="display: none;"></span></div>
                                <div class="img"> <img src="/template/1/default/_files/duanpian.jpg"  alt="文艺猫经营你的灵感" /><!--有中封面-->
                                    <!--无中封面-->
                                </div>

                                </a>
                            </div>

							<!--<a href="JavaScript:reflect0()"><img src="../../../../../../uploads/person/{{item.groupIdx}}.jpg " ></a>-->
                            <!--<img class="add1" src="../../../../../../uploads/person/{{item.groupIdx}}.jpg " >-->

							<!--<span>{{item.groupname}}({{item.id}})</span>-->
							<!--<p>{{item.des}}  {{#  if(item.des == ''){ }}无{{#  } }} </p>-->

							<!--<button class="layui-btn layui-btn-xs btncolor add" data-type="group"><i class="layui-icon">&#xe654;</i>加入创作</button>-->

                        </li>

					</div>
					{{#  }); }}
				{{# } }}
			</div>
        </textarea>

    <div class="lay_page0" id="LAY_page" ></div>
  </div>
</div>
</body>
</html>

<!--[/#escape]-->
