====
    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
====

//MOTOR COMMAND RECEIVER FIRMWARE
//FOR ARDUINO NARS ROBOTS

//DEFINE THE PORTS CORRECT ACCORDING TO YOUR ROBOT WIRING:
void left_forward()
{
  digitalWrite(11, HIGH);
}
void right_forward()
{
  digitalWrite(10, HIGH);
}
void right_backward()
{
  right_forward();
  digitalWrite(12, HIGH);
}
void left_backward()
{
  left_forward();
  digitalWrite(13, HIGH);
}
//END

void setup()
{
  for(int i=0;i<20;i++)
  {
    pinMode(i,OUTPUT); //we only use it for control
  }
  all_off();
  Serial.begin(9600);
}

void all_off()
{
  for(int i=0;i<20;i++)
  {
    digitalWrite(i, LOW);
  }  
}

char cmd='n'; //nothing
int time=500;
void loop()
{
  if(Serial.available()>0)
  {
    cmd = Serial.read();
    if(cmd=='1') //left forward
    {
      left_forward();
    }
    if(cmd=='2') //left backward
    {
      left_backward();
    }
    if(cmd=='3') //right forward
    {
      right_forward();
    }
    if(cmd=='4') //right forward
    {
      right_backward();
    }
    if(cmd=='f') //forward
    {
      left_forward();
      right_forward();
    }
    if(cmd=='b') //backward
    {
      left_backward();
      right_backward();
    }
    if(cmd=='l') //left
    {
      left_backward();
      right_forward();
    }
    if(cmd=='r') //right
    {
      left_forward();
      right_backward();
    }
    if(cmd=='+') //increase command time
    {
      time+=100;
    }
    if(cmd=='-') //decrease command time
    {
      time-=100;
    }
    if(time<100)
    {
      time=100; //minimal motor time
    }
    delay(time); //execute cmd for certain but fixed time
  }
  all_off();
  cmd='n';
}