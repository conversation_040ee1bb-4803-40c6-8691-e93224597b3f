/*
 * Created by <PERSON><PERSON>orm<PERSON>esigner on Sat Dec 21 08:35:29 CST 2019
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.gui.utils.GuiUtils;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.activation.Learnable;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PamImpl0;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import java.awt.*;
import java.text.DecimalFormat;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class NodeStructureTable extends GuiPanelImpl {
    private static final Logger logger = Logger.getLogger(NodeStructureTable.class.getCanonicalName());
    private NodeStructure nodeStructure;
    private FrameworkModule module;
    private NodeStructureTableModel nodeStructureTableModel;

    /** Creates new form NodeStructureTable */
    public NodeStructureTable() {
        nodeStructureTableModel=new NodeStructureTableModel();
        initComponents();
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jToolBar1 = new JToolBar();
        refreshButton = new JButton();
        nodeStructurePane = new JScrollPane();
        nodeStructureTable = new JTable();

        //======== this ========
        setMinimumSize(new Dimension(196, 100));

        //======== jToolBar1 ========
        {
            jToolBar1.setRollover(true);

            //---- refreshButton ----
            refreshButton.setText("Refresh");
            refreshButton.setFocusable(false);
            refreshButton.setHorizontalTextPosition(SwingConstants.CENTER);
            refreshButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            refreshButton.addActionListener(e -> refreshButtonActionPerformed(e));
            jToolBar1.add(refreshButton);
        }

        //======== nodeStructurePane ========
        {

            //---- nodeStructureTable ----
            nodeStructureTable.setModel(nodeStructureTableModel);
            nodeStructureTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
            nodeStructureTable.setMaximumSize(new Dimension(1000, 1000));
            nodeStructureTable.setPreferredScrollableViewportSize(new Dimension(450, 310));
            nodeStructurePane.setViewportView(nodeStructureTable);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createParallelGroup()
                    .addComponent(nodeStructurePane, GroupLayout.Alignment.TRAILING, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE))
                .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, 400, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createParallelGroup()
                    .addGroup(GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                        .addGap(27, 27, 27)
                        .addComponent(nodeStructurePane, GroupLayout.DEFAULT_SIZE, 273, Short.MAX_VALUE)))
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
                    .addContainerGap(275, Short.MAX_VALUE))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar jToolBar1;
    private JButton refreshButton;
    private JScrollPane nodeStructurePane;
    private JTable nodeStructureTable;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_refreshButtonActionPerformed
        refresh();
    }//GEN-LAST:event_refreshButtonActionPerformed


    /**
     * Definition of this Panel should include a parameter for the ModuleName for the
     * module from which the NodeStructure will be obtained.
     * E.g., workspace.PerceptualBuffer or PerceptualAssociativeMemory
     * @see edu.memphis.ccrg.lida.framework.gui.panels.GuiPanelImpl#initPanel(java.lang.String[])
     */
    @Override
    public void initPanel(String[] param) {
        if (param == null || param.length == 0) {
            logger.log(Level.WARNING,
                    "Error initializing NodeStructureTable, not enough parameters.",
                    0L);
            return;
        }
        module = GuiUtils.parseFrameworkModule(param[0], agent);

        if (module != null) {
            display(module.getModuleContent());
        } else {
            logger.log(Level.WARNING,
                    "Unable to parse module {1}. Panel not initialized.",
                    new Object[]{0L, param[0]});
        }
    }

	@Override
	public void refresh() {
		display(module.getModuleContent());
	}
	@Override
	public void display(Object o) {
		if (o instanceof NodeStructure) {
			nodeStructure = (NodeStructure) o;
			((AbstractTableModel) nodeStructureTable.getModel())
					.fireTableStructureChanged();
		} else {
			logger.log(Level.WARNING,
							"Can only display NodeStructure, but received {1} from module {2}",
							new Object[] {TaskManager.getCurrentTick(),o,module});
		}
	}

	/*
	 * Implementation of abstract table model to adapt a NodeStructure to a
	 * Table. Columns are the attributes of the Nodes in the NodeStructure. Rows
	 * are the Nodes.
	 * 
	 * <AUTHOR> Snaider
	 * <AUTHOR>
	 */
	private class NodeStructureTableModel extends AbstractTableModel {
		// Support links as well
		private DecimalFormat df = new DecimalFormat("0.0000");
		private String[] columnNames = {"Node Label", "ID", 
				"Base-Level Activation", "Current Activation", 
				"Base-Level Incentive Salience", "Current Incentive Salience", 
									"Percept Threshold"};

		@Override
		public int getColumnCount() {
			return columnNames.length;
		}
		@Override
		public int getRowCount() {
			return nodeStructure.getNodeCount();
		}
		@Override
		public String getColumnName(int column) {
			if (column < columnNames.length) {
				return columnNames[column];
			}
			return "";
		}

		/**
		 * Depending on the columnIndex, the appropriate method is called to get
		 * an attribute of the Node.
		 * 
		 * @param rowIndex the index of the row being filled in
		 * @param columnIndex the index of the attribute being asked for
		 * @see javax.swing.table.TableModel#getValueAt(int, int)
		 */
		@Override
		public Object getValueAt(int rowIndex, int columnIndex) {
			Object value = "";
			Object[] nodes = nodeStructure.getNodes().toArray();
			if (rowIndex >= 0 && rowIndex < nodes.length &&
						columnIndex >= 0 &&	columnIndex < columnNames.length) {				
				Node node = (Node) nodes[rowIndex];
				switch (columnIndex) {
					case 0:
						value = node.getTNname();
						break;
					case 1:
						value = node.getNodeId();
						break;
					case 2:
						if (node instanceof Learnable) {
							value = df.format(((Learnable)node).getBaseLevelActivation());
						}
						break;
					case 3:
						value = df.format(node.getActivation());
						break;
					case 4:
						if (node instanceof Learnable) {
							value = df.format(((Learnable) node).getBaseLevelIncentiveSalience());
						}
						break;
					case 5:
						value = df.format(node.getIncentiveSalience());	
						break;
					case 6:
						value = df.format(PamImpl0.getPerceptThreshold());
						break;
					default:
						break;
				}
			}				
			return value;
		}//method
		
	}//table model
}
