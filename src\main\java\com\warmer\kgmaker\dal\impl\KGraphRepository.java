package com.warmer.kgmaker.dal.impl;

import com.alibaba.fastjson.JSON;
import com.warmer.kgmaker.dal.IKGraphRepository;
import com.warmer.kgmaker.entity.*;
import com.warmer.kgmaker.query.*;
import com.warmer.kgmaker.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class KGraphRepository implements IKGraphRepository {
	@Autowired
	private Neo4jUtil neo4jUtil;

	/**
	 * 领域标签分页
	 * 
	 * @param queryItem
	 * @return
	 */
	public GraphPageRecord<Map<String, Object>> getPageDomain(GraphQuery queryItem) {
		GraphPageRecord<Map<String, Object>> resultRecord = new GraphPageRecord<Map<String, Object>>();
		try {
			String totalCountquery = "MATCH (n) RETURN count(distinct labels(n)) as count";
			int totalCount = 0;
			totalCount = neo4jUtil.executeScalar(totalCountquery);
			if (totalCount > 0) {
				int skipCount = (queryItem.getPageIndex() - 1) * queryItem.getPageSize();
				int limitCount = queryItem.getPageSize();
				String domainSql = String.format(
						"START n=node(*)  RETURN distinct labels(n) as domain,count(n) as nodecount order by nodecount desc SKIP %s LIMIT %s",
						skipCount, limitCount);
				List<Map<String, Object>> pageList = neo4jUtil.excuteCypherSql(domainSql,"GetEntityList");
				resultRecord.setPageIndex(queryItem.getPageIndex());
				resultRecord.setPageSize(queryItem.getPageSize());
				resultRecord.setTotalCount(totalCount);
				resultRecord.setNodeList(pageList);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return resultRecord;
	}

	/**
	 * 删除Neo4j 标签
	 * 
	 * @param domain
	 */
	public void deleteKGdomain(String domain) {
		try {
			String rSql = String.format("MATCH (n:`%s`) -[r]-(m)  delete r", domain);
			neo4jUtil.excuteCypherSql(rSql,"excuteCypherSql");
			String deleteNodeSql = String.format("MATCH (n:`%s`) delete n", domain);
			neo4jUtil.excuteCypherSql(deleteNodeSql,"excuteCypherSql");

		} catch (Exception e) {
			e.printStackTrace();

		}
	}

	/**
	 * 查询图谱节点和关系
	 * 
	 * @param query
	 * @return node relationship
	 */
	public Map<String, Object> getdomaingraph(GraphQuery query) {
		Map<String, Object> nr = new HashMap<String, Object>();
		try {
			String domain = query.getDomain();
			// MATCH (n:`症状`) -[r]-(m:症状) where r.name='治疗' or r.name='危险因素' return n,m
			if (!StringUtil.isBlank(domain)) {
				String cqr = "";
				List<String> lis = new ArrayList<String>();
				if (query.getRelation() != null && query.getRelation().length > 0) {
					for (String r : query.getRelation()) {
						String it = String.format("r.name='%s'", r);
						lis.add(it);
					}
					cqr = String.join(" or ", lis);
				}
				String cqWhere = "";
				if (!StringUtil.isBlank(query.getNodename()) || !StringUtil.isBlank(cqr)) {

					if (!StringUtil.isBlank(query.getNodename())) {
						if (query.getMatchtype() == 1) {
							cqWhere = String.format("where n.name ='%s' ", query.getNodename());

						} else {
							cqWhere = String.format("where n.name contains('%s')", query.getNodename());
						}
					}
					String nodeOnly = cqWhere;
					if (!StringUtil.isBlank(cqr)) {
						if (StringUtil.isBlank(cqWhere)) {
							cqWhere = String.format(" where ( %s )", cqr);

						} else {
							cqWhere += String.format(" and ( %s )", cqr);
						}

					}
					// 下边的查询查不到单个没有关系的节点,考虑要不要左箭头
					String nodeSql = String.format("MATCH (n:`%s`) -[r]-(m) %s return * limit %s", domain, cqWhere,
							query.getPageSize());
					Map<String, Object> graphNode = neo4jUtil.excuteCypherSql(nodeSql,"GetGraphNodeAndShip").get(0);
					Object node = graphNode.get("nodes");
					// 没有关系显示则显示节点
					if (node != null) {
						nr.put("nodes", graphNode.get("nodes"));
						nr.put("relationships", graphNode.get("relationships"));
					} else {
						String nodecql = String.format("MATCH (n:`%s`)-[]-() %s RETURN distinct(n) limit %s", domain,
								nodeOnly, query.getPageSize());
						List<Map<String, Object>> nodeItem = neo4jUtil.excuteCypherSql(nodecql,"GetGraphNode");

						if (nodeItem == null || nodeItem.size() == 0) {
							nodecql = String.format("MATCH (n:`%s`) %s RETURN distinct(n) limit %s", domain,
									nodeOnly, query.getPageSize());
							nodeItem = neo4jUtil.excuteCypherSql(nodecql,"GetGraphNode");
						}

						nr.put("nodes", nodeItem);
						nr.put("relationships", new ArrayList<Map<String, Object>>());
					}
				} else {
					String nodeSql = String.format("MATCH (n:`%s`)-[]-() %s RETURN distinct(n) limit %s", domain, cqWhere,
							query.getPageSize());
					List<Map<String, Object>> graphNode = neo4jUtil.excuteCypherSql(nodeSql,"GetGraphNode");

					if (graphNode == null || graphNode.size() < 10) {
						nodeSql = String.format("MATCH (n:`%s`) %s RETURN distinct(n) limit %s", domain,
								cqWhere, query.getPageSize());
						graphNode = neo4jUtil.excuteCypherSql(nodeSql,"GetGraphNode");
//						nr.put("relationship", new ArrayList<Map<String, Object>>());
//					}else {
					}
					String domainSql = String.format("MATCH (n:`%s`)<-[r]->(m) %s RETURN distinct(r) limit %s", domain,
							cqWhere, query.getPageSize());// m是否加领域
					List<Map<String, Object>> graphRelation = neo4jUtil.excuteCypherSql(domainSql,"GetGraphRelationShip");
					nr.put("relationships", graphRelation);

					nr.put("nodes", graphNode);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return nr;
	}

	/**
	 * 获取节点列表
	 * 
	 * @param domain
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	public Map<String, Object> getdomainnodes(String domain, Integer pageIndex, Integer pageSize) {
		Map<String, Object> resultItem = new HashMap<String, Object>();
		List<Map<String, Object>> ents = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> concepts = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> props = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> methods = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> entitys = new ArrayList<Map<String, Object>>();
		try {
			int skipCount = (pageIndex - 1) * pageSize;
			int limitCount = pageSize;
			String domainSql = String.format("START n=node(*) MATCH (n:`%s`) RETURN n SKIP %s LIMIT %s", domain,
					skipCount, limitCount);
			if (!StringUtil.isBlank(domain)) {
				ents = neo4jUtil.excuteCypherSql(domainSql,"GetGraphNode");
				for (Map<String, Object> hashMap : ents) {
					Object et = hashMap.get("entitytype");
					if (et != null) {
						String typeStr = et.toString();
						if (StringUtil.isNotBlank(typeStr)) {
							int type = Integer.parseInt(et.toString());
							if (type == 0) {
								concepts.add(hashMap);
							} else if (type == 1) {
								entitys.add(hashMap);
							} else if (type == 2 || type == 3) {
								props.add(hashMap);// 属性和方法放在一起展示
							} else {
								// methods.add(hashMap);
							}
						}
					}
				}
				resultItem.put("concepts", concepts);
				resultItem.put("props", props);
				resultItem.put("methods", methods);
				resultItem.put("entitys", entitys);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return resultItem;
	}

	/**
	 * 获取某个领域指定节点拥有的上下级的节点数
	 * 
	 * @param domain
	 * @param nodeid
	 * @return long 数值
	 */
	public long getrelationnodecount(String domain, long nodeid) {
		long totalcount = 0;
		try {
			if (!StringUtil.isBlank(domain)) {
				String nodeSql = String.format("MATCH (n) <-[r]->(m)  where id(n)=%s return count(m)",
						nodeid);
				totalcount = neo4jUtil.GetGraphValue(nodeSql);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return totalcount;
	}

	/**
	 * 创建领域,默认创建一个新的节点,给节点附上默认属性
	 * 
	 * @param domain
	 */
	public void createdomain(String domain) {
		try {
			String cypherSql = String.format(
					"create (n:`%s`{entitytype:0,name:'000'}) return id(n)", domain);
			neo4jUtil.excuteCypherSql(cypherSql,"excuteCypherSql");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 获取/展开更多节点,找到和该节点有关系的节点
	 * 
	 * @param domain
	 * @param nodeid
	 * @return
	 */
	public Map<String, Object> getmorerelationnode(String domain, String nodeid) {
		Map<String, Object> result = new HashMap<String, Object>();
		try {
			String cypherSql = String.format("MATCH (n)-[r]-(m) where id(n)=%s  return * limit 100", nodeid);
			result = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphNodeAndShip").get(0);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 更新节点名称
	 * 
	 * @param domain
	 * @param nodeid
	 * @param nodename
	 * @return 修改后的节点
	 */
	public Map<String, Object> updatenodename(String domain, String nodeid, String nodename) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<Map<String, Object>> graphNodeList = new ArrayList<Map<String, Object>>();
		try {
			String cypherSql = String.format("MATCH (n) where id(n)=%s set n.name='%s' return n", nodeid,
					nodename);
			graphNodeList = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphNode");
			if (graphNodeList.size() > 0) {
				return graphNodeList.get(0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 创建单个节点
	 * 
	 * @param domain
	 * @param entity
	 * @return
	 */
	@Override
	public Map<String, Object> createnode(String domain, QAEntityItem entity) {
		Map<String, Object> rss = new HashMap<String, Object>();
		List<Map<String, Object>> graphNodeList = new ArrayList<Map<String, Object>>();
		try {
			if (entity.getUuid() != 0) {
				String sqlkeyval = neo4jUtil.getkeyvalCyphersql(entity);
				String cypherSql = String.format("match (n) where id(n)= %s set %s ,n:%s return n", entity.getUuid(), sqlkeyval, entity.getLabel0());
				graphNodeList = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphNode");
			} else {
//				entity.setColor("#ff4555");// 默认颜色
//				entity.setR(30);// 默认半径
				String propertiesString = neo4jUtil.getFilterPropertiesJson(JSON.toJSONString(entity));
				String cypherSql = String.format("create (n:`%s` %s) return n", entity.getLabel0(), propertiesString);

				if("00".equals(entity.getName())){
					cypherSql = "create (n:`" + entity.getLabel0() + "`{name:\'00\'}) return n";
				}

				graphNodeList = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphNode");
//				graphNodeList = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphNodeAndShip");
			}
			if (graphNodeList.size() > 0) {
				rss = graphNodeList.get(0);
				return rss;
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return rss;
	}

	/**
	 * 批量创建节点和关系
	 * 
	 * @param domain
	 *            领域，标签，简化只用当前标签
	 * @param sourcename
	 *            源节点
	 * @param relation
	 *            关系，空则不加关系，加了还要改，每条改还不如逐条加，多条同类关系可加
	 * @param targetnames
	 *            目标节点数组
	 * @return
	 */
	public Map<String, Object> batchcreatenode(String domain, String sourcename, String relation, String[] targetnames) {

		String cypherSqlFmt = "create (n:`%s` {name:'%s',color:'#ff4555',r:30}) return n";
		String cypherSql = String.format(cypherSqlFmt, domain, sourcename);// 概念实体
		Map<String, Object> rss = new HashMap<String, Object>();

		rss = createRe(true, sourcename, relation, targetnames, cypherSqlFmt, domain, cypherSql);
		return rss;
	}

	/**
	 * 批量创建下级节点
	 * 
	 * @param domain
	 *            领域
	 * @param sourceuuid
	 *            源节点id
	 * @param entitytype
	 *            节点类型
	 * @param targetnames
	 *            目标节点名称数组
	 * @param relation
	 *            关系，必须有关系标签和关系名
	 * @return
	 */
	public Map<String, Object> batchcreatechildnode(String domain, String sourceuuid, Integer entitytype, String[] targetnames, String relation) {

		String cypherSqlFmt = "create (n:`%s`{name:'%s',color:'#ff4555',r:30}) return n";
		String cypherSql = String.format("match (n) where id(n)=%s return n", sourceuuid);
		Map<String, Object> rss = new HashMap<String, Object>();
		rss = createRe(false, sourceuuid, relation, targetnames, cypherSqlFmt, domain, cypherSql);

		return rss;
	}

	private Map<String, Object> createRe(boolean isname, String idorname, String relation, String[] targetnames, String cypherSqlFmt, String domain, String cypherSql) {
		Map<String, Object> rss = new HashMap<String, Object>();
		try {
			List<Map<String, Object>> nodes = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> ships = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> sourcenodeList = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphNode");
			if (sourcenodeList.size() > 0) {
				nodes.addAll(sourcenodeList);
				List<Map<String, Object>> rshipList = new ArrayList<>();

				if(isname){
					idorname = (String) sourcenodeList.get(0).get("id");
				}

				for (String tn : targetnames) {
					String targetnodeSql = String.format(cypherSqlFmt, domain, tn);
					List<Map<String, Object>> targetNodeList = neo4jUtil.excuteCypherSql(targetnodeSql, "GetGraphNode");
					if (targetNodeList.size() > 0) {
						Map<String, Object> targetNode = targetNodeList.get(0);
						nodes.add(targetNode);
						String targetuuid = String.valueOf(targetNode.get("id"));
						String[] re = relation.split(":");
						String relate;
						// 创建关系，必须有关系标签和关系名
						if (!"".equals(relation) && relation != null) {
							if (re.length == 2) {
								relate = re[1];
							} else {
								relate = "rname";
							}
							String rSql = String.format(
									"match(n),(m) where id(n)=%s and id(m)=%s create (n)-[r:%s {name:'%s'}]->(m) return r", idorname, targetuuid, re[0], relate);
							rshipList = neo4jUtil.excuteCypherSql(rSql, "GetGraphRelationShip");
						}

						ships.addAll(rshipList);
					}
				}
			}
			rss.put("nodes", nodes);
			rss.put("ships", ships);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rss;
	}

	/**
	 * 批量创建同级节点
	 * 
	 * @param domain
	 *            领域
	 * @param entitytype
	 *            节点类型
	 * @param sourcenames
	 *            节点名称
	 * @return
	 */
	public List<Map<String, Object>> batchcreatesamenode(String domain, Integer entitytype, String[] sourcenames) {
		List<Map<String, Object>> rss = new ArrayList<Map<String, Object>>();
		try {
			String cypherSqlFmt = "create (n:`%s`{name:'%s',color:'#ff4555',r:30}) return n";
			for (String tn : sourcenames) {
				String sourcenodeSql = String.format(cypherSqlFmt, domain, tn, entitytype);
				List<Map<String, Object>> targetNodeList = neo4jUtil.excuteCypherSql(sourcenodeSql,"GetGraphNode");
				rss.addAll(targetNodeList);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return rss;
	}

	/**
	 * 添加关系
	 * 
	 * @param domain
	 *            领域
	 * @param sourceid
	 *            源节点id
	 * @param targetid
	 *            目标节点id
	 * @return
	 */
	public Map<String, Object> createlink(String domain, long sourceid, long targetid, String type) {
		Map<String, Object> rss = new HashMap<String, Object>();
		try {
			String cypherSql = String.format("MATCH (n),(m) WHERE id(n)=%s AND id(m) = %s "
					+ "CREATE (n)-[r:%s{name:'rname',incentive:0.95,weight:0.8}]->(m)" + " RETURN r", sourceid, targetid, type);
			List<Map<String, Object>> cypherResult = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphRelationShip");
			if (cypherResult.size() > 0) {
				rss = cypherResult.get(0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return rss;
	}

	public Map<String, Object> changelink(String type, long id) {
		Map<String, Object> rss = new HashMap<String, Object>();
		String cypherSql = "";
		try {
				cypherSql = String.format("MATCH (n) -[r]->(m) where id(r)=%s\n" +
						"CREATE (n)-[r2:%s]->(m) SET r2 = r WITH r,r2 DELETE r with r2 return r2\n", id, type);
			List<Map<String, Object>> cypherResult = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphRelationShip");
			if (cypherResult.size() > 0) {
				rss = cypherResult.get(0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rss;
	}

	/**
	 * 更新关系
	 * @param id
	 *            关系id
	 * @return
	 */
	@Override
	public Map<String, Object> updatelink(String id, Map attrs) {
		Map<String, Object> rss = new HashMap<String, Object>();
		String cypherSql = "";
		StringBuffer sb = new StringBuffer();
		String[] attr0;
		int num = 0;
		try {
			for (Object attr: attrs.keySet()){
				attr0 = (String[]) attrs.get(attr);
				if (attr.toString().equals("id")) {
					continue;
				}else {
					num ++;
				}
				sb.append("r." + attr.toString() + "='" + attr0[0] + "' ");
				if (attrs.keySet().size() - 1 > num && num > 0) {
					sb.append(",");
				}
			}

			cypherSql = String.format("MATCH (n) -[r]->(m) where id(r)=%s set %s return r", id, sb);

			List<Map<String, Object>> cypherResult = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphRelationShip");
			if (cypherResult.size() > 0) {
				rss = cypherResult.get(0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rss;
	}

	/**
	 * 更新关系
	 * @param id
	 *            关系id
	 * @return
	 */
	@Override
	public Map<String, Object> updatenode(String id, Map attrs) {
		Map<String, Object> rss = new HashMap<String, Object>();
		String cypherSql = "";
		StringBuffer sb = new StringBuffer();
		String[] attr0;
		int num = 0;
		try {
			List<Map<String, Object>> cypherResult = new ArrayList<>();
			for (Object attr: attrs.keySet()){
				attr0 = (String[]) attrs.get(attr);
				if (attr.toString().equals("id")) {
					continue;
				}else {
					num ++;
					sb.append("n." + attr.toString() + "='" + attr0[0] + "' ");
				}
				if (attrs.keySet().size() - 1 > num && num > 0) {
					sb.append(",");
				}
			}
            if(!sb.equals("")){
				cypherSql = String.format("MATCH (n) where id(n)=%s set %s return n", id, sb);

				cypherResult = neo4jUtil.excuteCypherSql(cypherSql,"GetGraphNode");
			}

			if (cypherResult.size() > 0) {
				rss = cypherResult.get(0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rss;
	}

	/**
	 * 删除节点(先删除关系再删除节点)
	 * 
	 * @param domain
	 * @param nodeid
	 */
	public void deletenode(String domain, long nodeid) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		try {
//			String nSql = String.format("MATCH (n:`%s`)  where id(n) = %s return n", domain, nodeid);
//			result = neo4jUtil.excuteCypherSql(nSql,"GetGraphNode");

//			String rSql = String.format("MATCH (n:`%s`) <-[r]->(m) where id(n) = %s return r", domain, nodeid);
//			neo4jUtil.excuteCypherSql(rSql,"GetGraphRelationShip");

			String deleteRelationSql = String.format("MATCH (n) <-[r]->(m) where id(n) = %s delete r", nodeid);
			neo4jUtil.excuteCypherSql(deleteRelationSql,"excuteCypherSql");
			String deleteNodeSql = String.format("MATCH (n) where id(n) = %s delete n",nodeid);
			neo4jUtil.excuteCypherSql(deleteNodeSql,"excuteCypherSql");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 删除关系
	 * 
	 * @param domain
	 * @param shipid
	 */
	public void deletelink(String domain, long shipid) {
		try {
			String cypherSql = String.format("MATCH (n) -[r]->(m) where id(r)=%s delete r", shipid);
			neo4jUtil.excuteCypherSql(cypherSql,"excuteCypherSql");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 段落识别出的三元组生成图谱
	 * 
	 * @param domain
	 * @param entitytype
	 * @param operatetype
	 * @param sourceid
	 * @param rss
	 *            关系三元组
	 *            [[startname;ship;endname],[startname1;ship1;endname1],[startname2;ship2;endname2]]
	 * @return node relationship
	 */
	public Map<String, Object> createGraphByText(String domain, Integer entitytype, Integer operatetype,
			Integer sourceid, String[] rss) {
		Map<String, Object> rsList = new HashMap<String, Object>();
		try {
			List<Object> nodeIds = new ArrayList<Object>();
			List<Map<String, Object>> nodeList = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> shipList = new ArrayList<Map<String, Object>>();

			if (rss != null && rss.length > 0) {
				for (String item : rss) {
					String[] ns = item.split(";");
					String nodestart = ns[0];
					String ship = ns[1];
					String nodeend = ns[2];
					String nodestartSql = String.format("MERGE (n:`%s`{name:'%s',entitytype:'%s'})  return n", domain,
							nodestart, entitytype);
					String nodeendSql = String.format("MERGE (n:`%s`{name:'%s',entitytype:'%s'})  return n", domain,
							nodeend, entitytype);
					// 创建初始节点
					List<Map<String, Object>> startNode = neo4jUtil.excuteCypherSql(nodestartSql,"GetGraphNode");
					// 创建结束节点
					List<Map<String, Object>> endNode = neo4jUtil.excuteCypherSql(nodeendSql,"GetGraphNode");
					Object startId = startNode.get(0).get("id");
					if (!nodeIds.contains(startId)) {
						nodeIds.add(startId);
						nodeList.addAll(startNode);
					}
					Object endId = endNode.get(0).get("id");
					if (!nodeIds.contains(endId)) {
						nodeIds.add(endId);
						nodeList.addAll(endNode);
					}
					if (sourceid != null && sourceid > 0 && operatetype == 2) {// 添加下级
						String shipSql = String.format(
								"MATCH (n),(m) WHERE id(n)=%s AND id(m) = %s "
										+ "CREATE (n)-[r:RE{name:'%s'}]->(m)" + "RETURN r",
								sourceid, startId, "");
						List<Map<String, Object>> shipResult = neo4jUtil.excuteCypherSql(shipSql,"GetGraphRelationShip");
						shipList.add(shipResult.get(0));
					}
					String shipSql = String.format("MATCH (n),(m) WHERE id(n)=%s AND id(m) = %s "
							+ "CREATE (n)-[r:RE{name:'%s'}]->(m)" + "RETURN r", startId, endId, ship);
					List<Map<String, Object>> shipResult = neo4jUtil.excuteCypherSql(shipSql,"GetGraphRelationShip");
					shipList.addAll(shipResult);

				}
				rsList.put("node", nodeList);
				rsList.put("relationship", shipList);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return rsList;
	}

	public void batchcreateGraph(String domain, List<Map<String, Object>> params) {
		try {
			if (params != null && params.size() > 0) {
				String nodeStr = neo4jUtil.getFilterPropertiesJson(JSON.toJSONString(params));
				String nodeCypher = String
						.format("UNWIND %s as row " + " MERGE (n:`%s` {name:row.SourceNode,source:row.Source})"
								+ " MERGE (m:`%s` {name:row.TargetNode,source:row.Source})", nodeStr, domain, domain);
				neo4jUtil.excuteCypherSql(nodeCypher,"excuteCypherSql");
				String relationShipCypher = String.format("UNWIND %s as row " + " MATCH (n:`%s` {name:row.SourceNode})"
						+ " MATCH (m:`%s` {name:row.TargetNode})" + " MERGE (n)-[:RE{name:row.RelationShip}]->(m)",
						nodeStr, domain, domain);
				neo4jUtil.excuteCypherSql(relationShipCypher,"excuteCypherSql");
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 批量导入csv
	 * 
	 * @param domain
	 * @param csvUrl
	 * @param status
	 */
	public void batchInsertByCSV(String domain, String csvUrl, int status) {
		String loadNodeCypher1 = null;
		String loadNodeCypher2 = null;
		String addIndexCypher = null;
		addIndexCypher = " CREATE INDEX ON :" + domain + "(name);";
		loadNodeCypher1 = " USING PERIODIC COMMIT 500 LOAD CSV FROM '" + csvUrl + "' AS line " + " MERGE (:`" + domain
				+ "` {name:line[0]});";
		loadNodeCypher2 = " USING PERIODIC COMMIT 500 LOAD CSV FROM '" + csvUrl + "' AS line " + " MERGE (:`" + domain
				+ "` {name:line[1]});";
		// 拼接生产关系导入cypher
		String loadRelCypher = null;
		String type = "RE";
		loadRelCypher = " USING PERIODIC COMMIT 500 LOAD CSV FROM  '" + csvUrl + "' AS line " + " MATCH (m:`" + domain
				+ "`),(n:`" + domain + "`) WHERE m.name=line[0] AND n.name=line[1] " + " MERGE (m)-[r:" + type + "]->(n) "
				+ "	SET r.name=line[2];";
		neo4jUtil.excuteCypherSql(addIndexCypher,"excuteCypherSql");
		neo4jUtil.excuteCypherSql(loadNodeCypher1,"excuteCypherSql");
		neo4jUtil.excuteCypherSql(loadNodeCypher2,"excuteCypherSql");
		neo4jUtil.excuteCypherSql(loadRelCypher,"excuteCypherSql");
	}

	public void updateNodeFileStatus(String domain, long nodeId, int status) {
		try {
			String nodeCypher = String.format("match (n) where id(n)=%s set n.hasfile=%s ", nodeId, status);
			neo4jUtil.excuteCypherSql(nodeCypher,"excuteCypherSql");

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	@Override
	public void updateCorrdOfNode(String domain, String id, Double fx, Double fy) {
		String cypher = null;
		if (fx == null && fy==null) {
			cypher = " MATCH (n) where ID(n)=" + id
					+ " set n.fx=null, n.fy=null; ";
		} else {
			if ("0.0".equals(fx.toString()) && "0.0".equals(fy.toString())) {
				cypher = " MATCH (n) where ID(n)=" + id
						+ " set n.fx=null, n.fy=null; ";
			} else {
				cypher = " MATCH (n) where ID(n)=" + id
						+ " set n.fx=" + fx + ", n.fy=" + fy + ";";
			}
		}
		neo4jUtil.excuteCypherSql(cypher,"excuteCypherSql");
	}
}
