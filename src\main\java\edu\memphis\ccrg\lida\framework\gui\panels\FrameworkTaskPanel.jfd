JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {seq {comp jToolBar1::::390:x, space :::p}, comp threadPane::l::400:x}"
			"$verticalGroup": "par l {seq l {comp jToolBar1:::p:25:p, space :::p, comp threadPane::::215:x}}"
		} ) {
			name: "this"
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "jToolBar1"
				"rollover": true
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "refreshButton"
					"text": "Refresh"
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "refreshButtonActionPerformed", true ) )
				} )
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "threadPane"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "tasksTable"
					"autoResizeMode": 0
					"maximumSize": new java.awt.Dimension( 1000, 1000 )
					auxiliary() {
						"JavaCodeGenerator.preInitCode": "${field}.setModel(taskTableModel);"
					}
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
		} )
	}
}
