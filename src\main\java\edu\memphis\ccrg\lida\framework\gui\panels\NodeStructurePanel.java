/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.gui.utils.GuiLink;
import edu.memphis.ccrg.lida.framework.gui.utils.GuiUtils;
import edu.memphis.ccrg.lida.framework.gui.utils.NodeIcon;
import edu.memphis.ccrg.lida.framework.gui.utils.NodeStructureGuiAdapter;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.framework.shared.activation.Activatible;
import edu.memphis.ccrg.lida.framework.shared.activation.Learnable;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.uci.ics.jung.algorithms.layout.FRLayout;
import edu.uci.ics.jung.algorithms.layout.Layout;
import edu.uci.ics.jung.algorithms.layout.util.Relaxer;
import edu.uci.ics.jung.visualization.VisualizationViewer;
import edu.uci.ics.jung.visualization.control.DefaultModalGraphMouse;
import edu.uci.ics.jung.visualization.control.ModalGraphMouse;

import javax.swing.*;
import java.awt.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * A {@link GuiPanel} which creates a graphical view of a {@link NodeStructure}.
 * 
 * The {@link NodeStructure} is one from a paticular {@link FrameworkModule} which is defined as a parameter in the
 * guiPanels.properties file (or whatever file name is given by the lida.gui.panels property).
 * 
 * {@link FrameworkModule#getModuleContent(Object...)} must return {@link NodeStructure}.
 * 
 * <AUTHOR> Snaider
 * <AUTHOR> Lohbihler
 */
public class NodeStructurePanel extends GuiPanelImpl {
    private static final Logger logger = Logger.getLogger(NodeStructurePanel.class.getCanonicalName());
    private final NodeStructureGuiAdapter guiGraph = new NodeStructureGuiAdapter(new NodeStructureImpl());
    private VisualizationViewer<Linkable, GuiLink> vizViewer;
    private FrameworkModule module;

    /** Creates new form NodeStructurePanel */
    public NodeStructurePanel() {
        initComponents();
    }


    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jToolBar1 = new JToolBar();
        refreshButton = new JButton();
        relaxButton = new JButton();
        jScrollPane1 = new JScrollPane();
        jPanel1 = new JPanel();

        //======== this ========
        setMinimumSize(new Dimension(200, 150));

        //======== jToolBar1 ========
        {
            jToolBar1.setRollover(true);

            //---- refreshButton ----
            refreshButton.setText("Refresh");
            refreshButton.setFocusable(false);
            refreshButton.setHorizontalTextPosition(SwingConstants.CENTER);
            refreshButton.setMaximumSize(new Dimension(50, 25));
            refreshButton.setMinimumSize(new Dimension(50, 25));
            refreshButton.setPreferredSize(new Dimension(50, 25));
            refreshButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            refreshButton.addActionListener(e -> refreshButtonActionPerformed(e));
            jToolBar1.add(refreshButton);

            //---- relaxButton ----
            relaxButton.setText("Relax");
            relaxButton.setToolTipText("Relaxes the graph");
            relaxButton.setFocusable(false);
            relaxButton.setHorizontalTextPosition(SwingConstants.CENTER);
            relaxButton.setMaximumSize(new Dimension(50, 25));
            relaxButton.setMinimumSize(new Dimension(50, 25));
            relaxButton.setPreferredSize(new Dimension(50, 25));
            relaxButton.setVerticalTextPosition(SwingConstants.BOTTOM);
            relaxButton.addActionListener(e -> relaxButtonActionPerformed(e));
            jToolBar1.add(relaxButton);
        }

        //======== jScrollPane1 ========
        {

            //======== jPanel1 ========
            {

                GroupLayout jPanel1Layout = new GroupLayout(jPanel1);
                jPanel1.setLayout(jPanel1Layout);
                jPanel1Layout.setHorizontalGroup(
                    jPanel1Layout.createParallelGroup()
                        .addGap(0, 398, Short.MAX_VALUE)
                );
                jPanel1Layout.setVerticalGroup(
                    jPanel1Layout.createParallelGroup()
                        .addGap(0, 267, Short.MAX_VALUE)
                );
            }
            jScrollPane1.setViewportView(jPanel1);
        }

        GroupLayout layout = new GroupLayout(this);
        setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup()
                .addComponent(jToolBar1, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(jScrollPane1)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup()
                .addGroup(layout.createSequentialGroup()
                    .addComponent(jToolBar1, GroupLayout.PREFERRED_SIZE, 25, GroupLayout.PREFERRED_SIZE)
                    .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                    .addComponent(jScrollPane1))
        );
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JToolBar jToolBar1;
    private JButton refreshButton;
    private JButton relaxButton;
    private JScrollPane jScrollPane1;
    private JPanel jPanel1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    private void relaxButtonActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_relaxButtonActionPerformed
        relax();
    }//GEN-LAST:event_relaxButtonActionPerformed

    private void relax() {
        Relaxer relaxer = vizViewer.getModel().getRelaxer();
        if (relaxer != null) {
            relaxer.stop();
            relaxer.prerelax();
            relaxer.relax();
        }
    }

    private void refreshButtonActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_refreshButtonActionPerformed
        refresh();
    }// GEN-LAST:event_refreshButtonActionPerformed


    private void draw() {
        // The Layout<V, E> is parameterized by the vertex and edge types
        Layout<Linkable, GuiLink> layout = new FRLayout<Linkable, GuiLink>(guiGraph);
        // Sets initial size of the layout
        layout.setSize(new Dimension(300, 300));
        // The BasicVisualizationServer<V,E> is parameterized by the edge types
        vizViewer = new VisualizationViewer<Linkable, GuiLink>(layout);
        // Sets viewing area size
        vizViewer.setPreferredSize(new Dimension(350, 350));
        // Show vertex and edge labels
        vizViewer.getRenderContext().setVertexLabelTransformer(
                linkable -> {
                    if (linkable instanceof Link) {
                        return ((Link) linkable).getCategory().getName();
                    }
                    return linkable.getTNname();
                });

        // vv.getRenderContext().setEdgeLabelTransformer(new
        // ToStringLabeller<GuiLink>());
        // Create a graph mouse and add it to the visualization component
        DefaultModalGraphMouse<Linkable, GuiLink> gm2 = new DefaultModalGraphMouse<Linkable, GuiLink>();
        gm2.setMode(ModalGraphMouse.Mode.TRANSFORMING);
        vizViewer.getRenderContext().setVertexIconTransformer(v -> {
                    if (v instanceof Node) {
                        /*
                         * Implements the Icon interface to draw an Icon with
                         * background color
                         */
                        return NodeIcon.NODE_ICON;
                    } else {
                        return NodeIcon.LINK_ICON;
                    }
                });
        vizViewer.setVertexToolTipTransformer(l -> {
            //TODO use the toString of the linkable
            String tip = null;
            if (l instanceof Activatible) {
                if (l instanceof Learnable) {
                    Learnable pn = (Learnable) l;
                    tip = String.format("<html><b>%s</b><br/>Activation: %06.4f <br /> Base-Level Activation: %06.4f " +
                            "<br />Incentive Salience:  %06.4f <br />Base-Level Incentive Salience:  %06.4f <br />Threshold:  %06.4f </html>",
                            pn,pn.getActivation(), pn.getBaseLevelActivation(),
                            pn.getIncentiveSalience(),pn.getBaseLevelIncentiveSalience(), PamImpl0.getPerceptThreshold());
                }else{
                    Activatible n = l;
                    tip = String.format("<html><b>%s</b> <br/>Activation: %06.4f <br/>Incentive Salience: %06.4f</html>",
                                        n,n.getActivation(),n.getIncentiveSalience());
                }
            }
            return tip;
        });
        vizViewer.setEdgeToolTipTransformer(l -> {
            String tip = null; //TODO Link and pam link
            GuiLink gl = l;
            Link n = gl.getLink();
            tip = String.format("<html><b>%s</b> <br/>Activation: %06.4f <br/>Incentive Salience: %06.4f</html>",
                                n,n.getActivation(),n.getIncentiveSalience());
            return tip;
        });

        vizViewer.setGraphMouse(gm2);

        jScrollPane1.setViewportView(vizViewer);
        vizViewer.fireStateChanged();
    }

    /**
     * Definition of this Panel should include a parameter for the ModuleName for the
     * module from which the NodeStructure will be obtained.
     * E.g., workspace.PerceptualBuffer or PerceptualAssociativeMemory
     * @see edu.memphis.ccrg.lida.framework.gui.panels.GuiPanelImpl#initPanel(java.lang.String[])
     */
    @Override
    public void initPanel(String[] param) {
        if (param == null || param.length == 0) {
            logger.log(Level.WARNING,
                    "Error initializing NodeStructurePanel, not enough parameters.",
                    0L);
            return;
        }
        module = GuiUtils.parseFrameworkModule(param[0], agent);

        if (module != null) {
            display(module.getModuleContent());
            draw();
        } else {
            logger.log(Level.WARNING,
                    "Unable to parse module {1} Panel not initialized.",
                    new Object[]{0L, param[0]});
        }
    }

    @Override
    public void refresh() {
        display(module.getModuleContent());
        Layout<Linkable, GuiLink> layout = vizViewer.getGraphLayout();
        layout.initialize();
    }

    @Override
    public void display(Object o) {
        if (o instanceof NodeStructure) {
            guiGraph.setNodeStructure((NodeStructure) o);
        } else {
            logger.log(Level.WARNING,
                    "Panel can only display NodeStructure, but received {1} from module {2}",
                    new Object[]{TaskManager.getCurrentTick(), o, module});
        }
    }
}
