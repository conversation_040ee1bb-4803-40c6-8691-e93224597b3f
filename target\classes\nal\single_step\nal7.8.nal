'********** updating and revision 

'<PERSON> is holding key_101 now
<(*,<PERSON>,key_101) --> hold>. :|: 

6

'<PERSON> is not holding key_101 now
<(*,<PERSON>,key_101) --> hold>. :|: %0% 

'Is <PERSON> holding key_101 now? 
<(*,<PERSON>,key_101) --> hold>? :|: 

200

//revision on events
'<PERSON> maybe holding key_101 now
''outputMustContain('<John --> (/,hold,_,key_101)>. :!6: %0.50;0.95%')

//but also looking at it as separate:
'<PERSON> will not hold key_101 in the future
''outputMustContain('<John --> (/,hold,_,key_101)>. :!6: %0.00;0.90%')
