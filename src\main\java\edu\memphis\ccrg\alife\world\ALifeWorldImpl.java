package edu.memphis.ccrg.alife.world;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.elements.Cell;
import edu.memphis.ccrg.alife.elements.CellImpl;
import edu.memphis.ccrg.alife.elements.ObjectContainer;
import edu.memphis.ccrg.alife.elements.properties.AttributableImpl;
import edu.memphis.ccrg.alife.opreations.UpdateStrategy;
import edu.memphis.ccrg.alife.opreations.WorldOperation;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ALifeWorldImpl extends AttributableImpl implements ALifeWorld {
    private static final int DEFAULT_CELL_SIZE = 100;
    private static final Logger logger = Logger.getLogger(ALifeWorldImpl.class.getName());
    private Map<String, WorldOperation> actions;
    private Cell[][] cells;
    private int height;
    private Map<Integer, ALifeObject> objects;
    protected UpdateStrategy updateStrategy;
    private int width;

    public ALifeWorldImpl(int width2, int height2) {
        this(width2, height2, DEFAULT_CELL_SIZE);
    }

    public ALifeWorldImpl(int width2, int height2, int cellSize) {
        this.objects = new ConcurrentHashMap();
        this.actions = new HashMap();
        this.width = width2;
        this.height = height2;
        createCells(cellSize);
    }

    private void createCells(int cellSize) {
        if (this.width <= 0 || this.height > 0) {
        }
        this.cells = (Cell[][]) Array.newInstance(Cell.class, this.width, this.height);
        for (int i = 0; i < this.width; i++) {
            for (int j = 0; j < this.height; j++) {
                this.cells[i][j] = new CellImpl(i, j);
                this.cells[i][j].setCapacity(cellSize);
            }
        }
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public void addAction(String actionName, WorldOperation action) {
        this.actions.put(actionName, action);
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public boolean addObject(ALifeObject o, int x, int y) {
        Cell cell;
        if (x >= this.width || x < 0 || y >= this.height || y < 0 || (cell = this.cells[x][y]) == null || o == null || !cell.addObject(o)) {
            logger.log(Level.FINER, "object {0} can not be added to cell [{1}][{2}]", new Object[]{o, Integer.valueOf(x), Integer.valueOf(y)});
            return false;
        }
        registerObject(o);
        return true;
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public void registerObject(ALifeObject o) {
        this.objects.put(Integer.valueOf(o.getId()), o);
        if (o instanceof ObjectContainer) {
            for (ALifeObject obj : ((ObjectContainer) o).getObjects()) {
                registerObject(obj);
            }
        }
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public Cell getCell(int x, int y) {
        if (x >= this.width || x < 0 || y >= this.height || y < 0) {
            return null;
        }
        return this.cells[x][y];
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public Cell[][] getCells() {
        return this.cells;
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public ALifeObject getObject(int id) {
        return this.objects.get(Integer.valueOf(id));
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public Collection<ALifeObject> getObjects() {
        return this.objects.values();
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public Object performOperation(String actionName, ALifeObject subject, ALifeObject[] object, Object... params) {
        return performAction(this.actions.get(actionName), subject, object, params);
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public Object performAction(WorldOperation action, ALifeObject subject, ALifeObject[] object, Object... params) {
        if (action != null) {
            return action.performOperation(this, subject, object, params);
        }
        return null;
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public void updateWorldState() {
        updateState(this);
        for (int i = 0; i < this.width; i++) {
            for (int j = 0; j < this.height; j++) {
                this.cells[i][j].updateState(this);
            }
        }
        for (ALifeObject object : this.objects.values()) {
            object.updateState(this);
        }
    }

    @Override // edu.memphis.ccrg.alife.opreations.Updateable
    public void updateState(ALifeWorld world) {
    }

    @Override // edu.memphis.ccrg.alife.opreations.Updateable
    public void setUpdateStrategy(UpdateStrategy updateStrategy2) {
        this.updateStrategy = updateStrategy2;
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public int getWidth() {
        return this.width;
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public int getHeight() {
        return this.height;
    }

    @Override // edu.memphis.ccrg.alife.world.ALifeWorld
    public ALifeObject getObject(String name) {
        for (ALifeObject o : this.objects.values()) {
            if (o.getName().equals(name)) {
                return o;
            }
        }
        return null;
    }
}
