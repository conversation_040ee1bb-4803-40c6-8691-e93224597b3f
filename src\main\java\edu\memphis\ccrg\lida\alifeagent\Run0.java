/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/

package edu.memphis.ccrg.lida.alifeagent;

import com.warmer.kgmaker.KgmakerApplication;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import org.opennars.gui.NARSwing;
import org.opennars.lab.launcher.Launcher;

import java.util.logging.Level;
import java.util.logging.Logger;

public class Run0 extends KgmakerApplication {
    public static void main(String[] args){

        AgentStarter.main(args);

//        NARSwing.themeInvert();
//        // 全界面
////        java.awt.EventQueue.invokeLater(() -> new Launcher().setVisible(true));
//
//        // 主界面
//        try {
//            NARSwing sw = new NARSwing(AgentStarter.nar);
//        } catch (Exception ex) {
//            Logger.getLogger(Launcher.class.getName()).log(Level.SEVERE, null, ex);
//        }
    }
}
