package edu.memphis.ccrg.lida.data;

import edu.memphis.ccrg.lida.actionselection.ActionImpl;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.pam.*;
import edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemoryImpl;
import edu.memphis.ccrg.lida.proceduralmemory.Scheme;
import edu.memphis.ccrg.linars.Term;
import org.neo4j.graphdb.*;
import org.opennars.io.Parser;

import java.util.*;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.*;
//import static com.warmer.kgmaker.util.Neo4jUtil.graphDb;

public class NeoUtil {
    public static Map<String, Object > parameters = new HashMap<String, Object>();
    private static Node pnNode;
    private static Link reLink = null;
    private static boolean isExcite = false;

//    public  PamImpl0 pam = (PamImpl0) AgentStarter.pam;

    // 输入语句，查到的所有节点和边，都通过pam的receivePercept加入到指定buffer
    public static void addNodesAndRelsToBuffer(String query, ModuleName name) {
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                Map<String, Object> row;
                org.neo4j.graphdb.Node n;
                Relationship re;
                Link link0 = null;
                while ( result.hasNext() ) {
                    row = result.next();
                    for ( String key : result.columns() ) {
                        if (key.equals("n")) {
                            n = (org.neo4j.graphdb.Node) row.get(key);
                            // 节点转换为pamNodeImpl，并加入到buffer
                            Node pamNode = getPamNode(n,n.getProperty("name").toString());
                            pam.getListener().receivePercept(pamNode, name);
                        } else if (key.equals("re")) {
                            re = (Relationship) row.get(key);
                            link0 = CastNeoToLidaLink(re,null);
                            // 加入到buffer
                            pam.getListener().receivePercept(link0, name);
                        }
                    }
                }
            }
            tx.commit();
        }
    }

    public static Set<Scheme> getSchemes(Node n, ProceduralMemoryImpl pm) {
        Set<Scheme> schemes = new HashSet<>();
        Scheme sss;
        String query = "";
        query ="match (n:word{name:\'"+ n.getTNname() +"\'})-[r:longE]->() return r";
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                Set<String> areadyAdd = new HashSet<>();
                String tid;
                String endName;

                Map<String, Object> row;
                Relationship re;
                while ( result.hasNext() ) {
                    row = result.next();
                    for ( String key : result.columns() ) {
                        re = (Relationship) row.get(key);
//                        tid = (String) re.getProperty("taId");
//                        System.out.println(re.getStartNode().getProperty("name") + "的 id 是: " + re.getStartNode().getId());
                        endName = (String) re.getEndNode().getProperty("name");
                        if (areadyAdd.contains(endName)) {
                            continue; // 同类长链，一条就够?如turn，后面有多个行为选择
                        }
                        areadyAdd.add(endName);
                        query ="match (n:Verb{name:\'"+ endName +"\'})-[r:action]->() return r";
                        try ( Result result0 = tx.execute( query, parameters ) ) {
                            Map<String, Object> row0;
                            String schLabel;
                            while (result0.hasNext()) {
                                row0 = result0.next();
                                Relationship re0;
                                ActionImpl action;
                                for ( String key0 : result0.columns() ) {
                                    // 每次一个新的schema
                                    sss = pm.getNewScheme();
                                    re0 = (Relationship) row0.get(key0);
                                    schLabel = (String)re0.getStartNode().getProperty("name") + (String)re0.getEndNode().getProperty("name");
                                    sss.setName(schLabel);

//                                    action = new ActionImpl("action." + schLabel);
                                    action = new ActionImpl(schLabel);
//                                    action.setId(Integer.parseInt((String) re0.getProperty("aId")));
                                    action.setId(Integer.parseInt(String.valueOf(re0.getId())));
                                    sss.setAction(action);

                                    schemes.add(sss);
                                }
                            }
                        }
                    }
                }
            }
            tx.commit();
        }
        return schemes;
    }
    // 常规查节点，无特异
    public static Node getNode(String name) {
        if (name == null) {
            return null;
        }
        Node node1 = new PamNodeImpl();

        // 如果name是字符串数字，可能会导致查不到
        String query ="match (n{name:\'"+ name +"\'}) return n";
        Object weight = null;
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                int num = 0;
                while ( result.hasNext() ) {
                    num++;
                    Map<String, Object> row = result.next();

                    org.neo4j.graphdb.Node node = (org.neo4j.graphdb.Node) row.get(result.columns().get(0));
//                    isExcite = true;
//                    Node = castNeoToLidaNode(node,null);

                    node1 = getPamNode(node, name);
                    try {
                        weight = node.getProperty("weight");
                    }catch (NotFoundException e){
                        weight = 0.88;
                    }

                    // 类型可能是字符串、long、double
                    if(weight instanceof String){
                        node1.setActivation(Double.parseDouble((String) weight));
                    }else if(weight instanceof Long){
                        node1.setActivation((double) weight);
                    }else if(weight instanceof Double) {
                        node1.setActivation((double) weight);
                    }
                    break;
                }
                if (num == 0) {
                    boolean aaa = result.hasNext();
                    System.out.println("查不到这个：" + name);
                    node1 = null;
                }
//                Label scene = Label.label( "场景" );
//                tx.findNode(scene,"name","ft13");
            }
            tx.commit();
        }
        return node1;
    }

    // merge或create节点
    public static Node mergeNode(String name, String label) {
        if (name == null) {return null;}
        Node node1 = new PamNodeImpl();

        // 如果name是字符串数字，可能会导致查不到
        String query = "merge (n:" + label + "{name:\'" + name + "\'}) return n";
        Object weight = null;
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(query, parameters)) {
                while (result.hasNext()) {
                    Map<String, Object> row = result.next();

                    org.neo4j.graphdb.Node node = (org.neo4j.graphdb.Node) row.get(result.columns().get(0));
                    node1 = getPamNode(node, name);
                }
            }
            tx.commit();
        }
        return node1;
    }

    // merge或create边
    public static Link mergeLink(String linkName, String linkLabel, String fromNodeName, String toNodeName,
                                 String fromLabel, String toLabel) {
        if (linkName == null && linkLabel == null) {return null;}
        Link link = new PamLinkImpl();
        String query = "merge (n:" + fromLabel + "{name:\'" + fromNodeName + "\', weight:'0.87'}) merge (m:" + toLabel + "{name:\'" + toNodeName + "\', weight:'0.87'}) ";

        if (linkName != null && linkLabel != null) {
            query += "merge (n)-[r:" + linkLabel + "{name:\'" + linkName + "\', weight:'0.87'}]->(m) return r";
        }else if (linkName == null) {
            query += "merge (n)-[r:" + linkLabel + "{weight:'0.87'}]->(m) return r";
        }else {
            query += "merge (n)-[r{name:\"" + linkName + ", weight:'0.87'}]->(m) return r";
        }
        try (Transaction tx = graphDb.beginTx()) {
            try (Result result = tx.execute(query, parameters)) {
                while (result.hasNext()) {
                    Map<String, Object> row = result.next();

                    Relationship re = (Relationship) row.get(result.columns().get(0));
                    link = CastNeoToLidaLink(re,null);
                }
            }
            tx.commit();
        }
        return link;
    }

//    private static Node castNeoToLidaNode(org.neo4j.graphdb.Node node, PamLink reLink) {
//        String name = String.valueOf(node.getProperty("name"));
//        Node node1 = getPamNode(node, name);
//
//        double weight = (double) node.getProperty("weight");
//        double act = 0;
////        if (isExcite){
////            act = pnNode.getActivation();
////        } else
//        if (weight != 0 && reLink != null) {
//
//            // 权重激活传递，激发点-激发链接-被激发点，沿途激活度都会影响后续，各自权重独立
//            // 靠近搜索端计算传播值，下游更便捷，但全部默认计算=量大，
//            // 注意筛选=按点边类型，也需要全部算=并不是统一赋默认值，是按比例
//            // 点权重=激活加成，可能有不同点边阈值，调高阈值+降低加成=都更难传播，但意义不一样
//            act = weight * reLink.getActivation();
//            System.out.println(name + "--------- act --------" + act);
//        }else if(weight == 0 && reLink != null){
//            act = reLink.getActivation();
//        }else if(reLink == null && weight != 0){
//            act = weight;
//        }else {
//            act = 0.5;
//        }
//        node1.setActivation(act);
////        reLink = null;
////        isExcite = false;
//
//        for (Label label : node.getLabels()) {
//            node1.setLabel(label.name());
//            break;
//        }
//        node1.setFactoryType("PamNodeImpl");
//
//        return node1;
//    }

    public static Node getPamNode(org.neo4j.graphdb.Node node, String name) {
        Node node1 = new PamNodeImpl(name);
        node1.setNodeId((int) node.getId());
        node1.setNodeName(name);

        List<String> labels = new ArrayList<>();
        for (Label lb: node.getLabels()) {
            labels.add(lb.name());
        }

        node1.setLabels(labels);
//        node1.setNodeProxy((NodeEntity) node);
        node1.setProperties(node.getAllProperties());
        return node1;
    }

    public static Object getProperty(org.neo4j.graphdb.Node node ,String key) {
        Map<String, Object> properties = node.getAllProperties();
        for (String k: properties.keySet()) {
            if (k.equals(key)) {
                return properties.get(k);
            }
        }
        return null;
    }

    public static PamLink CastNeoToLidaLink(Relationship re, Node pnNode) {
        PamLink link = new PamLinkImpl();
        PamNode category = new PamNodeImpl();
        String type = re.getType().toString();
        category.setNodeName(type); // neo关系类型只有一个
        category.setNodeId(((Long)(re.getId())).intValue());

//        String namef = String.valueOf(re.getStartNode().getProperty("name"));
        String namef = (String) getProperty(re.getStartNode(),"name");
        Node fromNode = getPamNode(re.getStartNode(), namef);

//        String namet = String.valueOf(re.getEndNode().getProperty("name"));
        String namet = (String) getProperty(re.getEndNode(),"name");
        Node toNode = getPamNode(re.getEndNode(), namet);
        link.setCategory(category);
        double act = 0;
        Object weight0 = null;
        Object wt0 = null;
        double weight = 0;
        double wt = 0;
        if (pnNode != null) {
//            System.out.println(namef + "--" + type + "--" + namet);
            // 目前小于1，激活度联动后衰减，比来源小

            try {
                weight0 = re.getProperty("weight");
            }catch (NotFoundException e){
                weight0 = 0.88;
            }

            if(weight0 instanceof String){
                weight = Double.parseDouble((String) weight0);
            }else if(weight0 instanceof Long){
                weight = (double) weight0;
            }

            if (weight != 0) {
                act = weight * pnNode.getActivation();
            } else {
                act = pnNode.getActivation();
            }

            fromNode.setActivation(pnNode.getActivation());
            try {
                wt0 = re.getEndNode().getProperty("weight");
            }catch (NotFoundException e) {
                wt0 = 0.88;
            }

            if (wt0 instanceof String) {
                wt = Double.parseDouble((String) wt0);
            } else if (wt0 instanceof Long) {
                wt = (double) wt0;
            }

            toNode.setActivation(wt*act);
        }else {
            act = 0.8;
            fromNode.setActivation(act);
            toNode.setActivation(act);
        }

        link.setActivation(act);

        link.setSource(fromNode);
        link.setSink(toNode);
        link.setId(((Long)(re.getId())).intValue());
//        link.setLinkProxy((RelationshipEntity) re);
        link.setProperties(re.getAllProperties());
        ((PamLinkImpl)link).init(((PamLinkImpl)link).term);
        return link;
    }
    // 常规查链接，单向，出
    public static Set<Link> getLinks(Node n) {
        if (n == null) {return null;}
        Set<Link> links = new HashSet<Link>();
        Link link;
        String query ="match (n{name:\'"+ n.getTNname() +"\'})-[r]->() return r";
        links = getLinksCypher(query, n);
        return links;
    }
    // 只是执行，不返回结果
    public static void excuteOnly(String query) {
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters )) {

            }
            tx.commit();
        }
    }

    // 执行cypher，返回node
    public static Node getNodeCypher(String query) {
        Node node1 = null;
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                while ( result.hasNext() ) {
                    Map<String, Object> row = result.next();
                    org.neo4j.graphdb.Node node = (org.neo4j.graphdb.Node) row.get(result.columns().get(0));
                    node1 = getPamNode(node, node.getProperty("name").toString());
                    break;
                }
            }
            tx.commit();
        }
        return node1;
    }

    // 执行cypher，返回link
    public static Link getLinkCypher(String query) {
        Link link = null;
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                while ( result.hasNext() ) {
                    Map<String, Object> row = result.next();
                    Relationship re = (Relationship) row.get(result.columns().get(0));
                    link = CastNeoToLidaLink(re,null);
                    break;
                }
            }
            tx.commit();
        }
        return link;
    }

    // 执行cypher，返回links
    public static Set<Link> getLinksCypher(String query) {
        return getLinksCypher(query, null);
    }

    // 执行cypher，返回nodes
    public static Set<Node> getNodesCypher(String query) {
        Set<Node> nodes = new HashSet<Node>();
        Node node;
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                while ( result.hasNext() ) {
                    Map<String, Object> row = result.next();
                    org.neo4j.graphdb.Node node0 = (org.neo4j.graphdb.Node) row.get(result.columns().get(0));
                    node = getPamNode(node0, node0.getProperty("name").toString());
                    nodes.add(node);
                }
            }
            tx.commit();
        }
        return nodes;
    }

    // 执行cypher，返回path
    public static List<Node> getPathCypher(String query) {
        List<Node> nodes = new ArrayList<Node>();
        Node node;
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                while ( result.hasNext() ) {
                    Map<String, Object> row = result.next();
                    org.neo4j.graphdb.Node node0 = (org.neo4j.graphdb.Node) row.get(result.columns().get(0));
                    node = getPamNode(node0, node0.getProperty("name").toString());
                    nodes.add(node);
                }
            }
            tx.commit();
        }
        return nodes;
    }

    // 在事务内查一条边，返回后还在事务内，可在后续用事务内api操作
    public static Link getOneLinkTx(String query, Transaction tx) {
        Link link0 = null;
        try (Result result0 = tx.execute(query, NeoUtil.parameters)) {
            Map<String, Object> row0;
            while (result0.hasNext()) {
                row0 = result0.next();
                Relationship actre;
                for (String key0 : result0.columns()) {
                    actre = (Relationship) row0.get(key0);

                    link0 = NeoUtil.CastNeoToLidaLink(actre,null);
                    Node toNode = (Node)link0.getSink();
//                    toNode.setIncentiveSalience(sink.getIncentiveSalience());

                    // 每个时序分别加入计划，以备执行，头节点已有，不用加入
                    pam.getListener().receivePercept(toNode, ModuleName.SeqGraph);
                    pam.getListener().receivePercept(link0, ModuleName.SeqGraph);

                    System.out.println("------时序执行------" + link0.toString());
                }
            }
        }
        return link0;
    }

    public static Link getOneLink(Node n, String linktype, String fto, String n1type, String n2type) {
        Link link = null;
        Set<Link> links = getSomeLinks(n, linktype, fto, n1type, n2type);
        for (Link l : links) {
            link = l;
            break;
        }
        return link;
    }

    // 根据条件约束查两点间单度链接
    public static Set<Link> getSomeLinks(Node n, String linktype, String fto, String n1type, String n2type) {
//        if (n == null) {
//            return null;
//        }
        Set<Link> links = new HashSet<Link>();
        Link link;
        StringBuilder sb = new StringBuilder();
        String query;
        sb.append("match (n");
        if(n1type != null && !n1type.equals("")){
            sb.append(":" + n1type);
        }
        if (n == null) {
            sb.append(")");
        }else {
            sb.append("{name:\'"+ n.getTNname() +"\'})");
        }
        if(fto != null && fto.equals("<")){
            sb.append(fto);
        }
        sb.append("-[r");
        if(linktype != null && !linktype.equals("")){
            sb.append(":" + linktype);
        }
        sb.append("]-");
        if(fto != null && fto.equals(">")){
            sb.append(fto);
        }
        sb.append("(m");
        if(n2type != null && !n2type.equals("")){
            sb.append(":" + n2type);
        }
        sb.append(") return r");
//        query ="match (n{name:\'"+ n.getName() +"\'})-[r]-(m:变量) return r";
        query = sb.toString();

        links = getLinksCypher(query, n);

//        System.out.println("结束查getSomeLinks-----" + query);
        return links;
    }

    // 两点间特定度数内的所有节点，不限特定类型
    public static Set<Node> getNsN2N(String name1, String name2, int degree) {
        String query = "MATCH p=((n{name:\"" + name1 + "\"})-[*" + degree + "]-(m{name:\"" + name2 + "\"})) RETURN nodes(p)\n";
        System.out.println("query = " + query);
        Set<Node> nodes = new HashSet<>();
        try (Transaction tx0 = graphDb.beginTx()) {
            try (Result result0 = tx0.execute(query, NeoUtil.parameters)) {
                Map<String, Object> row0;
                while (result0.hasNext()) {
                    row0 = result0.next();
                    List<org.neo4j.graphdb.Node> resultNodes;
                    for (String key0 : result0.columns()) {
                        // nodes(p)是一个list，里面是node
                        resultNodes = (List<org.neo4j.graphdb.Node>) row0.get(key0);
                        for (org.neo4j.graphdb.Node scene0 : resultNodes) {
                            Node node1 = NeoUtil.getPamNode(scene0, scene0.getProperty("name").toString());
                            nodes.add(node1);
                        }
                    }
                }
            }
            tx0.commit();
        }
        return nodes;
    }

    public static void shortest(String startNodeName, String endNodeName) {
        try (Transaction tx0 = graphDb.beginTx()) {
            String cypherQuery = "MATCH (n)-[r]-(m) WHERE n.name = '"
                    + startNodeName + "' AND m.name = '" + endNodeName
                    + "' RETURN shortestPath((n)-[*..10]-(m))";

            try (Result result = tx0.execute(cypherQuery, NeoUtil.parameters)) {
                while (result.hasNext()) {
                    Map<String, Object> row = result.next();
                    org.neo4j.graphdb.Path shortestPaths;
                    for (String key : result.columns()) {
                        shortestPaths = (org.neo4j.graphdb.Path)row.get(key);
                        System.out.println("Shortest path between " + startNodeName + " and " + endNodeName + ":");
//                        for (org.neo4j.graphdb.Path path : shortestPaths) {
                            System.out.println(shortestPaths);
//                        }
                    }
                }
            }
            tx0.commit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static Set<Link> getLinksCypher(String query , Node n) {
        Set<Link> links = new HashSet<Link>();
        Link link;
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                while ( result.hasNext() ) {
                    Map<String, Object> row = result.next();
                    Relationship re;
                    for ( String key : result.columns() ) {
                        re = (Relationship) row.get(key);
                        // 实时场景，还是需要转，以便流通全程，记忆子图则用neo？
                        link = CastNeoToLidaLink(re,n);
                        links.add(link);
                    }
                }
            }
            tx.commit();
        }
        return links;
    }

    public static int setTermToNode(Term term, Node node, int num) throws Parser.InvalidInputException {
        Term finalTerm = narsese.parseTerm(term.toString());
        String str = "linars";
        if(finalTerm.getComplexity() > 12){
            System.out.println("复杂度过高，不更新到图谱---------- " + term);
            // 用其他代号代替
            str = "nars_" + num;
            num++;
        }else {
            str = term.toString();
        }
        String query;
        // 以node的id为准，不用name，因为name可能重复。cpstr意思是复合词的字符串
        query = "match (n) where id(n) = " + node.getNodeId() + " set n.name = \'" + str + "\'";
        excuteOnly(query);
        return num;
    }

    // 以场景点为中心，找到整个场景元素，方向为入
    public static List<Node> getSceneSon(String tnname) {
        List<Node> nodes = new ArrayList<>();
        String query = "match (n:场景{name:\'" + tnname + "\'})<-[r]-(m) return r";
        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( query, parameters ) ) {
                Map<String, Object> row;
                Relationship re;
                Set<Relationship> links = new HashSet<>();
                while ( result.hasNext() ) {
                    row = result.next();
                    // 关系类型如果有arg，则是场景元素，取source，否则不取
                    for ( String key : result.columns() ) {
                        re = (Relationship) row.get(key);
                        if (re.getType().toString().contains("arg")) {
                            links.add(re);
                        }
                    }
                }
                // 对links排序
                List<Relationship> linkList = new ArrayList<>();
                for (String s : arg) {
                    for (Relationship link : links) {
                        if (link.getType().toString().equals(s)) {
                            linkList.add(link);
                        }
                    }
                }
                for (Relationship re0 : linkList) {
                    Node node = NeoUtil.getPamNode(re0.getStartNode(), re0.getStartNode().getProperty("name").toString());
                    nodes.add(node);
                }
            }
            tx.commit();
        }
        return nodes;
    }

    // 将links按边类型次序排列，如arg0，arg1，arg2。先获取link类型，再按类型排序，用集合
    // 没有arg开头的，按照原来的顺序排列
    public static void sortLinks(List<Link> links) {
        List<Link> linkList = new ArrayList<>();
        for (String s : arg) {
            for (Link link : links) {
                if (link.getTNname().equals(s)) {
                    linkList.add(link);
                }
            }
        }
        if (linkList.isEmpty()) {
            linkList.addAll(links);
        }else if (linkList.size() < links.size()) {
            // 有arg开头的，但不全，按照原来的顺序排列
            for (Link link : links) {
                if (!linkList.contains(link)) {
                    linkList.add(link);
                }
            }
        } else {
            // 有arg开头的，且全，按照arg开头的顺序排列
//          linkList.addAll(links);
        }
        links.clear();
        links.addAll(linkList);
//        links = linkList;
    }

    // 任意查询
    public static List<Map<String, Object>> getByCypher(String cypher, Transaction tx) {
        List<Map<String, Object>> list = new ArrayList<>();
//        try (Transaction tx = graphDb.beginTx()) {
            try ( Result result = tx.execute( cypher ) ) {
//                Map<String, Object> row0;
                while ( result.hasNext() ) {
                    list.add(result.next());
//                    row0 = result.next();
//                    for (String key0 : result.columns()) {
//
//                    }
                }
            }
//            tx.commit();
//        }
        return list;
    }
}
