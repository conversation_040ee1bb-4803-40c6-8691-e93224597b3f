'********** temporal induction/comparison 

'Someone open door_101 before he enter room_101
<<(*, $x, door_101) --> open> =/> <(*, $x, room_101) --> enter>>. %0.9%  

'Someone open door_101 after he hold key_101
<<(*, $y, door_101) --> open> =\> <(*, $y, key_101) --> hold>>. %0.8% 

100

'If someone hold key_101, he will enter room_101
''outputMustContain('<<(*,$1,key_101) --> hold> =/> <(*,$1,room_101) --> enter>>. %0.90;0.39%')  
'If someone enter room_101, he should hold key_101 before
''outputMustContain('<<(*,$1,room_101) --> enter> =\> <(*,$1,key_101) --> hold>>. %0.80;0.42%') 
'If someone hold key_101, it means he will enter room_101
''outputMustContain('<<(*,$1,key_101) --> hold> </> <(*,$1,room_101) --> enter>>. %0.73;0.44%')

