//First: Input diamond:
// |    ██    |
// |  ██  ██  |
// |██      ██|
// |  ██  ██  |
// |    ██    |:
<{M1[-1.0,0.0]} --> [BRIGHT]>.
<{M1[1.0,0.0]} --> [BRIGHT]>.
<{M1[0.0,1.0]} --> [BRIGHT]>.
<{M1[0.0,-1.0]} --> [BRIGHT]>.
<{M1[0.5,0.5]} --> [BRIGHT]>.
<{M1[-0.5,0.5]} --> [BRIGHT]>.
<{M1[0.5,-0.5]} --> [BRIGHT]>.
<{M1[-0.5,-0.5]} --> [BRIGHT]>.
<{M1} --> (/,called,_,circle)>.

//Second: Input cross:
// |    ██    |
// |    ██    |
// |██████████|
// |    ██    |
// |    ██    |:
<{M2[0.0,1.0]} --> [BRIGHT]>.
<{M2[0.0,0.5]} --> [BRIGHT]>.
<{M2[-1.0,0.0]} --> [BRIGHT]>.
<{M2[-0.5,0.0]} --> [BRIGHT]>.
<{M2[0.0,0.0]} --> [BRIGHT]>.
<{M2[0.5,0.0]} --> [BRIGHT]>.
<{M2[1.0,0.0]} --> [BRIGHT]>.
<{M2[0.0,-1.0]} --> [BRIGHT]>.
<{M2[0.0,-0.5]} --> [BRIGHT]>.
<{M2} --> (/,called,_,cross)>.


//Re-observe imperfectly
// |▒▒  ██    |
// |  ██  ▒▒  |
// |▒▒      ██|
// |  ██  ▒▒▒▒|
// |          |:
<{M3[-1.0,1.0]} --> [BRIGHT]>. %0.5%
<{M3[0.0,1.0]} --> [BRIGHT]>.
<{M3[-0.5,0.5]} --> [BRIGHT]>.
<{M3[0.5,0.5]} --> [BRIGHT]>. %0.5%
<{M3[-1.0,0.0]} --> [BRIGHT]>. %0.5%
<{M3[1.0,0.0]} --> [BRIGHT]>.
<{M3[-0.5,-0.5]} --> [BRIGHT]>.
<{M3[0.5,-0.5]} --> [BRIGHT]>. %0.5%
<{M3[1.0,-0.5]} --> [BRIGHT]>. %0.5%

50000
//What was observed?
<{M3} --> (/,called,_,?what)>?
//A circle
''outputMustContain('<{M3} --> (/,called,_,circle)>. %0.83;0.36%')
