/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.alifeagent.environment.operations;

import edu.memphis.ccrg.alife.elements.ALifeObject;
import edu.memphis.ccrg.alife.opreations.MoveOperation;
import edu.memphis.ccrg.alife.world.ALifeWorld;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PamLinkable;

import java.util.logging.Level;
import java.util.logging.Logger;

public class GetFoodOperation extends MoveOperation {

	private static final Logger logger = Logger.getLogger(GetFoodOperation.class.getCanonicalName());
	private static final double DEFAULT_BUMP_HEALTH_PENALTY = 0.05;

	private NodeStructure nodeStructure = new NodeStructureImpl();

	@Override
	public Object performOperation(ALifeWorld world, ALifeObject subject,
			ALifeObject[] object, Object... params) {

		if (!move(world, subject, (Character) subject.getAttribute("direction"))) {
			subject.decreaseHealth(DEFAULT_BUMP_HEALTH_PENALTY);
			return false;
		}

		Linkable linkable = AgentStarter.pam.getNode("get");
		if (linkable == null) {
			linkable = nodeStructure.getNeoNode("get");
			if (linkable != null) {
				AgentStarter.pam.addDefaultNode((Node) linkable);
			}
		}
//		AgentStarter.pam.receiveExcitation(linkable, 0.8, "feel");

		logger.log(Level.FINE, "agent moves forward", TaskManager.getCurrentTick());
		return true;
	}
}
