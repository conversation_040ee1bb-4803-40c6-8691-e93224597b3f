<(*,a,b) --> like>. %1.00;0.90%
<(*,b,c) --> like>. %1.00;0.90%
<(*,a,c) --> like>. %1.00;0.90%
<<(*,b,$1) --> like> ==> <(*,a,$1) --> like>>?
//<(&&,<(*,#1,$2) --> like>,<(*,$3,#1) --> like>) ==> <(*,$3,$2) --> like>>?
210000
''outputMustContain('<<(*,b,$1) --> like> ==> <(*,a,$1) --> like>>. %1.00;0.45%')
''outputMustContain('<(&&,<(*,#1,$2) --> like>,<(*,$3,#1) --> like>) ==> <(*,$3,$2) --> like>>. %1.00;0.26%')
