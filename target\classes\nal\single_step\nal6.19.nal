'********** variables introduction

'Key-1 opens Lock-1.
<{key1} --> (/,open,_,{lock1})>. 

'Key-1 is a key.
<{key1} --> key>.

45

'I guess every key can open Lock-1.
''outputMustContain('<<$1 --> key> ==> <$1 --> (/,open,_,{lock1})>>. %1.00;0.45%')

'Some key can open Lock-1.
''//outputMustContain('(&&,<#1 --> (/,open,_,{lock1})>,<#1 --> key>). %1.00;0.81%') //reversed
''  outputMustContain('(&&,<#1 --> (/,open,_,{lock1})>,<#1 --> key>). %1.00;0.25%')
