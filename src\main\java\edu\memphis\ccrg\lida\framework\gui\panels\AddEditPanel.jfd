JF<PERSON><PERSON> JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {seq {space :::p, par l {seq {par l {seq {par l {comp jLabel2::l:p::p, comp jLabel1::l:p::p, comp jLabel5::l:p::p, comp jLabel3::l:p::p}, space :::p, par l {comp cmbPosition::l::319:x:1, comp txtName::l::319:x:1, comp jLabel6::l:p::p, comp txtParameters::l::319:x:1, comp cmbClassname::l::319:x}}, seq l {comp chkRefresh:::p::p, space ::197:p}}, space :::p}, seq t {comp btnAddPanel:::p:126:p, space :p:94:p}}}}"
			"$verticalGroup": "par l {seq {space :::p, par b {comp jLabel1::b:p::p, comp txtName::b:p::p}, space :::p, par b {comp jLabel2::b:p::p, comp cmbClassname::b:p::p}, space :::p, par b {comp cmbPosition::b:p::p, comp jLabel3::b:p::p}, space :::p, par t {comp jLabel5:::p::p, comp txtParameters:::p::p}, space :::p, comp jLabel6:::p::p, space :::p, comp chkRefresh:::p::p, space ::90:x, comp btnAddPanel:::p:33:p, space :::p}}"
		} ) {
			name: "this"
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "jLabel1"
				"text": "Name:"
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txtName"
				"preferredSize": new java.awt.Dimension( 60, 20 )
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "jLabel2"
				"text": "Classname:"
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "jLabel3"
				"text": "Position:"
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cmbPosition"
				"model": new javax.swing.DefaultComboBoxModel {
					selectedItem: "A"
					addElement( "A" )
					addElement( "B" )
					addElement( "C" )
					addElement( "D" )
					addElement( "E" )
					addElement( "FLOAT" )
					addElement( "TOOL" )
				}
				"selectedIndex": 1
			} )
			add( new FormComponent( "javax.swing.JCheckBox" ) {
				name: "chkRefresh"
				"selected": true
				"text": "Refresh after load"
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btnAddPanel"
				"text": "OK"
				addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "btnAddPanelActionPerformed", true ) )
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "jLabel5"
				"text": "Parameters:"
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txtParameters"
				"preferredSize": new java.awt.Dimension( 60, 20 )
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "jLabel6"
				"text": "(separated by \",\")"
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cmbClassname"
				"editable": true
				"model": new javax.swing.DefaultComboBoxModel
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
