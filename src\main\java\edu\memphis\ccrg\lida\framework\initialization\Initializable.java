/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.framework.initialization;

import java.util.Map;

/**
 * An object that can be configured with parameters. 可以使用参数配置的对象
 * <AUTHOR>
 */
public interface Initializable {

	/**
	 * Initialize this object with parameters.
	 * 
	 * @see AgentXmlFactory
	 * @param parameters Map of parameters indexed by their String names
	 */
	public void init(Map<String, ?> parameters);

	/**
	 * This is a convenience method to initialize this Object with parameters. 
	 * It is called from {@link #init(Map)}.
	 * Subclasses can overwrite this method and initialize their parameters. 
	 * Make sure to call super.init() at the beginning of this method 
	 * so the {@link #init()} method of superclasses will run.
	 *
	 * 这是使用参数初始化此Object的便捷方法。 *从{@link #init（Map）}调用。 *子类可以覆盖此方法并初始化其参数。
	 * *确保在此方法的开头调用super.init（）*这样超类的{@link #init（）}方法将运行
	 */
	public void init();

	/**
	 * Method to read parameters from the Map of properties set 
	 * by the {@link #init(Map)} method.
	 * @param <T> expected type of the parameter
	 * @param name the parameter name
	 * @param defaultValue the default value to be returned if the parameter doesn't exist
	 * @return the value of the parameter or the default value
	 */
	public <T> T getParam(String name, T defaultValue);
	
	/**
	 * Returns whether a parameter with specified key is in this {@link Initializable}
	 * @param key {@link String}
	 * @return true if specified key exists
	 */
	public boolean containsParameter(String key);
	
	/**
	 * Returns the Map of parameters of this initializable
	 * @return the Map of parameters
	 */
	public Map<String, ?> getParameters();

}