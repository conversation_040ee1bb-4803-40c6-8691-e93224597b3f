package com.warmer.kgmaker.controller;

import com.warmer.kgmaker.util.Neo4jUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/api/graph/")
public class XRController {

	@Autowired
	private Neo4jUtil neo4jUtil;

	@ResponseBody
	@RequestMapping("/neo4j/project/getInfo")
	public Map<String, Object> getInfo(HttpServletRequest request) {
		Map mm = request.getParameterMap();
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/project/getUISetting")
	public Map<String, Object> getUISetting(HttpServletRequest request, String projectId) {
		Map mm = request.getParameterMap();
		Object pp = request.getParameter("projectId");
		System.out.println(projectId);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/project/connect")
	public Map<String, Object> connect(HttpServletRequest request) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/project/getProjectSettings")
	public Map<String, Object> getProjectSettings(HttpServletRequest request) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/neo4jBaseInfo")
	public Map<String, Object> neo4jBaseInfo(HttpServletRequest request) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/neo4jBaseInfo/SampleQuery")
	public Map<String, Object> SampleQuery(HttpServletRequest request){
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/excuteCommand")
	public Map<String, Object> excuteCommand(String command,String projectId,String queryLimit,String options,String params){
		System.out.println("参数 ---- " + command + "----" + projectId + "----" + queryLimit + "----" + options + "----" + params);
		Map<String, Object> result = new HashMap<String, Object>();

		Map<String, Object> graphData0 = neo4jUtil.excuteCypherSql(command,"GetGraphNodeAndShip").get(0);

		Map<String, Object> graphData = new HashMap<>();

		graphData.put("data", graphData0);
		graphData.put("type", "GRAPH");
		graphData.put("summary","4.4.10");

		result.put("content", graphData);
		result.put("status", 0);
		result.put("message","Query successful.");
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/project/updateforceLayoutSettings")
	public Map<String, Object> updateforceLayoutSettings(HttpServletRequest request){
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/project/updateProjectSettings")
	public Map<String, Object> updateProjectSettings(HttpServletRequest request){
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		return result;
	}

	@ResponseBody
	@RequestMapping("/neo4j/label/{e}/properties")
	public Map<String, Object> label_properties(HttpServletRequest request, @PathVariable String e){
		Map mm = request.getParameterMap();
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 200);
		System.out.println("---e---" + e);
		return result;
	}


}

