/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.attentioncodelets;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * An {@link AttentionCodelet} that seeks to create {@link Coalition}s from its sought content.
 * The resulting {@link Coalition} includes these nodes and possibly neighbors nodes.
 *
 * 概念提取，查找相近节点，与传统注意力不太一样，传统是筛选信息=聚焦
 * 注意力打包=激活值汇编，超过阈值，会被选中广播
 * todo 相似注意小码  注意都是从上而下 = 貌似从下而上的也必须经过意识才能调动注意力 + 至少是无意识
 */
public class AttentionTask extends FrameworkTaskImpl {

    private static final Logger logger = Logger.getLogger(AttentionTask.class.getCanonicalName());
    private NodeStructure nodeStructure = new NodeStructureImpl();

    public AttentionTask(String soughtContent){
        super.init();
    }

    public AttentionTask(){
        super.init();
    }

    @Override
    protected void runThisFrameworkTask() {

    }

}