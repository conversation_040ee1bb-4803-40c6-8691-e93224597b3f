JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {comp toolBar::l::450:x};par l {comp mainPanel::l:::x}"
			"$verticalGroup": "par l {seq l {comp toolBar:::p:28:p, space ::272:x:1}};par l {seq l {space :p:33:p, comp mainPanel:::::x}}"
		} ) {
			name: "this"
			"minimumSize": new java.awt.Dimension( 250, 34 )
			"preferredSize": new java.awt.Dimension( 250, 200 )
			add( new FormContainer( "javax.swing.JToolBar", new FormLayoutManager( class javax.swing.JToolBar ) ) {
				name: "toolBar"
				"rollover": true
				add( new FormComponent( "javax.swing.JLabel" ) {
					name: "jLabel1"
					"text": "Module "
				} )
				add( new FormComponent( "javax.swing.JComboBox" ) {
					name: "moduleComboBox"
					"model": new javax.swing.DefaultComboBoxModel
					"preferredSize": new java.awt.Dimension( 200, 22 )
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "moduleComboBoxActionPerformed", true ) )
				} )
				add( new FormComponent( "javax.swing.JToolBar$Separator" ) {
					name: "jSeparator1"
				} )
				add( new FormComponent( "javax.swing.JLabel" ) {
					name: "jLabel2"
					"text": " Element Name "
				} )
				add( new FormComponent( "javax.swing.JTextField" ) {
					name: "elementNameTextField"
					"maximumSize": new java.awt.Dimension( 100, 22 )
					"preferredSize": new java.awt.Dimension( 90, 22 )
				} )
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "displayButton"
					"font": new java.awt.Font( "Tahoma", 1, 11 )
					"text": "Plot Activation"
					"focusable": false
					"horizontalTextPosition": 0
					"verticalTextPosition": 3
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "displayButtonActionPerformed", true ) )
				} )
			} )
			add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
				"$horizontalGroup": "par l {space :0:450:x}"
				"$verticalGroup": "par l {space :0:267:x}"
			} ) {
				name: "mainPanel"
				auxiliary() {
					"JavaCodeGenerator.customCreateCode": "new ChartPanel(chart);"
				}
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
