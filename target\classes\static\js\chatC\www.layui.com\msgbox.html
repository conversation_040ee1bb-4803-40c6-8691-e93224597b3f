<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>消息盒子</title>
  <link rel="stylesheet" href="../res.layui.com/layui/src/css/layui.css">
  <link rel="stylesheet" href="../res.layui.com/layui/src/css/layui.demo.css">

</head>
<body>

<ul class="layim-msgbox" id="LAY_view"></ul>
<textarea title="消息模版" id="LAY_tpl" style="display:none;">
            {{# layui.each(d.data, function(index, item){
                if(item.msgType == 1 || item.msgType == 3){ }}
                    {{# if(item.fromUser == d.id){ }}
                        <li data-uid="{{ item.toId }}">
                          <a href="javascript:void(0);" target="_blank">
                            <img src="/uploads/person/{{ item.toId }}.jpg" class="layui-circle layim-msgbox-avatar img0" >
                              <!--<img src="/uploads/person/{{ item.toId }}.jpg" class="layui-circle layim-msgbox-avatar" onerror="javascript:this.src='../../../../../../uploads/person/{{# if(item.msgType == 1){ }}empty2.jpg{{# }else{ }}empty1.jpg{{# } }} '">-->
                          </a>
                          <p class="layim-msgbox-user">
                            <a href="javascript:void(0);" target="_blank"><b>{{ item.groupname||'' }}</b></a>
                            <span>{{ item.sendTime }}</span>
                          </p>
                          <p class="layim-msgbox-content">
                            {{# if(item.msgType == 1){ }}
                              申请添加 <b>{{ item.toname }}</b> 为好友
                            {{# }else{ }}
                            申请加入该群
                            {{# } }}
                            <span>{{ item.remark ? '附言: '+item.remark : '' }}</span>
                          </p>
                          <p class="layim-msgbox-btn">
                            等待验证
                          </p>
                        </li>
                    {{# }else{ }}
                        <li data-uid="{{ item.fromUser }}" data-id="{{ item.msgIdx }}" data-type="{{item.msgType}}" data-name="{{ item.fromname }}" {{# if(item.groupIdx){ }} data-groupidx="{{ item.groupIdx||'' }}"  data-group="{{ item.groupname||'' }}" {{#} }} data-signature="{{ item.signature }}">
                          <a href="javascript:void(0);" target="_blank">
                            <img src="http://test.guoshanchina.com/uploads/person/{{ item.fromUser }}.jpg" class="layui-circle layim-msgbox-avatar img0" >
                              <!--<img src="http://test.guoshanchina.com/uploads/person/{{ item.fromUser }}.jpg" class="layui-circle layim-msgbox-avatar" onerror="javascript:this.src='../../../../../../uploads/person/{{# if(item.msgType == 1){ }}empty2.jpg{{# }else{ }}empty1.jpg{{# } }} '">-->
                          </a>
                          <p class="layim-msgbox-user">
                            <a href="javascript:void(0);" target="_blank"><b>{{ item.fromname||'' }}</b></a>
                            <span>{{ item.sendTime }}</span>
                          </p>
                          <p class="layim-msgbox-content">
                            {{# if(item.msgType == 1){ }}
                            申请添加你为好友
                            {{# }else{ }}
                            申请加入 {{ item.groupname||'' }} 群
                            {{# } }}
                            <span>{{ item.remark ? '附言: '+item.remark : '' }}</span>
                          </p>
                          <p class="layim-msgbox-btn">
                            <button class="layui-btn layui-btn-small" data-type="agree">同意</button>
                            <button class="layui-btn layui-btn-small layui-btn-primary" data-type="refuse">拒绝</button>
                          </p>
                        </li>
                    {{# } }}

                {{# } else if(item.msgType == 2) { }}
                    {{# if(item.fromUser == d.id){ }}
                        <li class="layim-msgbox-system">
                            <p><em>系统：</em><b>{{ item.toname }}</b>
                            {{# if(item.multiAct0 == 1){ }}
                            已同意你的好友申请 <button class="layui-btn layui-btn-xs btncolor chat" data-name="{{ item.toname }}" data-chattype="friend" data-type="chat" data-uid="{{item.toId}}">发起会话</button>
                            {{# }else{ }}
                            已拒绝你的好友申请
                            {{# } }}
                            <span>{{ item.readTime }}</span></p>
                        </li>
                    {{# }else{ }}
                        <li>
                          <a href="javascript:void(0);" target="_blank">
                            <!--<img src="http://test.guoshanchina.com/uploads/person/{{ item.fromUser }}.jpg" class="layui-circle layim-msgbox-avatar" onerror="javascript:this.src='../../../../../../uploads/person/empty2.jpg'">-->
                            <img src="http://test.guoshanchina.com/uploads/person/{{ item.fromUser }}.jpg" class="layui-circle layim-msgbox-avatar img0" >
                          </a>
                          <p class="layim-msgbox-user">
                            <a href="javascript:void(0);" target="_blank"><b>{{ item.fromname||'' }}</b></a>
                            <span>{{ item.sendTime }}</span>
                          </p>
                          <p class="layim-msgbox-content">
                            申请添加你为好友
                            <span>{{ item.remark ? '附言: '+item.remark : '' }}</span>
                            {{# if(item.multiAct0 == 1){ }}
                            <button class="layui-btn layui-btn-xs btncolor chat" data-name="{{ item.fromname }}" data-chattype="friend" data-type="chat" data-uid="{{item.fromUser}}">发起会话</button>
                            {{# } }}

                          </p>
                          <p class="layim-msgbox-btn">
                            {{# if(item.multiAct0 == 1){ }}
                            已同意
                            {{# }else{ }}
                            已拒绝
                            {{# } }}

                          </p>
                        </li>

                    {{# } }}

                {{# }else if(item.msgType == 4){ }}
                    {{# if(item.fromUser == d.id){ }}
                        <li class="layim-msgbox-system">
                            <p><em>系统：</em> 管理员 {{ item.handle }}
                            {{# if(item.multiAct0 == 1){ }}
                            已同意你加入群 <b>{{ item.groupname }}</b> <button class="layui-btn layui-btn-xs btncolor chat" data-name="{{ item.groupname }}" data-chattype="group" data-type="chat" data-uid="{{item.toId}}">发起会话</button>
                            {{# }else{ }}
                            已拒绝你加入群 <b>{{ item.groupname }}</b>
                            {{# } }}
                            <span>{{ item.readTime }}</span></p>
                        </li>
                    {{# }else{ }}
                        <li class="layim-msgbox-system">
                            <p><em>系统：</em>
                            管理员{{ item.handle }}
                            {{# if(item.multiAct0 == 1){ }}
                            已同意 <b>{{ item.fromname }}</b> 加入群 <b>{{ item.groupname }}</b>
                            {{# }else{ }}
                            你已拒绝 <b>{{ item.fromname }}</b> 加入群 <b>{{ item.groupname }}</b>
                            {{# } }}
                            <span>{{ item.readTime }}</span></p>
                        </li>
                    {{# } }}

                {{# }
            }); }}
</textarea>

<!--
上述模版采用了 laytpl 语法，不了解的同学可以去看下文档：http://www.layui.com/doc/modules/laytpl.html
-->
<script type='text/javascript' src='../res.layui.com/mods/webim.config.js'></script>
<script type='text/javascript' src='../res.layui.com/mods/websdk.js'></script>
<script type='text/javascript' src='../res.layui.com/mods/strophe-1.2.8.min.js'></script>
<script src="../res.layui.com/layui/src/layui.js"></script>

<script>
    layui.config({
        base: '../res.layui.com/mods/'
    }).extend({
        socket: 'socket'
    });
    layui.use(['layim', 'flow','socket'], function (socket) {
        var layim = layui.layim, layer = layui.layer, laytpl = layui.laytpl, $ = layui.jquery, flow = layui.flow;

        var formatDate = function (now) {
            var myDate = new Date(now);
            var month=myDate.getMonth()+1;
            var date=myDate.getDate();
            return month+"月"+date+"日";
        }
        //请求消息
        var renderMsg = function (page, callback){
            //实际部署时，请将下述 getmsg.json 改为你的接口地址
            var cachedata = parent.layui.layim.cache();
            var url = cachedata.base.getMsgBox.url || {};
//            $.get(url, {page: page || 1}, function(data){
            $.get(url, function(data){
                var res = eval('(' + JSON.stringify(data) + ')');
                if(res.code != 0){
                    return layer.msg(res.msg);
                }
                layui.each(res.data.data, function(index, item){
                    res.data.data[index]['sendTime'] =  formatDate(item.sendTime);
                    res.data.data[index]['readTime'] =  formatDate(item.readTime);
                });
                var msg0 = res.data.data[0];
                var toId = msg0.toId;
                callback && callback(res.data.data, res.data.page,cachedata.mine.id);
            });
        };

        //消息信息流
        flow.load({
            elem: '#LAY_view' //流加载容器
            , isAuto: false
            , end: '<li class="layim-msgbox-tips">暂无更多新消息</li>'
            , done: function (page, next) { //加载下一页
                renderMsg(page, function (data, pages,id) {
                    var html = laytpl(LAY_tpl.value).render({
                        data: data
                        , page: page
                        , id: id
                    });
                    next(html, page < pages);
                });
            }
        });
        //操作
        var active = {
            IsExist: function (avatar){ //判断头像是否存在
                var ImgObj=new Image();
                ImgObj.src= avatar;
                if(ImgObj.fileSize > 0 || (ImgObj.width > 0 && ImgObj.height > 0))
                {
                    return true;
                } else {
                    return false;
                }
            },
            agree: function (othis) {
                parent.layui.im.receiveAddFriendGroup(othis,2);//type 1添加好友 3添加群
            }
            //拒绝
            , refuse: function (othis) {
                layer.confirm('确定拒绝吗？', function (index) {
                    parent.layui.im.receiveAddFriendGroup(othis,3);//type 1添加好友 3添加群
                });
            },chat: function(othis){//发起好友聊天
                var  uid = othis.data('uid'), avatar = "http://test.guoshanchina.com/uploads/person/"+uid+'.jpg';
                parent.layui.layim.chat({
                    name: othis.data('name')
                    ,type: othis.data('chattype')
                    ,avatar: avatar
                    ,id: uid
                });
            }

        };
        //打开页面即把系统消息标记为已读
        $(function(){
            $.get('../../../../../../webim/set_allread', {}, function (res) {
            });
        });
        $('body').on('click', '.layui-btn', function () {
            var othis = $(this), type = othis.data('type');
            active[type] ? active[type].call(this, othis) : '';
        });
        // layer.close(index);

    });
</script>
</body>
</html>
