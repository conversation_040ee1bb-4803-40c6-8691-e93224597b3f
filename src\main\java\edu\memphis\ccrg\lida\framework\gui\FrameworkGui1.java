/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/* To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/*
 * FrameworkGui.java
 *
 * Created on 12/07/2009, 08:47:40
 */
package edu.memphis.ccrg.lida.framework.gui;

import edu.memphis.ccrg.lida.framework.Agent;
import edu.memphis.ccrg.lida.framework.gui.events.FrameworkGuiEvent;
import edu.memphis.ccrg.lida.framework.gui.events.FrameworkGuiEventListener;
import edu.memphis.ccrg.lida.framework.gui.panels.AddEditPanel;
import edu.memphis.ccrg.lida.framework.gui.panels.GuiPanel;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.initialization.FrameworkGuiDef;
import edu.memphis.ccrg.lida.framework.initialization.FrameworkGuiPanelDef;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowEvent;
import java.util.List;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * The main GUI for the LIDA framework. A swing JFrame that is divide into four
 * regions. Each region can contain one or more {@link GuiPanel}s specified by
 * a config file. Panels can be assigned to one of the regions or can "float"
 * apart from the main JFrame. Implements the GUI part of the MVC pattern. <br/>
 * The format of the configuration file is as follows:<br/>
 * <br/>
 * #name = panel title, class name, Position [A,B,C,FLOAT, TOOL], tab order,
 * Refresh after load, [optional parameters]* <br/>
 * <br/>
 * <b>name</b> - panel name, must be unique<br/>
 * <b>panel title</b> - title of the panel as displayed in the GUI<br/>
 * <b>class name</b> - canonical name of the panel's java class, must implement
 * {@link GuiPanel}<br/>
 * <b>position</b> - location the panel will be placed in the main GUI window<br/>
 * <b>tab order</b> - if there are multiple panels assigned to a particular
 * region, this specifies the position this panel will appear in that region. <br/>
 * <b>refresh after load</b> - determines if the panel will be refreshed after
 * loading.<br/>
 * <b>optional parameters</b> - additional parameters may be defined, separated
 * by comma.
 * 
 * <AUTHOR> Snaider
 */
public class FrameworkGui1 extends javax.swing.JFrame implements FrameworkGuiEventListener {
    private static final long serialVersionUID = 1L;

    private static final Logger logger = Logger.getLogger(FrameworkGui1.class.getCanonicalName());
    //    private static int DEFAULT_GUI_EVENT_INTERVAL = 5;
    private final List<String> panelClassNames = new ArrayList<String>();
    private final List<GuiPanel> panels = new ArrayList<GuiPanel>();
    private final List<FrameworkGuiPanelDef> panelConfigs = new ArrayList<FrameworkGuiPanelDef>();
    private final List<Container> panelParents = new ArrayList<Container>();
    private Agent agent;
    private FrameworkGuiController controller;
    private javax.swing.JDialog addEditDialog;

    /**
     * Constructs a new FrameworkGui using the {@link Agent} object as the model and
     * {@link FrameworkGuiController} as the controller. Reads the {@link Properties}
     * file and creates and configures the {@link GuiPanel}s specified therein.
     *
     * @param a
     *            {@link Agent} the model.
     * @param cont
     *            {@link FrameworkGuiController} the controller
     * param prop
     *            {@link Properties} for configuration
     */
    public FrameworkGui1(Agent a, FrameworkGuiController cont, FrameworkGuiDef config) {
        initComponents();
        if (a == null) {
            logger.log(Level.WARNING, "Agent is null. Panels will not be loaded.", TaskManager.getCurrentTick());
        }else if (cont == null) {
            logger.log(Level.WARNING, "Controller is null. Panels will not be loaded.", TaskManager.getCurrentTick());
        }
        else if (config == null) {
            logger.log(Level.WARNING, "Configuration is null. Panels will not be loaded.", TaskManager.getCurrentTick());
        }
        else {
            agent = a;
            controller = cont;
            TaskManager tm = a.getTaskManager();
            tm.addFrameworkGuiEventListener(this);
            loadPanels(config);
        }
        pack();

        if (config != null) {
            if (config.getFrameLocation() != null)
                setLocation(config.getFrameLocation());
            if (config.getFrameSize() != null)
                setSize(config.getFrameSize());
            setExtendedState(config.getFrameExtendedState().id);
        }
    }

    /*
     * @param panelProp
     */
    private void loadPanels(FrameworkGuiDef config) {
        // Sort panels by position and tab order.
        List<FrameworkGuiPanelDef> panels = config.getPanels();

        Collections.sort(panels, new Comparator<FrameworkGuiPanelDef>() {
            @Override
            public int compare(FrameworkGuiPanelDef p1, FrameworkGuiPanelDef p2) {
                int result = p1.getPosition().compareTo(p2.getPosition());
                if (result == 0)
                    result = p1.getTabOrder() - p2.getTabOrder();
                return result;
            }
        });

        for (FrameworkGuiPanelDef panel : panels) {
            panelClassNames.add(panel.getClassName());
            createGuiPanel(panel);
        }
    }

    /**
     * Based on the specified parameters (from the configuration file), creates
     * a new {@link GuiPanel} and initializes it.
     *
     * param panelParams
     *            Parameters specified in 'configs.guiPanels.properties'
     */
    public void createGuiPanel(FrameworkGuiPanelDef config) {
        GuiPanel panel;
        try {
            panel = (GuiPanel) (Class.forName(config.getClassName())).newInstance();
        }
        catch (Exception e) {
            e.printStackTrace();
            logger.log(Level.WARNING, "Error instantiating panel {1} {2}",
                    new Object[] { 0L, config.getClassName(), e });
            return;
        }
        panelConfigs.add(config);
        panel.setName(config.getTitle());
        panel.registerAgent(agent);
        panel.registerGuiController(controller);

        // init panel with optional parameters
        try {
            panel.initPanel(config.getOptions());
        }
        catch (Exception e) {
            logger.log(Level.SEVERE, "Exception {1} encountered initializing panel {2}", new Object[] { 0L, e, panel });
            e.printStackTrace();//TODO print stack trace in logging panel also
//            logger.log(Level.SEVERE,"Exception Stack Trace",e);
        }

        // add the panel to the specified region of the GUI
        addGuiPanel(panel, config.getPosition());

        if (config.isRefreshAfterLoad()) {
            try {
                panel.refresh();
            }catch (Exception e) {
                logger.log(Level.SEVERE, "Exception {1} encountered when refreshing panel {2}", new Object[] { 0L, e,
                        panel });
                e.printStackTrace();//TODO print stack trace in logging panel also
//                logger.log(Level.SEVERE,"Exception Stack Trace",e);
            }
        }

        logger.log(Level.INFO, "GuiPanel added: {0}", panel.getName());
    }

    /*
     * Adds a Panel to the main GUI.
     *
     * @param panel
     *            the panel to add.
     * @param panelPosition
     *            Determines where the panel is going to be placed. <br/> A:
     *            Upper Left position. <br/> B: Upper Right position. In a new
     *            TAB<br/> C: Lower position. In a new TAB<br/> TOOL: In the
     *            ToolBox <br/> FLOAT: In a new frame. <br/>
     */
    private void addGuiPanel(GuiPanel panel, String panelPosition) {
        final JPanel jPanel = panel.getPanel();
        java.awt.Container parent = null;

        panels.add(panel);

        javax.swing.JMenu associatedMenu = areaOthersPanelsMenu;
        if ("A".equalsIgnoreCase(panelPosition)) {
            jTabbedPaneL.addTab(panel.getName(), jPanel);
            parent = jTabbedPaneL;
            associatedMenu = areaAPanelsMenu;
        } else if ("B".equalsIgnoreCase(panelPosition)) {
            jTabbedPaneR.addTab(panel.getName(), jPanel);
            parent = jTabbedPaneR;
            associatedMenu = areaBPanelsMenu;
        } else if ("C".equalsIgnoreCase(panelPosition)) {
            principalTabbedPanel.addTab(panel.getName(), jPanel);
            parent = principalTabbedPanel;
            associatedMenu = areaCPanelsMenu;
        } else if ("D".equalsIgnoreCase(panelPosition)) {
            tabbedPane1.addTab(panel.getName(), jPanel);
            parent = tabbedPane1;
            associatedMenu = areaDPanelsMenu;
        } else if ("E".equalsIgnoreCase(panelPosition)) {
            chatPanel.addTab(panel.getName(), jPanel);
            parent = chatPanel;
            associatedMenu = areaEPanelsMenu;
        } else if ("FLOAT".equalsIgnoreCase(panelPosition)) {
            JFrame dialog = new JFrame(panel.getName());
            dialog.add(jPanel);
            dialog.pack();
            dialog.setAlwaysOnTop(false);
            dialog.setResizable(true);
            dialog.setVisible(true);
            dialog.setDefaultCloseOperation(javax.swing.WindowConstants.HIDE_ON_CLOSE);
            dialog.addWindowListener(new java.awt.event.WindowAdapter() {

                int index = panels.size() - 1;

                @Override
                public void windowClosing(WindowEvent winEvt) {
                    for (java.awt.Component firstLevelMenu : panelsMenu.getMenuComponents()) {
                        if (firstLevelMenu instanceof javax.swing.JMenu) {
                            for (java.awt.Component secondLevelMenu : ((javax.swing.JMenu) firstLevelMenu)
                                    .getMenuComponents()) {
                                if (secondLevelMenu instanceof javax.swing.JMenu) {
                                    String menuText = ((javax.swing.JMenu) secondLevelMenu).getText();
                                    String panelName = panels.get(index).getName();
                                    if (menuText.equals(panelName)) {
                                        ((javax.swing.JCheckBoxMenuItem) ((javax.swing.JMenu) secondLevelMenu)
                                                .getMenuComponent(0)).setSelected(false);
                                    }
                                    // ((javax.swing.JMenu)firstLevelMenu).remove(secondLevelMenu);
                                }
                            }
                        }
                    }
                }
            });

        } else if ("TOOL".equalsIgnoreCase(panelPosition)) {
            getContentPane().add(jPanel, java.awt.BorderLayout.PAGE_START);
            parent = getContentPane();
        } else {
            logger.log(Level.WARNING, "Position error for panel "
                    + panel.getName() + " pos:" + panelPosition, 0L);
        }

        panelParents.add(parent);

        addToPanelsMenu(panel, parent, associatedMenu);
    }

    private void addToPanelsMenu(final GuiPanel panel, final Container parent, JMenu associatedMenu) {
        final JPanel jPanel = panel.getPanel();
        JMenu cMenu;
        String menuItemLabel;
        int unnamedIndex = 0;

        menuItemLabel = panel.getName();
        if (menuItemLabel.equals("")) {
            menuItemLabel = "Unnamed" + (unnamedIndex++);
        }
        cMenu = new JMenu();
        cMenu.setText(menuItemLabel);

        JCheckBoxMenuItem showItem = new JCheckBoxMenuItem();
        showItem.setText("Show Panel");
        showItem.setSelected(true);
        showItem.addActionListener(new ActionListener() {

            private final GuiPanel cGuiPanel = panel;
            private final JPanel cjPanel = jPanel;
            private final Container cParent = parent;
            private final FrameworkGuiPanelDef cConfig = panelConfigs.get(panels.indexOf(panel));

            @Override
            public void actionPerformed(ActionEvent evt) {
                togglePanel();
            }

            private void togglePanel() {
                int index = -1;
                if (parent != null) {
                    // normal panel (not FLOAT)
                    for (java.awt.Component c : parent.getComponents()) {
                        if (c.equals(cjPanel)) {
                            for (int i = 0; i < panels.size(); i++) {
                                if (panels.get(i).getPanel().equals(cjPanel)) {
                                    index = i;
                                    break;
                                }
                            }
                            break;
                        }
                    }

                    if (index > -1) {
                        // panel found, remove it
                        removePanelAt(index, false);
                    } else {
                        // panel not found, add it
                        cParent.add(cjPanel);
                        panels.add(cGuiPanel);
                        panelParents.add(cParent);
                        panelConfigs.add(cConfig);
                    }
                } else {
                    // dialog (FLOAT)
                    for (int i = 0; i < panels.size(); i++) {
                        if (panels.get(i).equals(cGuiPanel)) {
                            index = i;
                            break;
                        }
                    }

                    Container c = cjPanel.getParent();
                    while (c != null && !(c instanceof JFrame)) {
                        c = c.getParent();
                    }
                    if (c instanceof JFrame) {
                        if (c.isVisible()) {
                            ((JFrame) c).setVisible(false);
                        } else {
                            ((JFrame) c).setVisible(true);
                        }
                    }
                }
            }
        });
        cMenu.add(showItem);

//        cMenu.add(new JPopupMenu.Separator());
//        JMenuItem editItem = new JMenuItem();
//        editItem.setText("Edit Panel");
//        final String[] panelParams = panelParameters.get(panels.indexOf(panel));
//        editItem.addActionListener(new ActionListener() {
//
//            @Override
//            public void actionPerformed(ActionEvent evt) {
//                showEditPanelDialog(panelParams);
//            }
//        });
//        cMenu.add(editItem);

        associatedMenu.add(cMenu);
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {
        menuBar = new JMenuBar();
        fileMenu = new JMenu();
        guiRefreshRateMunuItem = new JMenuItem();
        exitMenuItem = new JMenuItem();
        panelsMenu = new JMenu();
        addPanelMenuItem = new JMenuItem();
        areaAPanelsMenu = new JMenu();
        areaBPanelsMenu = new JMenu();
        areaCPanelsMenu = new JMenu();
        areaDPanelsMenu = new JMenu();
        areaEPanelsMenu = new JMenu();
        areaOthersPanelsMenu = new JMenu();
        helpMenu = new JMenu();
        contentsMenuItem = new JMenuItem();
        aboutMenuItem = new JMenuItem();
        jSplitPane1 = new JSplitPane();
        jSplitPane2 = new JSplitPane();
        jTabbedPaneL = new JTabbedPane();
        splitPane3 = new JSplitPane();
        jTabbedPaneR = new JTabbedPane();
        tabbedPane1 = new JTabbedPane();
        splitPane1 = new JSplitPane();
        chatPanel = new JTabbedPane();
        principalTabbedPanel = new JTabbedPane();

        //======== this ========
        this.setExtendedState(MAXIMIZED_BOTH);
        setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);
        setTitle("LIDA Framework");
        setMinimumSize(new Dimension(200, 100));
        Container contentPane = getContentPane();
        contentPane.setLayout(new BorderLayout());

        //======== menuBar ========
        {
            menuBar.setPreferredSize(new Dimension(500, 21));

            //======== fileMenu ========
            {
                fileMenu.setText("File");

                //---- guiRefreshRateMunuItem ----
                guiRefreshRateMunuItem.setText("Gui Refresh Rate...");
                guiRefreshRateMunuItem.addActionListener(e -> guiRefreshRateMunuItemActionPerformed(e));
                fileMenu.add(guiRefreshRateMunuItem);

                //---- exitMenuItem ----
                exitMenuItem.setText("Exit");
                exitMenuItem.addActionListener(e -> exitMenuItemActionPerformed(e));
                fileMenu.add(exitMenuItem);
            }
            menuBar.add(fileMenu);

            //======== panelsMenu ========
            {
                panelsMenu.setText("Panels");

                //---- addPanelMenuItem ----
                addPanelMenuItem.setText("Add new panel");
                addPanelMenuItem.addActionListener(e -> addPanelMenuItemActionPerformed(e));
                panelsMenu.add(addPanelMenuItem);
                panelsMenu.addSeparator();

                //======== areaAPanelsMenu ========
                {
                    areaAPanelsMenu.setText("Area A");
                }
                panelsMenu.add(areaAPanelsMenu);

                //======== areaBPanelsMenu ========
                {
                    areaBPanelsMenu.setText("Area B");
                }
                panelsMenu.add(areaBPanelsMenu);

                //======== areaCPanelsMenu ========
                {
                    areaCPanelsMenu.setText("Area C");
                }
                panelsMenu.add(areaCPanelsMenu);

                //======== areaDPanelsMenu ========
                {
                    areaDPanelsMenu.setText("Area D");
                }
                panelsMenu.add(areaDPanelsMenu);

                //======== areaEPanelsMenu ========
                {
                    areaEPanelsMenu.setText("Area E");
                }
                panelsMenu.add(areaEPanelsMenu);

                //======== areaOthersPanelsMenu ========
                {
                    areaOthersPanelsMenu.setText("Others");
                }
                panelsMenu.add(areaOthersPanelsMenu);
                panelsMenu.addSeparator();
            }
            menuBar.add(panelsMenu);

            //======== helpMenu ========
            {
                helpMenu.setText("Help");

                //---- contentsMenuItem ----
                contentsMenuItem.setText("Help");
                contentsMenuItem.addActionListener(e -> contentsMenuItemActionPerformed(e));
                helpMenu.add(contentsMenuItem);

                //---- aboutMenuItem ----
                aboutMenuItem.setText("About");
                aboutMenuItem.addActionListener(e -> aboutMenuItemActionPerformed(e));
                helpMenu.add(aboutMenuItem);
            }
            menuBar.add(helpMenu);
        }
        setJMenuBar(menuBar);

        //======== jSplitPane1 ========
        {
            jSplitPane1.setDividerLocation(GraphicsEnvironment.getLocalGraphicsEnvironment().getMaximumWindowBounds().height/2);
            jSplitPane1.setOrientation(JSplitPane.VERTICAL_SPLIT);

            //======== jSplitPane2 ========
            {
                jSplitPane2.setDividerLocation(GraphicsEnvironment.getLocalGraphicsEnvironment().getMaximumWindowBounds().width/2);
                jSplitPane2.setMinimumSize(new Dimension(5, 5));
                jSplitPane2.setPreferredSize(new Dimension(600, 300));

                //======== jTabbedPaneL ========
                {
                    jTabbedPaneL.setPreferredSize(new Dimension(300, 100));
                }
                jSplitPane2.setLeftComponent(jTabbedPaneL);

                //======== splitPane3 ========
                {

                    //======== jTabbedPaneR ========
                    {
                        jTabbedPaneR.setPreferredSize(new Dimension(150, 100));
                    }
                    splitPane3.setLeftComponent(jTabbedPaneR);
                    splitPane3.setRightComponent(tabbedPane1);
                }
                jSplitPane2.setRightComponent(splitPane3);
            }
            jSplitPane1.add(jSplitPane2, "top");

            //======== splitPane1 ========
            {
                splitPane1.setLeftComponent(chatPanel);

                //======== principalTabbedPanel ========
                {
                    principalTabbedPanel.setPreferredSize(new Dimension(350, 150));
                    principalTabbedPanel.setMinimumSize(new Dimension(250, 90));
                }
                splitPane1.setRightComponent(principalTabbedPanel);
            }
            jSplitPane1.setBottomComponent(splitPane1);
        }
        contentPane.add(jSplitPane1, BorderLayout.CENTER);
        setSize(745, 445);
        setLocationRelativeTo(null);
    }// </editor-fold>//GEN-END:initComponents

    private void aboutMenuItemActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_aboutMenuItemActionPerformed
        JOptionPane.showMessageDialog(this, "LIDA Framework 1.1 \n "
                + "Cognitive Computing Research Group \n The University of Memphis \n"
                + " ccrg.cs.memphis.edu", "About", JOptionPane.INFORMATION_MESSAGE);
    }//GEN-LAST:event_aboutMenuItemActionPerformed

    private void contentsMenuItemActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_contentsMenuItemActionPerformed
        JOptionPane.showMessageDialog(this, "For more on the LIDA framework visit:\n"
                + "ccrg.cs.memphis.edu/framework.html", "Help", JOptionPane.INFORMATION_MESSAGE);
    }//GEN-LAST:event_contentsMenuItemActionPerformed

    private void guiRefreshRateMunuItemActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_guiRefreshRateMunuItemActionPerformed
        TaskManager tm = agent.getTaskManager();
        int guiRefreshInterval = tm.getGuiEventsInterval();
        String sticks = JOptionPane.showInputDialog("Enter a new gui refresh rate (zero is no refresh):", guiRefreshInterval);
        if (sticks != null) {
            try {
                guiRefreshInterval = Integer.parseInt(sticks);
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(this, "Error in the new Gui refresh interval. must be a positive int or zero.", "Error", JOptionPane.ERROR_MESSAGE);
            }
            if (guiRefreshInterval < 0) {
                JOptionPane.showMessageDialog(this, "Error in the new Gui refresh interval. must be a positive int or zero.", "Error", JOptionPane.ERROR_MESSAGE);
            } else {
                tm.setGuiEventsInterval(guiRefreshInterval);
            }
        }
    }//GEN-LAST:event_guiRefreshRateMunuItemActionPerformed

    @SuppressWarnings("unused")
    private void saveMenuItemActionPerformed(java.awt.event.ActionEvent evt) {
    }

    @SuppressWarnings("unused")
    private void openMenuItemActionPerformed(java.awt.event.ActionEvent evt) {
    }

    @SuppressWarnings("unused")
    private void saveAsMenuItemActionPerformed(java.awt.event.ActionEvent evt) {
    }

    private void addPanelMenuItemActionPerformed(java.awt.event.ActionEvent evt) {
        showAddPanelDialog();
    }

    private void showAddPanelDialog() {
        final AddEditPanel addEditPanel = new AddEditPanel();
        addEditPanel.setName("AddPanel");
        addEditPanel.registerAgent(agent);
        addEditPanel.registerGuiController(this.controller);
        addEditPanel.initClassnames(panelClassNames);
        if (addEditDialog != null) {
            addEditDialog.setVisible(false);
        }
        addEditDialog = new JDialog();
        addEditDialog.add(addEditPanel.getPanel());
        addEditDialog.pack();
        addEditDialog.setVisible(true);

        final javax.swing.JPanel jpanel = addEditPanel.getPanel();
        for (java.awt.Component c : jpanel.getComponents()) {
            if (c instanceof javax.swing.JButton) {
                ((javax.swing.JButton) c).addActionListener(new java.awt.event.ActionListener() {

                    @Override
                    public void actionPerformed(
                            java.awt.event.ActionEvent evt) {
                        createGuiPanel(addEditPanel.getPanelConfig());
                        addEditDialog.setVisible(false);
                    }
                });
            }
        }

        addEditPanel.refresh();
    }

    @SuppressWarnings("unused")
	private void loadPanelSettingsMenuItemActionPerformed(java.awt.event.ActionEvent evt) {
        javax.swing.JFileChooser fc = new javax.swing.JFileChooser(
                new java.io.File(AgentStarter.DEFAULT_PROPERTIES_PATH));
        fc.showOpenDialog(this);
        java.io.File file = fc.getSelectedFile();
        if (file != null) {
            loadPanelConfigFromFile(file.getPath());
        }
    }

    private void loadPanelConfigFromFile(String path) {
        FrameworkGuiDef config = FrameworkGuiFactory.loadConfig(path);
        if (config != null) {
            loadPanels(config);
            while (panels.size() > 0) {
                removePanelAt(0);
            }

            areaAPanelsMenu.removeAll();
            areaBPanelsMenu.removeAll();
            areaCPanelsMenu.removeAll();
            areaDPanelsMenu.removeAll();
            areaEPanelsMenu.removeAll();
            areaOthersPanelsMenu.removeAll();

            pack();
        }
    }

    //    @SuppressWarnings("unused")
    //    private void savePanelSettingsMenuItemActionPerformed(java.awt.event.ActionEvent evt) {
    //        javax.swing.JFileChooser fc = new javax.swing.JFileChooser(new java.io.File(
    //                AgentStarter.DEFAULT_PROPERTIES_PATH));
    //        fc.showSaveDialog(this);
    //        java.io.File file = fc.getSelectedFile();
    //        if (file != null) {
    //            savePanelConfigToFile(file.getPath());
    //        }
    //    }
    //
    //    private void savePanelConfigToFile(String path) {
    //        FrameworkGuiFactory.savePanelConfigToFile(path, getBounds(), getExtendedState());
    //    }

    private void exitMenuItemActionPerformed(java.awt.event.ActionEvent evt) {
        controller.executeCommand("quitAll", null);
    }// GEN-LAST:event_exitMenuItemActionPerformed
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private JMenuBar menuBar;
    private JMenu fileMenu;
    private JMenuItem guiRefreshRateMunuItem;
    private JMenuItem exitMenuItem;
    private JMenu panelsMenu;
    private JMenuItem addPanelMenuItem;
    private JMenu areaAPanelsMenu;
    private JMenu areaBPanelsMenu;
    private JMenu areaCPanelsMenu;
    private JMenu areaDPanelsMenu;
    private JMenu areaEPanelsMenu;
    private JMenu areaOthersPanelsMenu;
    private JMenu helpMenu;
    private JMenuItem contentsMenuItem;
    private JMenuItem aboutMenuItem;
    private JSplitPane jSplitPane1;
    private JSplitPane jSplitPane2;
    private JTabbedPane jTabbedPaneL;
    private JSplitPane splitPane3;
    private JTabbedPane jTabbedPaneR;
    private JTabbedPane tabbedPane1;
    private JSplitPane splitPane1;
    private JTabbedPane chatPanel;
    private JTabbedPane principalTabbedPanel;
    // End of variables declaration//GEN-END:variables

//    private void showEditPanelDialog(final String[] panelParams) {
//        final AddEditPanel addEditPanel = new AddEditPanel();
//        addEditPanel.setName("EditPanel");
//        addEditPanel.registerAgent(agent);
//        addEditPanel.registerGuiController(this.controller);
//        addEditPanel.setPanelParams(panelParams);
//        if (addEditDialog != null) {
//            addEditDialog.setVisible(false);
//        }
//        addEditDialog = new javax.swing.JDialog();
//        addEditDialog.add(addEditPanel.getPanel());
//        addEditDialog.pack();
//        addEditDialog.setVisible(true);
//
//        final javax.swing.JPanel jpanel = addEditPanel.getPanel();
//        for (java.awt.Component c : jpanel.getComponents()) {
//            if (c instanceof javax.swing.JButton) {
//                ((javax.swing.JButton) c).addActionListener(new java.awt.event.ActionListener() {
//
//                    @Override
//                    public void actionPerformed(
//                            java.awt.event.ActionEvent evt) {
//                        String[] params = addEditPanel.getPanelParams();
//                        int index = -1;
//                        for (int i = 0; i < panels.size(); i++) {
//                            String className = panels.get(i).getClass().toString().replace("class ", "");
//                            if (panels.get(i).getName().equals(
//                                    params[PANEL_NAME])
//                                    && className.equals(params[CLASS_NAME])) {
//                                index = i;
//                                break;
//                            }
//                        }
//                        if (index >= 0) {
//                            removePanelAt(index);
//                        }
//
//                        createGuiPanel(addEditPanel.getPanelParams());
//                        addEditDialog.setVisible(false);
//                    }
//                });
//            }
//        }
//
//        addEditPanel.refresh();
//    }

    private void removePanelAt(int panelIndex) {
        removePanelAt(panelIndex, true);
    }

    private void removePanelAt(int panelIndex, boolean removeFromMenu) {
        if (removeFromMenu) {
            for (java.awt.Component firstLevelMenu : panelsMenu.getMenuComponents()) {
                if (firstLevelMenu instanceof javax.swing.JMenu) {
                    for (java.awt.Component secondLevelMenu : ((javax.swing.JMenu) firstLevelMenu).getMenuComponents()) {
                        if (secondLevelMenu instanceof javax.swing.JMenu) {
                            String menuText = ((javax.swing.JMenu) secondLevelMenu).getText();
                            String panelName = panels.get(panelIndex).getName();
                            if (menuText.equals(panelName)) {
                                ((javax.swing.JMenu) firstLevelMenu).remove(secondLevelMenu);
                            }
                        }
                    }
                }
            }
        }

        if (panelIndex > -1) {
            panelParents.get(panelIndex).remove(panels.get(panelIndex).getPanel());
            panelParents.get(panelIndex).repaint();
            panelParents.remove(panelIndex);
            panels.remove(panelIndex);
            panelConfigs.remove(panelIndex);
        }
    }

    @Override
    public void receiveFrameworkGuiEvent(FrameworkGuiEvent event) {
        SwingUtilities.invokeLater(new Runnable() {

            @Override
            public void run() {
                for (GuiPanel panel : panels) {
                    panel.refresh();
                }
            }
        });
    }
}
