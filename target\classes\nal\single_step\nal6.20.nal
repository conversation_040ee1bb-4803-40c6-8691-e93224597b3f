'********** multiple variables introduction

'Lock-1 can be opened by every key.
<<$x --> key> ==> <{lock1} --> (/,open,$x,_)>>. 

'Lock-1 is a lock.
<{lock1} --> lock>. 

166

'There is a lock that can be opened by every key.
''outputMustContain('(&&,<#1 --> lock>,<<$2 --> key> ==> <#1 --> (/,open,$2,_)>>). %1.00;0.81%')

'I guess every lock can be opened by every key.
''outputMustContain('<(&&,<$1 --> key>,<$2 --> lock>) ==> <$2 --> (/,open,$1,_)>>. %1.00;0.45%')
