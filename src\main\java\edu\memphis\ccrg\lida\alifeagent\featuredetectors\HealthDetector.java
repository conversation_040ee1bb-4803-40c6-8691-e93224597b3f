/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.alifeagent.featuredetectors;

import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.pam.PamLinkable;
import edu.memphis.ccrg.lida.pam.tasks.DetectionAlgorithm;
import edu.memphis.ccrg.lida.pam.tasks.MultipleDetectionAlgorithm;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;
import edu.memphis.ccrg.linars.Memory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

public class HealthDetector extends MultipleDetectionAlgorithm{

	private PamLinkable goodHealth;
	private PamLinkable fairHealth;
	private PamLinkable badHealth;
	private PamLinkable feel;
	private final String modality = "";
	private Map<String, Object> detectorParams = new HashMap<String, Object>();

	private NodeStructure nodeStructure = new NodeStructureImpl();
	
	@Override
	public void init(){
		super.init();
//		goodHealth = nodeStructure.getNeoNode("goodHealth");
//		fairHealth = nodeStructure.getNeoNode("fairHealth");
//		badHealth = nodeStructure.getNeoNode("badHealth");
//
//		feel = nodeStructure.getNeoNode("feel");

//		goodHealth = pamNodeMap.get("goodHealth");
//		fairHealth = pamNodeMap.get("fairHealth");
//		badHealth = pamNodeMap.get("badHealth");

		detectorParams.put("mode","health");
	}
	
	@Override
	public void detectLinkables() {
//		double healthValue = (Double)sensoryMemory.getSensoryContent(modality, detectorParams);

		sensorParam.put("mode","health");
		double healthValue = (Double)environment.getState(sensorParam);

		double amount;

//		if(healthValue > 0.99){
////			excite("feel", 0.55, "feel");
////			excite("goodHealth", 0.63, "feel");
//			excite("{SELF}", 0.55, "feel");
//			excite("[goodHealth]", 0.63, "feel");
//			// 取得系统时间，然后每隔十秒执行一次？
//
//
//			// 具体体感参数本是不能直接通达的，通达的都是强烈程度。然后可被语言描述，可细化
//			AgentStarter.nar.addInput("<{SELF} --> [goodHealth]>. :|:");
//			System.out.println("----------goodHealth-------------------");
//			AgentStarter.nar.cycles(1);
//
//		}else if(healthValue < 0.85){
//			amount = 0.5/healthValue;
//			excite("feel", amount, "feel");
//			excite("hungry", amount, "feel");
//			if (healthValue == 0.3) {

//			excite("[satisfied]", 0.63, "feel");
//			excite("{SELF}", 0.55, "feel");
//			excite("[goodHealth]", 0.63, "feel");
				// 省略体感，直接触发动机
//		AgentStarter.nar.addInput("<{SELF} --> [goodHealth]>! :|:");
//		AgentStarter.nar.cycles(1);
//		System.out.println("------------------------------------goodHealth-----------------------------------");
//		AgentStarter.nar.addInput("<{SELF} --> [satisfied]>! :|:");
//		AgentStarter.nar.cycles(1);
//		AgentStarter.nar.addInput("<SELF --> [happy]>! :|:");
//		AgentStarter.nar.cycles(1);

		WorkspaceBuffer buffer = (WorkspaceBuffer) ((WorkspaceImpl) pam.getListener()).getSubmodule(ModuleName.GoalGraph);
		NodeStructure nsmem = buffer.getBufferContent(null);

//		nar.addInputTo("<{SELF} --> [goodHealth]>! :|:", (Memory) nsmem);
//		nar.addInput("<{SELF} --> [goodHealth]>! :|:");
////		((Memory) nsmem).doGBuffer("goal");
//
//		nar.addInputTo("<{SELF} --> [satisfied]>! :|:", (Memory) nsmem);
//		nar.addInput("<{SELF} --> [satisfied]>! :|:");
////		((Memory) nsmem).doGBuffer("goal");
//
//		nar.addInputTo("<SELF --> [happy]>! :|:", (Memory) nsmem);
//		nar.addInput("<SELF --> [happy]>! :|:");
////		((Memory) nsmem).doGBuffer("goal");

//			}
//		}else {
//			excite("{SELF}", 0.55, "feel");
//			excite("[satisfied]", 0.63, "feel");
//			AgentStarter.nar.addInput("<{SELF} --> [satisfied]>! :|:");
//			AgentStarter.nar.cycles(1);
//		}
	}
}
