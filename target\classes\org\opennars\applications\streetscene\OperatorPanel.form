<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" value="Operator Panel"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jScrollPane2" alignment="1" max="32767" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jScrollPane3" alignment="1" max="32767" attributes="0"/>
                          <Group type="102" attributes="0">
                              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                              <EmptySpace pref="352" max="32767" attributes="0"/>
                              <Component id="jCheckBox2" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="jCheckBox1" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" attributes="0">
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                                  <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                              </Group>
                              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                          </Group>
                          <Component id="jScrollPane1" alignment="0" max="32767" attributes="0"/>
                          <Component id="jScrollPane4" alignment="1" max="32767" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jButton1" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Component id="jScrollPane5" alignment="1" max="32767" attributes="0"/>
                  <Group type="102" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="saveQANarButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="stopSaveQANarButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="saveLocationNarButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="stopSaveLocationNarButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="savePredictionNarButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="stopSavePredictionNarButton" min="-2" max="-2" attributes="0"/>
                              <EmptySpace type="unrelated" max="-2" attributes="0"/>
                              <Component id="logOutputCheckBox" min="-2" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                      <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jCheckBox1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jCheckBox2" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" max="-2" attributes="0">
                  <Group type="102" attributes="0">
                      <Component id="jScrollPane3" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jScrollPane4" min="-2" pref="40" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                      <EmptySpace type="unrelated" max="-2" attributes="0"/>
                      <Component id="jScrollPane1" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Component id="jButton1" max="32767" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jScrollPane2" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jScrollPane5" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="103" alignment="0" groupAlignment="3" attributes="0">
                      <Component id="savePredictionNarButton" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="stopSavePredictionNarButton" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="logOutputCheckBox" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="saveLocationNarButton" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="stopSaveLocationNarButton" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="saveQANarButton" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="stopSaveQANarButton" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace pref="23" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="jTextArea1">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="rows" type="int" value="5"/>
            <Property name="toolTipText" type="java.lang.String" value=""/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Background knowledge and motivations"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane2">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="jTextArea2">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="rows" type="int" value="5"/>
            <Property name="disabledTextColor" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="0" green="0" red="0" type="rgb"/>
            </Property>
            <Property name="enabled" type="boolean" value="false"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="Current answers"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="jButton1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Apply"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton1ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Current motivations"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane3">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="jTextArea3">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="rows" type="int" value="5"/>
            <Property name="toolTipText" type="java.lang.String" value=""/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JCheckBox" name="jCheckBox1">
      <Properties>
        <Property name="selected" type="boolean" value="true"/>
        <Property name="text" type="java.lang.String" value="Learn&Show predictions"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jCheckBox1ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="Current questions"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane4">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="jTextArea4">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="rows" type="int" value="5"/>
            <Property name="toolTipText" type="java.lang.String" value=""/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JCheckBox" name="jCheckBox2">
      <Properties>
        <Property name="selected" type="boolean" value="true"/>
        <Property name="text" type="java.lang.String" value="Allow relative location relations"/>
        <Property name="toolTipText" type="java.lang.String" value=""/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jCheckBox2ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="Current operator messages"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane5">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="jTextArea5">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="rows" type="int" value="5"/>
            <Property name="disabledTextColor" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="0" green="0" red="0" type="rgb"/>
            </Property>
            <Property name="enabled" type="boolean" value="false"/>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_VariableModifier" type="java.lang.Integer" value="1"/>
          </AuxValues>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JButton" name="saveQANarButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Save QANar experience"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="saveQANarButtonActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="stopSaveQANarButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Stop"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="stopSaveQANarButtonActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="stopSaveLocationNarButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Stop"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="stopSaveLocationNarButtonActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="saveLocationNarButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Save LocationNar experience"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="saveLocationNarButtonActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="stopSavePredictionNarButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Stop"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="stopSavePredictionNarButtonActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="savePredictionNarButton">
      <Properties>
        <Property name="text" type="java.lang.String" value="Save PredictionNar experience"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="savePredictionNarButtonActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JCheckBox" name="logOutputCheckBox">
      <Properties>
        <Property name="selected" type="boolean" value="true"/>
        <Property name="text" type="java.lang.String" value="Log input only"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
