package edu.memphis.ccrg.alife.gui;

import javax.swing.*;
import java.awt.*;

/* access modifiers changed from: private */
public class CellImagePanel extends JPanel {
    private Image cellImg;

    public CellImagePanel() {
    }

    /* access modifiers changed from: protected */
    public void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.setColor(Color.LIGHT_GRAY);
        g.fillRect(0, 0, getWidth(), getHeight());
        if (this.cellImg != null) {
            int size = getWidth() < getHeight() ? getWidth() : getHeight();
            g.drawImage(this.cellImg.getScaledInstance(size, size, 4), (getWidth() - size) / 2, (getHeight() - size) / 2, this);
        }
    }

    public void setCellImg(Image cellImg2) {
        this.cellImg = cellImg2;
        revalidate();
        repaint();
    }
}
