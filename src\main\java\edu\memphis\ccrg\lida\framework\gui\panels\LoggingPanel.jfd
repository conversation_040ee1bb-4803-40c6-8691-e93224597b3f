JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class java.awt.BorderLayout ) ) {
			name: "this"
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "jScrollPane1"
				add( new FormComponent( "javax.swing.JTextArea" ) {
					name: "loggingText"
					"columns": 20
					"rows": 5
				} )
			}, new FormLayoutConstraints( class java.lang.String ) {
				"value": "Center"
			} )
			add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
				"$horizontalGroup": "par l {seq t {space :p::p, comp jLabel2:::p::p, space :p::p, comp loggerComboBox::::260:x, space :p::p, comp jLabel1:::p::p, space :p::p, comp levelComboBox:::p:134:p, space :p::p, comp clearButton:::p::p, space :p:123:p}}"
				"$verticalGroup": "par l {seq l {space :::p, par b {comp jLabel1::b:p::p, comp clearButton::b:p::p, comp levelComboBox::b:p::p, comp jLabel2::b:p::p, comp loggerComboBox::b:p::p}, space :::x}}"
			} ) {
				name: "jPanel1"
				"minimumSize": new java.awt.Dimension( 100, 60 )
				add( new FormComponent( "javax.swing.JLabel" ) {
					name: "jLabel1"
					"font": new java.awt.Font( "Lucida Grande", 0, 14 )
					"text": "Logging level"
				} )
				add( new FormComponent( "javax.swing.JButton" ) {
					name: "clearButton"
					"text": "Clear log"
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "clearButtonActionPerformed", true ) )
				} )
				add( new FormComponent( "javax.swing.JComboBox" ) {
					name: "levelComboBox"
					"model": new javax.swing.DefaultComboBoxModel {
						selectedItem: "SEVERE"
						addElement( "SEVERE" )
						addElement( "WARNING" )
						addElement( "INFO" )
						addElement( "CONFIG" )
						addElement( "FINE" )
						addElement( "FINER" )
						addElement( "FINEST" )
						addElement( "ALL" )
						addElement( "OFF" )
					}
					auxiliary() {
						"JavaCodeGenerator.preInitCode": "${field}.setSelectedItem(\"INFO\");"
					}
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "levelComboBoxActionPerformed", true ) )
				} )
				add( new FormComponent( "javax.swing.JLabel" ) {
					name: "jLabel2"
					"font": new java.awt.Font( "Lucida Grande", 0, 14 )
					"text": "Logger:"
				} )
				add( new FormComponent( "javax.swing.JComboBox" ) {
					name: "loggerComboBox"
					auxiliary() {
						"JavaCodeGenerator.preInitCode": "${field}.setModel(new DefaultComboBoxModel());\n${field}.setSelectedItem(\"INFO\");"
					}
					addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "loggerComboBoxActionPerformed", true ) )
				} )
			}, new FormLayoutConstraints( class java.lang.String ) {
				"value": "First"
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 658, 300 )
		} )
	}
}
