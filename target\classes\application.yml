server:
  port0: 8080
spring:
  servlet:
    multipart:
#      max-file-size: 100Mb
#      maxRequestSize: 1000Mb
  thymeleaf:
    cache : false
    mode: HTML5
  neo4j:
      #url: bolt://localhost:7687  #3.x
      url: neo4j://localhost:7687 #4.x
      username: neo4j
      password: 123456
  datasource:
      url: **************************************************************
#    &allowPublicKeyRetrieval=true
      driverClassName: com.mysql.cj.jdbc.Driver
      username: root
      password: root
      type: com.zaxxer.hikari.HikariDataSource
mybatis:
  typeAliasesPackage: com.warmer.kgmaker.entity
  mapperLocations: classpath*:/mapping/*.xml
#配置分页插件pagehelper
pagehelper:
    helperDialect: mysql
    reasonable: true
    supportMethodsArguments: true
    params: count=countSql 
#--------七牛云配置
qiniu:
  access:
    key: 2J5BOKpbxDlzkYVYZ5dwGS3jAevVmOJwcL3fIdpw
  secret:
    key: 2LXAQbFFbFR_I76bseNEmu-Sjnh4RRaSRsazX5Dj
  bucket:
    name: nndt
    host:
      name: file.miaoleyan.com
  prefixName: /nndt
#--------七牛云配置
img:  #//如果是Windows情况下，格式是 D:\\blog\\image\\   linx 格式"/home/<USER>/image/";
   location : D:\\blog\\image\\
file:
  serverurl:  http://localhost:8089
  location : D:\\lida\\kgmaker-xr\\csv\\   #如果是Windows情况下，格式是 D:\\kgmanager\\csv\\   linx 格式"/home/<USER>/csv/";