<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
              <Component id="jCheckBox1" min="-2" max="-2" attributes="0"/>
          </Group>
          <Group type="102" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <Component id="jLabel2" max="32767" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" attributes="0">
                      <Component id="jTextField2" pref="474" max="32767" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jTextField1" min="-2" pref="176" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jTextField3" min="-2" pref="207" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jScrollPane2" alignment="0" min="-2" pref="273" max="-2" attributes="0"/>
                          <Component id="jLabel1" alignment="0" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="102" attributes="0">
                              <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                          </Group>
                          <Component id="jScrollPane1" max="32767" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jCheckBox1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="16" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jTextField1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField3" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="unrelated" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jScrollPane1" pref="363" max="32767" attributes="0"/>
                  <Component id="jScrollPane2" max="32767" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="5" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JTextField" name="jTextField1">
      <Properties>
        <Property name="text" type="java.lang.String" value="jTextField1"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField2">
      <Properties>
        <Property name="text" type="java.lang.String" value="jTextField1"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField3">
      <Properties>
        <Property name="text" type="java.lang.String" value="jTextField1"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="jTextArea1">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="rows" type="int" value="5"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Input that resulted"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane2">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTextArea" name="jTextArea2">
          <Properties>
            <Property name="columns" type="int" value="20"/>
            <Property name="rows" type="int" value="5"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="."/>
      </Properties>
    </Component>
    <Component class="javax.swing.JCheckBox" name="jCheckBox1">
      <Properties>
        <Property name="selected" type="boolean" value="true"/>
        <Property name="text" type="java.lang.String" value="Vary properties"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Event bag elements"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="."/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
