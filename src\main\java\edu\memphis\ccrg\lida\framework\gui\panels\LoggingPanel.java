/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package edu.memphis.ccrg.lida.framework.gui.panels;

import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import javax.swing.*;
import java.awt.*;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import java.util.logging.*;

/**
 * A {@link GuiPanel} to display the logs of the system.
 * 
 * Panel is initialized by loading all relevant Loggers which includes all those
 * in the framework (edu.memphis.ccrg.lida.*) and optionally those packages
 * specified by the parameters.
 * 
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 */
public class LoggingPanel extends GuiPanelImpl {

	private String frameworkPackageName = "edu.memphis.ccrg.lida";
	private Logger logger = Logger.getLogger(frameworkPackageName);

	/** Creates new form LogPanel */
	public LoggingPanel() {
		initComponents();
	}

	@Override
	public void initPanel(String[] params) {
		// TODO may be better way to handle null params
		GuiLogHandler handler = new GuiLogHandler();
		logger.addHandler(handler);
		String otherName = null;
		if (params != null) {
			// add handlers for parameter-specified packages
			for (int i = 0; i < params.length; i++) {
				otherName = params[i].trim();
				Logger otherLogger = Logger.getLogger(otherName);
				otherLogger.addHandler(handler);
			}
		}
		loggerComboBox.removeAllItems();
		loggerComboBox.addItem("GLOBAL");

		// Add only loggers to the panel which match frameworkPackageName or
		// one of the parameter-specified names
		List<String> loggerNamesToAdd = new ArrayList<String>();
		Enumeration<String> el = LogManager.getLogManager().getLoggerNames();
		while (el.hasMoreElements()) {
			String loggerName = el.nextElement();
			if (loggerName.regionMatches(0, frameworkPackageName, 0,
					frameworkPackageName.length())) {
				loggerNamesToAdd.add(loggerName);
			} else if (params != null) {
				for (int i = 0; i < params.length; i++) {
					otherName = params[i].trim();
					if (otherName != null
							&& (loggerName.regionMatches(0, otherName, 0,
									otherName.length()))) {
						loggerNamesToAdd.add(loggerName);
						break;
					}
				}
			}
		}
		// Sort names and add them to the combo box
		Collections.sort(loggerNamesToAdd);
		for (String n : loggerNamesToAdd) {
			loggerComboBox.addItem(n);
		}
		refresh();
	}


    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents
        jScrollPane1 = new JScrollPane();
        loggingText = new JTextArea();
        jPanel1 = new JPanel();
        jLabel1 = new JLabel();
        clearButton = new JButton();
        levelComboBox = new JComboBox<>();
        jLabel2 = new JLabel();
        loggerComboBox = new JComboBox();

        //======== this ========
        setLayout(new BorderLayout());

        //======== jScrollPane1 ========
        {

            //---- loggingText ----
            loggingText.setColumns(20);
            loggingText.setRows(5);
            jScrollPane1.setViewportView(loggingText);
        }
        add(jScrollPane1, BorderLayout.CENTER);

        //======== jPanel1 ========
        {
            jPanel1.setMinimumSize(new Dimension(100, 60));

            //---- jLabel1 ----
            jLabel1.setFont(new Font("Lucida Grande", Font.PLAIN, 14));
            jLabel1.setText("Logging level");

            //---- clearButton ----
            clearButton.setText("Clear log");
            clearButton.addActionListener(e -> clearButtonActionPerformed(e));

            //---- levelComboBox ----
            levelComboBox.setSelectedItem("INFO");
            levelComboBox.setModel(new DefaultComboBoxModel<>(new String[] {
                "SEVERE",
                "WARNING",
                "INFO",
                "CONFIG",
                "FINE",
                "FINER",
                "FINEST",
                "ALL",
                "OFF"
            }));
            levelComboBox.addActionListener(e -> levelComboBoxActionPerformed(e));

            //---- jLabel2 ----
            jLabel2.setFont(new Font("Lucida Grande", Font.PLAIN, 14));
            jLabel2.setText("Logger:");

            //---- loggerComboBox ----
            loggerComboBox.setModel(new DefaultComboBoxModel());
            loggerComboBox.setSelectedItem("INFO");
            loggerComboBox.addActionListener(e -> loggerComboBoxActionPerformed(e));

            GroupLayout jPanel1Layout = new GroupLayout(jPanel1);
            jPanel1.setLayout(jPanel1Layout);
            jPanel1Layout.setHorizontalGroup(
                jPanel1Layout.createParallelGroup()
                    .addGroup(GroupLayout.Alignment.TRAILING, jPanel1Layout.createSequentialGroup()
                        .addContainerGap()
                        .addComponent(jLabel2)
                        .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(loggerComboBox, GroupLayout.DEFAULT_SIZE, 260, Short.MAX_VALUE)
                        .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(jLabel1)
                        .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(levelComboBox, GroupLayout.PREFERRED_SIZE, 134, GroupLayout.PREFERRED_SIZE)
                        .addPreferredGap(LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(clearButton)
                        .addGap(123, 123, 123))
            );
            jPanel1Layout.setVerticalGroup(
                jPanel1Layout.createParallelGroup()
                    .addGroup(jPanel1Layout.createSequentialGroup()
                        .addContainerGap()
                        .addGroup(jPanel1Layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                            .addComponent(jLabel1)
                            .addComponent(clearButton)
                            .addComponent(levelComboBox, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
                            .addComponent(jLabel2)
                            .addComponent(loggerComboBox, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
                        .addContainerGap(GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
            );
        }
        add(jPanel1, BorderLayout.PAGE_START);
        // JFormDesigner - End of component initialization  //GEN-END:initComponents
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables
    private JScrollPane jScrollPane1;
    private JTextArea loggingText;
    private JPanel jPanel1;
    private JLabel jLabel1;
    private JButton clearButton;
    private JComboBox<String> levelComboBox;
    private JLabel jLabel2;
    private JComboBox loggerComboBox;
    // JFormDesigner - End of variables declaration  //GEN-END:variables

    // End of variables declaration//GEN-END:variables
    private void loggerComboBoxActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_loggerComboBoxActionPerformed
        String loggerN = loggerComboBox.getSelectedItem().toString();
        if (loggerN.equals("GLOBAL")) {
            loggerN = "";
        }

        String levelName;
        Level levelVal = Logger.getLogger(loggerN).getLevel();
        if (levelVal == null) {
            levelName = "<Parent Level>";
        } else {
            levelName = levelVal.toString();
        }

        levelComboBox.setSelectedItem(levelName);
    }// GEN-LAST:event_loggerComboBoxActionPerformed

    private void clearButtonActionPerformed(java.awt.event.ActionEvent evt) {
        loggingText.setText("");
    }

    private void levelComboBoxActionPerformed(java.awt.event.ActionEvent evt) {
        Level levelVal = null;
        if (levelComboBox.getSelectedIndex() != 0) {
            levelVal = Level.parse(levelComboBox.getSelectedItem().toString());
        }
        String loggerN = loggerComboBox.getSelectedItem().toString();
        if (loggerN.equals("GLOBAL")) {
            loggerN = "";
        }

        Logger.getLogger(loggerN).setLevel(levelVal);
    }


    @Override
    public void display(Object o) {
        if (o instanceof String) {
            String s = (String) o;
            loggingText.append(s);
        }
    }

    /*
     * Logger handler for this panel
     */
    private class GuiLogHandler extends Handler {

        /** Creates a new instance of GuiLogHandler */
        public GuiLogHandler() {
        }

        @Override
        public void publish(LogRecord logRecord) {
            String logMessages = "";
            // String dateString="";
            long actualTick = 0L;
            // String name;

            String message = logRecord.getMessage();
            if (message == null) {
                logger.log(Level.WARNING, "in Logging Panel message was null",
                        TaskManager.getCurrentTick());
            } else {
                MessageFormat mf = new MessageFormat(message);

                Object[] param = logRecord.getParameters();
                if (param != null && param[0] instanceof Long) {
                    actualTick = (Long) param[0];
                }
                logMessages = String.format(
                        "%010d :%010d :%-10s :%-60s \t-> %s %n", logRecord
                                .getSequenceNumber(), actualTick, logRecord
                                .getLevel(), logRecord.getLoggerName(), mf
                                .format(logRecord.getParameters()));
                //TODO log stack traces
//				Throwable e = logRecord.getThrown();
//				if (e != null) {
//					StackTraceElement[] stackt = e.getStackTrace();
//					for (int i = 0; i < stackt.length && i < 100; i++) {
//						logMessages += "\n" + stackt[i].toString();
//					}
//				}
                SwingUtilities.invokeLater(new UpdateLoggerPanel(logMessages));
            }
        }

        @Override
        public void flush() {
        }

        @Override
        public void close() {
        }
    }

    private class UpdateLoggerPanel implements Runnable {
        private String logMessages;

        public UpdateLoggerPanel(String logMessages) {
            this.logMessages = logMessages;
        }

        @Override
        public void run() {
            loggingText.append(logMessages);
            JScrollBar jsb = jScrollPane1.getVerticalScrollBar();
            jsb.setValue(jsb.getMaximum());
        }
    }
}
