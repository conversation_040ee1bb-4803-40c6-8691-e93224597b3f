package edu.memphis.ccrg.alife.gui;

import edu.memphis.ccrg.alife.elements.ALifeObject;

import javax.swing.table.AbstractTableModel;
import java.text.DecimalFormat;
import java.util.Arrays;

/* access modifiers changed from: private */
public class AttributesTableModel extends AbstractTableModel {
    private final String[] EMPTY_ARRAY = new String[0];
    private String[] attributeNames = new String[0];
    private String[] columnNames = {"attribute", "value"};
    private String[] defaultAttributes = {"id", "name", "size", "health", "container"};
    private DecimalFormat df = new DecimalFormat("0.0000");
    private ALifeObject selectedObject = null;

    public AttributesTableModel() {
    }

    public void setALifeObject(ALifeObject object) {
        if (object != null) {
            this.selectedObject = object;
            this.attributeNames = (String[]) this.selectedObject.getAttributes().keySet().toArray(this.EMPTY_ARRAY);
            Arrays.sort(this.attributeNames);
            String[] auxArray = new String[(this.attributeNames.length + this.defaultAttributes.length)];
            System.arraycopy(this.defaultAttributes, 0, auxArray, 0, this.defaultAttributes.length);
            System.arraycopy(this.attributeNames, 0, auxArray, this.defaultAttributes.length, this.attributeNames.length);
            this.attributeNames = auxArray;
            fireTableStructureChanged();
        }
    }

    public int getColumnCount() {
        return 2;
    }

    public int getRowCount() {
        if (this.selectedObject != null) {
            return this.attributeNames.length;
        }
        return 0;
    }

    public String getColumnName(int column) {
        if (column < this.columnNames.length) {
            return this.columnNames[column];
        }
        return "";
    }

    public Object getValueAt(int rowIndex, int columnIndex) {
        if (this.selectedObject == null) {
            return null;
        }
        if (columnIndex == 0) {
            return this.attributeNames[rowIndex];
        }
        switch (rowIndex) {
            case 0:
                return Integer.valueOf(this.selectedObject.getId());
            case 1:
                return this.selectedObject.getName();
            case 2:
                return Integer.valueOf(this.selectedObject.getSize());
            case 3:
                return this.df.format(this.selectedObject.getHealth());
            case 4:
                return this.selectedObject.getContainer();
            default:
                Object o = this.selectedObject.getAttribute(this.attributeNames[rowIndex]);
                if (o instanceof Double) {
                    return this.df.format(o);
                }
                return o;
        }
    }

    public void setValueAt(Object value, int row, int column) {
    }

    public boolean isCellEditable(int row, int column) {
        return false;
    }
}
