'********** temporal deduction/explification 

'Someone enter the room_101 after he open the door_101
<<(*, $x, room_101) --> enter> =\> <(*, $x, door_101) --> open>>. %0.9% 

'Someone open the door_101 after he hold the key_101
<<(*, $y, door_101) --> open> =\> <(*, $y, key_101) --> hold>>. %0.8% 

100

'If someone enter room_101, he should hold key_101 before
''outputMustContain('<<(*,$1,room_101) --> enter> =\> <(*,$1,key_101) --> hold>>. %0.72;0.58%')
'If someone hold key_101, he will enter room_101
''outputMustContain('<<(*,$1,key_101) --> hold> =/> <(*,$1,room_101) --> enter>>. %1.00;0.37%')
