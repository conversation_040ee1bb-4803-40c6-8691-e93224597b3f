/**
 * Created by Administrator on 2018/5/2.
 */

var WebIM = window.WebIM || {};
WebIM.connection = connection;
// WebIM.message = _msg.message;
/**
 * The connection class.
 * @constructor
 * @param {Object} options - 创建连接的初始化参数
 * @param {String} options.url - xmpp服务器的URL
 * @param {String} options.apiUrl - API服务器的URL
 * @param {Boolean} options.isHttpDNS - 防止域名劫持
 * @param {Boolean} options.isMultiLoginSessions - 为true时同一账户可以同时在多个Web页面登录（多标签登录，默认不开启，如有需要请联系商务），为false时同一账号只能在一个Web页面登录
 * @param {Boolean} options.https - 是否启用wss.
 * @param {Number} options.heartBeatWait - 发送心跳包的时间间隔（毫秒）
 * @param {Boolean} options.isAutoLogin - 登录成功后是否自动出席
 * @param {Number} options.autoReconnectNumMax - 掉线后重连的最大次数
 * @param {Number} options.autoReconnectInterval -  掉线后重连的间隔时间（毫秒）
 * @param {Boolean} options.isWindowSDK - 是否运行在WindowsSDK上
 * @param {Boolean} options.encrypt - 是否加密文本消息
 * @param {Boolean} options.delivery - 是否发送delivered ack
 * @returns {Class}  连接实例
 */

var connection = function connection(options) {
    if (!this instanceof connection) {
        return new connection(options);
    }
    var options = options || {};

    this.isHttpDNS = options.isHttpDNS || false;
    this.isMultiLoginSessions = options.isMultiLoginSessions || false;
    this.wait = options.wait || 30;
    this.hold = options.hold || 1;
    this.retry = options.retry || false;
    this.https = options.https || location.protocol === 'https:';

    // this.url = _getXmppUrl(options.url, this.https);
    this.url = options.url || null;

    this.route = options.route || null;
    this.domain = options.domain || 'easemob.com';
    this.inactivity = options.inactivity || 30;
    this.heartBeatWait = options.heartBeatWait || 4500;
    this.maxRetries = options.maxRetries || 5;
    this.isAutoLogin = options.isAutoLogin === false ? false : true;
    this.pollingTime = options.pollingTime || 800;
    this.stropheConn = false;
    this.autoReconnectNumMax = options.autoReconnectNumMax || 0;
    this.autoReconnectNumTotal = 0;
    this.autoReconnectInterval = options.autoReconnectInterval || 0;

    this.context = options.context || null;
    // this.context = { status: _code.STATUS_INIT };

    // this.sendQueue = new Queue(); //instead of sending message immediately,cache them in this queue
    this.intervalId = null; //clearInterval return value
    this.apiUrl = options.apiUrl || '';
    this.isWindowSDK = options.isWindowSDK || false;
    this.encrypt = options.encrypt || { encrypt: { type: 'none' } };
    this.delivery = options.delivery || false;
    this.saveLocal = options.saveLocal || false;
    this.user = '';
    this.orgName = '';
    this.appName = '';
    this.token = '';
    this.unSendMsgArr = [];
    this.offLineSendConnecting = false;
    this.logOut = false;

    this.dnsArr = ['https://rs.easemob.com', 'https://rsbak.easemob.com', 'http://*************', 'http://**************']; //http dns server hosts
    this.dnsIndex = 0; //the dns ip used in dnsArr currently
    this.dnsTotal = this.dnsArr.length; //max number of getting dns retries
    this.restHosts = null; //rest server ips
    this.restIndex = 0; //the rest ip used in restHosts currently
    this.restTotal = 0; //max number of getting rest token retries
    this.xmppHosts = null; //xmpp server ips
    this.xmppIndex = 0; //the xmpp ip used in xmppHosts currently
    this.xmppTotal = 0; //max number of creating xmpp server connection(ws/bosh) retries

    this.groupOption = {};
};

/**
 * 注册监听函数
 * @param {Object} options - 回调函数集合
 * @param {connection~onOpened} options.onOpened - 处理登录的回调
 * @param {connection~onTextMessage} options.onTextMessage - 处理文本消息的回调
 * @param {connection~onEmojiMessage} options.onEmojiMessage - 处理表情消息的回调
 * @param {connection~onPictureMessage} options.onPictureMessage - 处理图片消息的回调
 * @param {connection~onAudioMessage} options.onAudioMessage - 处理音频消息的回调
 * @param {connection~onVideoMessage} options.onVideoMessage - 处理视频消息的回调
 * @param {connection~onFileMessage} options.onFileMessage - 处理文件消息的回调
 * @param {connection~onLocationMessage} options.onLocationMessage - 处理位置消息的回调
 * @param {connection~onCmdMessage} options.onCmdMessage - 处理命令消息的回调
 * @param {connection~onPresence} options.onPresence - 处理Presence消息的回调
 * @param {connection~onError} options.onError - 处理错误消息的回调
 * @param {connection~onReceivedMessage} options.onReceivedMessage - 处理Received消息的回调
 * @param {connection~onInviteMessage} options.onInviteMessage - 处理邀请消息的回调
 * @param {connection~onDeliverdMessage} options.onDeliverdMessage - 处理Delivered ACK消息的回调
 * @param {connection~onReadMessage} options.onReadMessage - 处理Read ACK消息的回调
 * @param {connection~onMutedMessage} options.onMutedMessage - 处理禁言消息的回调
 * @param {connection~onOffline} options.onOffline - 处理断网的回调
 * @param {connection~onOnline} options.onOnline - 处理联网的回调
 * @param {connection~onCreateGroup} options.onCreateGroup - 处理创建群组的回调
 */
connection.prototype.listen = function (options) {
    /**
     * 登录成功后调用
     * @callback connection~onOpened
     */
    /**
     * 收到文本消息
     * @callback connection~onTextMessage
     */
    /**
     * 收到表情消息
     * @callback connection~onEmojiMessage
     */
    /**
     * 收到图片消息
     * @callback connection~onPictureMessage
     */
    /**
     * 收到音频消息
     * @callback connection~onAudioMessage
     */
    /**
     * 收到视频消息
     * @callback connection~onVideoMessage
     */
    /**
     * 收到文件消息
     * @callback connection~onFileMessage
     */
    /**
     * 收到位置消息
     * @callback connection~onLocationMessage
     */
    /**
     * 收到命令消息
     * @callback connection~onCmdMessage
     */
    /**
     * 收到错误消息
     * @callback connection~onError
     */
    /**
     * 收到Presence消息
     * @callback connection~onPresence
     */
    /**
     * 收到Received消息
     * @callback connection~onReceivedMessage
     */
    /**
     * 被邀请进群
     * @callback connection~onInviteMessage
     */
    /**
     * 收到已送达回执
     * @callback connection~onDeliverdMessage
     */
    /**
     * 收到已读回执
     * @callback connection~onReadMessage
     */
    /**
     * 被群管理员禁言
     * @callback connection~onMutedMessage
     */
    /**
     * 浏览器被断网时调用
     * @callback connection~onOffline
     */
    /**
     * 浏览器联网时调用
     * @callback connection~onOnline
     */
    /**
     * 建群成功后调用
     * @callback connection~onCreateGroup
     */
    this.onOpened = options.onOpened || utils.emptyfn;
    this.onClosed = options.onClosed || utils.emptyfn;
    this.onTextMessage = options.onTextMessage || utils.emptyfn;
    this.onEmojiMessage = options.onEmojiMessage || utils.emptyfn;
    this.onPictureMessage = options.onPictureMessage || utils.emptyfn;
    this.onAudioMessage = options.onAudioMessage || utils.emptyfn;
    this.onVideoMessage = options.onVideoMessage || utils.emptyfn;
    this.onFileMessage = options.onFileMessage || utils.emptyfn;
    this.onLocationMessage = options.onLocationMessage || utils.emptyfn;
    this.onCmdMessage = options.onCmdMessage || utils.emptyfn;
    this.onPresence = options.onPresence || utils.emptyfn;
    this.onRoster = options.onRoster || utils.emptyfn;
    this.onError = options.onError || utils.emptyfn;
    this.onReceivedMessage = options.onReceivedMessage || utils.emptyfn;
    this.onInviteMessage = options.onInviteMessage || utils.emptyfn;
    this.onDeliverdMessage = options.onDeliveredMessage || utils.emptyfn;
    this.onReadMessage = options.onReadMessage || utils.emptyfn;
    this.onMutedMessage = options.onMutedMessage || utils.emptyfn;
    this.onOffline = options.onOffline || utils.emptyfn;
    this.onOnline = options.onOnline || utils.emptyfn;
    this.onConfirmPop = options.onConfirmPop || utils.emptyfn;
    this.onCreateGroup = options.onCreateGroup || utils.emptyfn;
    //for WindowSDK start
    this.onUpdateMyGroupList = options.onUpdateMyGroupList || utils.emptyfn;
    this.onUpdateMyRoster = options.onUpdateMyRoster || utils.emptyfn;
    //for WindowSDK end
    this.onBlacklistUpdate = options.onBlacklistUpdate || utils.emptyfn;

    // _listenNetwork(this.onOnline, this.onOffline);
};

    var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) {
        return typeof obj;
    } : function (obj) {
        return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    };

    // var utils = utils;

    // (function () {

        var EMPTYFN = function EMPTYFN() {};
        // var _code = __webpack_require__(207).code;
        var WEBIM_FILESIZE_LIMIT = 10485760;

        var _createStandardXHR = function _createStandardXHR() {
            try {
                return new window.XMLHttpRequest();
            } catch (e) {
                return false;
            }
        };

        var _createActiveXHR = function _createActiveXHR() {
            try {
                return new window.ActiveXObject('Microsoft.XMLHTTP');
            } catch (e) {
                return false;
            }
        };

        var _xmlrequest = function _xmlrequest(crossDomain) {
            crossDomain = crossDomain || true;
            var temp = _createStandardXHR() || _createActiveXHR();

            if ('withCredentials' in temp) {
                return temp;
            }
            if (!crossDomain) {
                return temp;
            }
            if (typeof window.XDomainRequest === 'undefined') {
                return temp;
            }
            var xhr = new XDomainRequest();
            xhr.readyState = 0;
            xhr.status = 100;
            xhr.onreadystatechange = EMPTYFN;
            xhr.onload = function () {
                xhr.readyState = 4;
                xhr.status = 200;

                var xmlDoc = new ActiveXObject('Microsoft.XMLDOM');
                xmlDoc.async = 'false';
                xmlDoc.loadXML(xhr.responseText);
                xhr.responseXML = xmlDoc;
                xhr.response = xhr.responseText;
                xhr.onreadystatechange();
            };
            xhr.ontimeout = xhr.onerror = function () {
                xhr.readyState = 4;
                xhr.status = 500;
                xhr.onreadystatechange();
            };
            return xhr;
        };

        var _hasFlash = function () {
            if ('ActiveXObject' in window) {
                try {
                    return new ActiveXObject('ShockwaveFlash.ShockwaveFlash');
                } catch (ex) {
                    return 0;
                }
            } else {
                if (navigator.plugins && navigator.plugins.length > 0) {
                    return navigator.plugins['Shockwave Flash'];
                }
            }
            return 0;
        }();

        var _tmpUtilXHR = _xmlrequest(),
            _hasFormData = typeof FormData !== 'undefined',
            _hasBlob = typeof Blob !== 'undefined',
            _isCanSetRequestHeader = _tmpUtilXHR.setRequestHeader || false,
            _hasOverrideMimeType = _tmpUtilXHR.overrideMimeType || false,
            _isCanUploadFileAsync = _isCanSetRequestHeader && _hasFormData,
            _isCanUploadFile = _isCanUploadFileAsync || _hasFlash,
            _isCanDownLoadFile = _isCanSetRequestHeader && (_hasBlob || _hasOverrideMimeType);

        if (!Object.keys) {
            Object.keys = function () {
                'use strict';

                var hasOwnProperty = Object.prototype.hasOwnProperty,
                    hasDontEnumBug = !{toString: null}.propertyIsEnumerable('toString'),
                    dontEnums = ['toString', 'toLocaleString', 'valueOf', 'hasOwnProperty', 'isPrototypeOf', 'propertyIsEnumerable', 'constructor'],
                    dontEnumsLength = dontEnums.length;

                return function (obj) {
                    if ((typeof obj === 'undefined' ? 'undefined' : _typeof(obj)) !== 'object' && (typeof obj !== 'function' || obj === null)) {
                        throw new TypeError('Object.keys called on non-object');
                    }

                    var result = [],
                        prop,
                        i;

                    for (prop in obj) {
                        if (hasOwnProperty.call(obj, prop)) {
                            result.push(prop);
                        }
                    }

                    if (hasDontEnumBug) {
                        for (i = 0; i < dontEnumsLength; i++) {
                            if (hasOwnProperty.call(obj, dontEnums[i])) {
                                result.push(dontEnums[i]);
                            }
                        }
                    }
                    return result;
                };
            }();
        }

    var utils = {
        hasFormData: _hasFormData,

        hasBlob: _hasBlob,

        emptyfn: EMPTYFN,

        isCanSetRequestHeader: _isCanSetRequestHeader,

        hasOverrideMimeType: _hasOverrideMimeType,

        isCanUploadFileAsync: _isCanUploadFileAsync,

        isCanUploadFile: _isCanUploadFile,

        isCanDownLoadFile: _isCanDownLoadFile,

        isSupportWss: function () {
            var notSupportList = [
                //1: QQ browser X5 core
                /MQQBrowser[\/]5([.]\d+)?\sTBS/

                //2: etc.
                //...
            ];

            if (!window.WebSocket) {
                return false;
            }

            var ua = window.navigator.userAgent;
            for (var i = 0, l = notSupportList.length; i < l; i++) {
                if (notSupportList[i].test(ua)) {
                    return false;
                }
            }
            return true;
        }(),

        getIEVersion: function () {
            var ua = navigator.userAgent,
                matches,
                tridentMap = {'4': 8, '5': 9, '6': 10, '7': 11};

            matches = ua.match(/MSIE (\d+)/i);

            if (matches && matches[1]) {
                return +matches[1];
            }
            matches = ua.match(/Trident\/(\d+)/i);
            if (matches && matches[1]) {
                return tridentMap[matches[1]] || null;
            }
            return null;
        }(),

        stringify: function stringify(json) {
            if (typeof JSON !== 'undefined' && JSON.stringify) {
                return JSON.stringify(json);
            } else {
                var s = '',
                    arr = [];

                var iterate = function iterate(json) {
                    var isArr = false;

                    if (Object.prototype.toString.call(json) === '[object Array]') {
                        arr.push(']', '[');
                        isArr = true;
                    } else if (Object.prototype.toString.call(json) === '[object Object]') {
                        arr.push('}', '{');
                    }

                    for (var o in json) {
                        if (Object.prototype.toString.call(json[o]) === '[object Null]') {
                            json[o] = 'null';
                        } else if (Object.prototype.toString.call(json[o]) === '[object Undefined]') {
                            json[o] = 'undefined';
                        }

                        if (json[o] && _typeof(json[o]) === 'object') {
                            s += ',' + (isArr ? '' : '"' + o + '":' + (isArr ? '"' : '')) + iterate(json[o]) + '';
                        } else {
                            s += ',"' + (isArr ? '' : o + '":"') + json[o] + '"';
                        }
                    }

                    if (s != '') {
                        s = s.slice(1);
                    }

                    return arr.pop() + s + arr.pop();
                };
                return iterate(json);
            }
        },
      /**  login: function login(options) {
            var options = options || {};
            var suc = options.success || EMPTYFN;
            var err = options.error || EMPTYFN;

            var appKey = options.appKey || '';
            var devInfos = appKey.split('#');
            if (devInfos.length !== 2) {
                err({
                    type: _code.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR
                });
                return false;
            }

            var orgName = devInfos[0];
            var appName = devInfos[1];
            var https = https || options.https;
            var user = options.user || '';
            var pwd = options.pwd || '';

            var apiUrl = options.apiUrl;

            var loginJson = {
                grant_type: 'password',
                username: user,
                password: pwd,
                timestamp: +new Date()
            };
            var loginfo = utils.stringify(loginJson);

            var options = {
                url: apiUrl + '/' + orgName + '/' + appName + '/token',
                dataType: 'json',
                data: loginfo,
                success: suc,
                error: err
            };
            return utils.ajax(options);
        },
       */
        getFileUrl: function getFileUrl(fileInputId) {
            var uri = {
                url: '',
                filename: '',
                filetype: '',
                data: ''
            };

            var fileObj = typeof fileInputId === 'string' ? document.getElementById(fileInputId) : fileInputId;

            if (!utils.isCanUploadFileAsync || !fileObj) {
                return uri;
            }
            try {
                if (window.URL.createObjectURL) {
                    var fileItems = fileObj.files;
                    if (fileItems.length > 0) {
                        var u = fileItems.item(0);
                        uri.data = u;
                        uri.url = window.URL.createObjectURL(u);
                        uri.filename = u.name || '';
                    }
                } else {
                    // IE
                    var u = document.getElementById(fileInputId).value;
                    uri.url = u;
                    var pos1 = u.lastIndexOf('/');
                    var pos2 = u.lastIndexOf('\\');
                    var pos = Math.max(pos1, pos2);
                    if (pos < 0) uri.filename = u; else uri.filename = u.substring(pos + 1);
                }
                var index = uri.filename.lastIndexOf('.');
                if (index != -1) {
                    uri.filetype = uri.filename.substring(index + 1).toLowerCase();
                }
                return uri;
            } catch (e) {
                throw e;
            }
        },

        getFileSize: function getFileSize(file) {
            var fileSize = this.getFileLength(file);
            if (fileSize > 10000000) {
                return false;
            }
            var kb = Math.round(fileSize / 1000);
            if (kb < 1000) {
                fileSize = kb + ' KB';
            } else if (kb >= 1000) {
                var mb = kb / 1000;
                if (mb < 1000) {
                    fileSize = mb.toFixed(1) + ' MB';
                } else {
                    var gb = mb / 1000;
                    fileSize = gb.toFixed(1) + ' GB';
                }
            }
            return fileSize;
        },

        getFileLength: function getFileLength(file) {
            var fileLength = 0;
            if (file) {
                if (file.files) {
                    if (file.files.length > 0) {
                        fileLength = file.files[0].size;
                    }
                } else if (file.select && 'ActiveXObject' in window) {
                    file.select();
                    var fileobject = new ActiveXObject('Scripting.FileSystemObject');
                    var file = fileobject.GetFile(file.value);
                    fileLength = file.Size;
                }
            }
            return fileLength;
        },

        hasFlash: _hasFlash,

        trim: function trim(str) {

            str = typeof str === 'string' ? str : '';

            return str.trim ? str.trim() : str.replace(/^\s|\s$/g, '');
        },

        parseEmoji: function parseEmoji(msg) {
            if (typeof WebIM.Emoji === 'undefined' || typeof WebIM.Emoji.map === 'undefined') {
                return msg;
            } else {
                var emoji = WebIM.Emoji,
                    reg = null;

                for (var face in emoji.map) {
                    if (emoji.map.hasOwnProperty(face)) {
                        while (msg.indexOf(face) > -1) {
                            msg = msg.replace(face, '<img class="emoji" src="' + emoji.path + emoji.map[face] + '" />');
                        }
                    }
                }
                return msg;
            }
        },

        parseLink: function parseLink(msg) {

            var reg = /(https?\:\/\/|www\.)([a-zA-Z0-9-]+(\.[a-zA-Z0-9]+)+)(\:[0-9]{2,4})?\/?((\.[:_0-9a-zA-Z-]+)|[:_0-9a-zA-Z-]*\/?)*\??[:_#@*&%0-9a-zA-Z-/=]*/gm;

            msg = msg.replace(reg, function (v) {

                var prefix = /^https?/gm.test(v);

                return "<a href='" + (prefix ? v : '//' + v) + "' target='_blank'>" + v + "</a>";
            });

            return msg;
        },

        parseJSON: function parseJSON(data) {

            if (window.JSON && window.JSON.parse) {
                return window.JSON.parse(data + '');
            }

            var requireNonComma,
                depth = null,
                str = utils.trim(data + '');

            return str && !utils.trim(str.replace(/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g, function (token, comma, open, close) {

                if (requireNonComma && comma) {
                    depth = 0;
                }

                if (depth === 0) {
                    return token;
                }

                requireNonComma = open || comma;
                depth += !close - !open;
                return '';
            })) ? Function('return ' + str)() : Function('Invalid JSON: ' + data)();
        },

        parseUploadResponse: function parseUploadResponse(response) {
            return response.indexOf('callback') > -1 ? //lte ie9
                response.slice(9, -1) : response;
        },

        parseDownloadResponse: function parseDownloadResponse(response) {
            return response && response.type && response.type === 'application/json' || 0 > Object.prototype.toString.call(response).indexOf('Blob') ? this.url + '?token=' : window.URL.createObjectURL(response);
        },

        uploadFile: function uploadFile(options) {
            var options = options || {};
            options.onFileUploadProgress = options.onFileUploadProgress || EMPTYFN;
            options.onFileUploadComplete = options.onFileUploadComplete || EMPTYFN;
            options.onFileUploadError = options.onFileUploadError || EMPTYFN;
            options.onFileUploadCanceled = options.onFileUploadCanceled || EMPTYFN;

            var acc = options.accessToken || this.context.accessToken;
            if (!acc) {
                options.onFileUploadError({
                    type: _code.WEBIM_UPLOADFILE_NO_LOGIN,
                    id: options.id
                });
                return;
            }

            var orgName, appName, devInfos;
            // var appKey = options.appKey || this.context.appKey || '';

            // if (appKey) {
            //     devInfos = appKey.split('#');
            //     orgName = devInfos[0];
            //     appName = devInfos[1];
            // }

            if (!orgName && !appName) {
                options.onFileUploadError({
                    type: _code.WEBIM_UPLOADFILE_ERROR,
                    id: options.id
                });
                return;
            }

            var apiUrl = options.apiUrl;
            var uploadUrl = apiUrl + '/' + orgName + '/' + appName + '/chatfiles';

            if (!utils.isCanUploadFileAsync) {
                if (utils.hasFlash && typeof options.flashUpload === 'function') {
                    options.flashUpload && options.flashUpload(uploadUrl, options);
                } else {
                    options.onFileUploadError({
                        type: _code.WEBIM_UPLOADFILE_BROWSER_ERROR,
                        id: options.id
                    });
                }
                return;
            }

            var fileSize = options.file.data ? options.file.data.size : undefined;
            if (fileSize > WEBIM_FILESIZE_LIMIT) {
                options.onFileUploadError({
                    type: _code.WEBIM_UPLOADFILE_ERROR,
                    id: options.id
                });
                return;
            } else if (fileSize <= 0) {
                options.onFileUploadError({
                    type: _code.WEBIM_UPLOADFILE_ERROR,
                    id: options.id
                });
                return;
            }

            var xhr = utils.xmlrequest();
            var onError = function onError(e) {
                options.onFileUploadError({
                    type: _code.WEBIM_UPLOADFILE_ERROR,
                    id: options.id,
                    xhr: xhr
                });
            };
            if (xhr.upload) {
                xhr.upload.addEventListener('progress', options.onFileUploadProgress, false);
            }
            if (xhr.addEventListener) {
                xhr.addEventListener('abort', options.onFileUploadCanceled, false);
                xhr.addEventListener('load', function (e) {
                    try {
                        var json = utils.parseJSON(xhr.responseText);
                        try {
                            options.onFileUploadComplete(json);
                        } catch (e) {
                            options.onFileUploadError({
                                type: _code.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,
                                data: e
                            });
                        }
                    } catch (e) {
                        options.onFileUploadError({
                            type: _code.WEBIM_UPLOADFILE_ERROR,
                            data: xhr.responseText,
                            id: options.id,
                            xhr: xhr
                        });
                    }
                }, false);
                xhr.addEventListener('error', onError, false);
            } else if (xhr.onreadystatechange) {
                xhr.onreadystatechange = function () {
                    if (xhr.readyState === 4) {
                        if (ajax.status === 200) {
                            try {
                                var json = utils.parseJSON(xhr.responseText);
                                options.onFileUploadComplete(json);
                            } catch (e) {
                                options.onFileUploadError({
                                    type: _code.WEBIM_UPLOADFILE_ERROR,
                                    data: xhr.responseText,
                                    id: options.id,
                                    xhr: xhr
                                });
                            }
                        } else {
                            options.onFileUploadError({
                                type: _code.WEBIM_UPLOADFILE_ERROR,
                                data: xhr.responseText,
                                id: options.id,
                                xhr: xhr
                            });
                        }
                    } else {
                        xhr.abort();
                        options.onFileUploadCanceled();
                    }
                };
            }

            xhr.open('POST', uploadUrl);

            xhr.setRequestHeader('restrict-access', 'true');
            xhr.setRequestHeader('Accept', '*/*'); // Android QQ browser has some problem with this attribute.
            xhr.setRequestHeader('Authorization', 'Bearer ' + acc);

            var formData = new FormData();
            formData.append('file', options.file.data);
            // fix: ie8 status error
            window.XDomainRequest && (xhr.readyState = 2);
            xhr.send(formData);
        },

        download: function download(options) {
            options.onFileDownloadComplete = options.onFileDownloadComplete || EMPTYFN;
            options.onFileDownloadError = options.onFileDownloadError || EMPTYFN;

            var accessToken = options.accessToken || this.context.accessToken;
            if (!accessToken) {
                options.onFileDownloadError({
                    type: _code.WEBIM_DOWNLOADFILE_NO_LOGIN,
                    id: options.id
                });
                return;
            }

            var onError = function onError(e) {
                options.onFileDownloadError({
                    type: _code.WEBIM_DOWNLOADFILE_ERROR,
                    id: options.id,
                    xhr: xhr
                });
            };

            if (!utils.isCanDownLoadFile) {
                options.onFileDownloadComplete();
                return;
            }
            var xhr = utils.xmlrequest();
            if ('addEventListener' in xhr) {
                xhr.addEventListener('load', function (e) {
                    options.onFileDownloadComplete(xhr.response, xhr);
                }, false);
                xhr.addEventListener('error', onError, false);
            } else if ('onreadystatechange' in xhr) {
                xhr.onreadystatechange = function () {
                    if (xhr.readyState === 4) {
                        if (ajax.status === 200) {
                            options.onFileDownloadComplete(xhr.response, xhr);
                        } else {
                            options.onFileDownloadError({
                                type: _code.WEBIM_DOWNLOADFILE_ERROR,
                                id: options.id,
                                xhr: xhr
                            });
                        }
                    } else {
                        xhr.abort();
                        options.onFileDownloadError({
                            type: _code.WEBIM_DOWNLOADFILE_ERROR,
                            id: options.id,
                            xhr: xhr
                        });
                    }
                };
            }

            var method = options.method || 'GET';
            var resType = options.responseType || 'blob';
            var mimeType = options.mimeType || 'text/plain; charset=x-user-defined';
            xhr.open(method, options.url);
            if (typeof Blob !== 'undefined') {
                xhr.responseType = resType;
            } else {
                xhr.overrideMimeType(mimeType);
            }

            var innerHeaer = {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/octet-stream',
                'share-secret': options.secret,
                'Authorization': 'Bearer ' + accessToken
            };
            var headers = options.headers || {};
            for (var key in headers) {
                innerHeaer[key] = headers[key];
            }
            for (var key in innerHeaer) {
                if (innerHeaer[key]) {
                    xhr.setRequestHeader(key, innerHeaer[key]);
                }
            }
            // fix: ie8 status error
            window.XDomainRequest && (xhr.readyState = 2);
            xhr.send(null);
        },

        parseTextMessage: function parseTextMessage(message, faces) {
            if (typeof message !== 'string') {
                return;
            }

            if (Object.prototype.toString.call(faces) !== '[object Object]') {
                return {
                    isemoji: false,
                    body: [{
                        type: 'txt',
                        data: message
                    }]
                };
            }

            var receiveMsg = message;
            var emessage = [];
            var expr = /\[[^[\]]{2,3}\]/mg;
            var emoji = receiveMsg.match(expr);

            if (!emoji || emoji.length < 1) {
                return {
                    isemoji: false,
                    body: [{
                        type: 'txt',
                        data: message
                    }]
                };
            }
            var isemoji = false;
            for (var i = 0; i < emoji.length; i++) {
                var tmsg = receiveMsg.substring(0, receiveMsg.indexOf(emoji[i])),
                    existEmoji = WebIM.Emoji.map[emoji[i]];

                if (tmsg) {
                    emessage.push({
                        type: 'txt',
                        data: tmsg
                    });
                }
                if (!existEmoji) {
                    emessage.push({
                        type: 'txt',
                        data: emoji[i]
                    });
                    continue;
                }
                var emojiStr = WebIM.Emoji.map ? WebIM.Emoji.path + existEmoji : null;

                if (emojiStr) {
                    isemoji = true;
                    emessage.push({
                        type: 'emoji',
                        data: emojiStr
                    });
                } else {
                    emessage.push({
                        type: 'txt',
                        data: emoji[i]
                    });
                }
                var restMsgIndex = receiveMsg.indexOf(emoji[i]) + emoji[i].length;
                receiveMsg = receiveMsg.substring(restMsgIndex);
            }
            if (receiveMsg) {
                emessage.push({
                    type: 'txt',
                    data: receiveMsg
                });
            }
            if (isemoji) {
                return {
                    isemoji: isemoji,
                    body: emessage
                };
            }
            return {
                isemoji: false,
                body: [{
                    type: 'txt',
                    data: message
                }]
            };
        },

        parseUri: function parseUri() {
            var pattern = /([^\?|&])\w+=([^&]+)/g;
            var uri = {};
            if (window.location.search) {
                var args = window.location.search.match(pattern);
                for (var i in args) {
                    var str = args[i];
                    var eq = str.indexOf('=');
                    var key = str.substr(0, eq);
                    var value = str.substr(eq + 1);
                    uri[key] = value;
                }
            }
            return uri;
        },

        parseHrefHash: function parseHrefHash() {
            var pattern = /([^\#|&])\w+=([^&]+)/g;
            var uri = {};
            if (window.location.hash) {
                var args = window.location.hash.match(pattern);
                for (var i in args) {
                    var str = args[i];
                    var eq = str.indexOf('=');
                    var key = str.substr(0, eq);
                    var value = str.substr(eq + 1);
                    uri[key] = value;
                }
            }
            return uri;
        },

        xmlrequest: _xmlrequest,

        getXmlFirstChild: function getXmlFirstChild(data, tagName) {
            var children = data.getElementsByTagName(tagName);
            if (children.length == 0) {
                return null;
            } else {
                return children[0];
            }
        },
        ajax: function ajax(options) {
            var dataType = options.dataType || 'text';
            var suc = options.success || EMPTYFN;
            var error = options.error || EMPTYFN;
            var xhr = utils.xmlrequest();

            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    var status = xhr.status || 0;
                    if (status === 200) {
                        try {
                            switch (dataType) {
                                case 'text':
                                    suc(xhr.responseText);
                                    return;
                                case 'json':
                                    var json = utils.parseJSON(xhr.responseText);
                                    suc(json, xhr);
                                    return;
                                case 'xml':
                                    if (xhr.responseXML && xhr.responseXML.documentElement) {
                                        suc(xhr.responseXML.documentElement, xhr);
                                    } else {
                                        error({
                                            type: _code.WEBIM_CONNCTION_AJAX_ERROR,
                                            data: xhr.responseText
                                        });
                                    }
                                    return;
                            }
                            suc(xhr.response || xhr.responseText, xhr);
                        } catch (e) {
                            error({
                                type: _code.WEBIM_CONNCTION_AJAX_ERROR,
                                data: e
                            });
                        }
                        return;
                    } else {
                        error({
                            type: _code.WEBIM_CONNCTION_AJAX_ERROR,
                            data: xhr.responseText
                        });
                        return;
                    }
                }
                if (xhr.readyState === 0) {
                    error({
                        type: _code.WEBIM_CONNCTION_AJAX_ERROR,
                        data: xhr.responseText
                    });
                }
            };

            if (options.responseType) {
                if (xhr.responseType) {
                    xhr.responseType = options.responseType;
                }
            }
            if (options.mimeType) {
                if (utils.hasOverrideMimeType) {
                    xhr.overrideMimeType(options.mimeType);
                }
            }

            var type = options.type || 'POST',
                data = options.data || null,
                tempData = '';

            if (type.toLowerCase() === 'get' && data) {
                for (var o in data) {
                    if (data.hasOwnProperty(o)) {
                        tempData += o + '=' + data[o] + '&';
                    }
                }
                tempData = tempData ? tempData.slice(0, -1) : tempData;
                options.url += (options.url.indexOf('?') > 0 ? '&' : '?') + (tempData ? tempData + '&' : tempData) + '_v=' + new Date().getTime();
                data = null;
                tempData = null;
            }
            xhr.open(type, options.url, utils.isCanSetRequestHeader);

            if (utils.isCanSetRequestHeader) {
                var headers = options.headers || {};
                for (var key in headers) {
                    if (headers.hasOwnProperty(key)) {
                        xhr.setRequestHeader(key, headers[key]);
                    }
                }
            }
            // fix: ie8 status error
            window.XDomainRequest && (xhr.readyState = 2);
            xhr.send(data);
            return xhr;
        },
        ts: function ts() {
            var d = new Date();
            var Hours = d.getHours(); //获取当前小时数(0-23)
            var Minutes = d.getMinutes(); //获取当前分钟数(0-59)
            var Seconds = d.getSeconds(); //获取当前秒数(0-59)
            var Milliseconds = d.getMilliseconds(); //获取当前毫秒
            return (Hours < 10 ? "0" + Hours : Hours) + ':' + (Minutes < 10 ? "0" + Minutes : Minutes) + ':' + (Seconds < 10 ? "0" + Seconds : Seconds) + ':' + Milliseconds + ' ';
        },

        getObjectKey: function getObjectKey(obj, val) {
            for (var key in obj) {
                if (obj[key] == val) {
                    return key;
                }
            }
            return '';
        },

        sprintf: function sprintf() {
            var arg = arguments,
                str = arg[0] || '',
                i,
                len;
            for (i = 1, len = arg.length; i < len; i++) {
                str = str.replace(/%s/, arg[i]);
            }
            return str;
        },

        setCookie: function setCookie(name, value, days) {
            var cookie = name + '=' + encodeURIComponent(value);
            if (typeof days == 'number') {
                cookie += '; max-age: ' + days * 60 * 60 * 24;
            }
            document.cookie = cookie;
        },

        getCookie: function getCookie() {
            var allCookie = {};
            var all = document.cookie;
            if (all === "") {
                return allCookie;
            }
            var list = all.split("; ");
            for (var i = 0; i < list.length; i++) {
                var cookie = list[i];
                var p = cookie.indexOf('=');
                var name = cookie.substring(0, p);
                var value = cookie.substring(p + 1);
                value = decodeURIComponent(value);
                allCookie[name] = value;
            }
            return allCookie;
        }
    };
// })();

var _getJid = function _getJid(options, conn) {
    var jid = options.toJid || '';

    if (jid === '') {
        // var appKey = conn.context.appKey || '';
        var toJid = options.to + '@' + conn.domain;

        if (options.resource) {
            toJid = toJid + '/' + options.resource;
        }
        jid = toJid;
    }
    return jid;
};

/**
 * 删除联系人
 *
 * @param {Object} options
 * @param {String} options.to - 想要删除的联系人ID
 * @param {Function} options.success - 成功回调，在这里面调用connection.unsubscribed才能真正删除联系人
 * @fires connection#unsubscribed
 */
connection.prototype.removeRoster = function (options) {
    var jid = _getJid(options, this);
    var iq = $iq({ type: 'set' })
            .c('query', { xmlns: 'jabber:iq:roster' })
            .c('item', {
                jid: jid,
                subscription: 'remove'
            });
    var suc = options.success || _utils.emptyfn;
    var error = options.error || _utils.emptyfn;
    this.context.stropheConn.sendIQ(iq, suc, error);
};

/**
 * 获取联系人
 * @param {Object} options
 * @param {Function} options.success - 获取好友列表成功
 */
connection.prototype.getRoster = function (options) {
    var dom = $iq({
        type: 'get'
    }).c('query', { xmlns: 'jabber:iq:roster' });

    var options = options || {};
    var suc = options.success || this.onRoster;
    var completeFn = function completeFn(ele) {
        var rouster = [];
        var msgBodies = ele.getElementsByTagName('query');
        if (msgBodies && msgBodies.length > 0) {
            var queryTag = msgBodies[0];
            rouster = _parseFriend(queryTag);
        }
        suc(rouster, ele);
    };
    var error = options.error || this.onError;
    var failFn = function failFn(ele) {
        error({
            type: _code.WEBIM_CONNCTION_GETROSTER_ERROR,
            data: ele
        });
    };
    if (this.isOpened()) {
        this.context.stropheConn.sendIQ(dom.tree(), completeFn, failFn);
    } else {
        error({
            type: _code.WEBIM_CONNCTION_DISCONNECTED
        });
    }
};

/**
 * 订阅和反向订阅
 * @example
 *
 * A订阅B（A添加B为好友）
 * A执行：
 *  conn.subscribe({
	                to: 'B',
	                message: 'Hello~'
	            });
 B的监听函数onPresence参数message.type == subscribe监听到有人订阅他
 B执行：
 conn.subscribed({
	                to: 'A',
	                message: '[resp:true]'
	          });
 同意A的订阅请求
 B继续执行：
 conn.subscribe({
	                to: 'A',
	                message: '[resp:true]'
	            });
 反向订阅A，这样才算双方添加好友成功。
 若B拒绝A的订阅请求，只需执行：
 conn.unsubscribed({
	                        to: 'A',
	                        message: 'I don't want to be subscribed'
	                    });
 另外，在监听函数onPresence参数message.type == "subscribe"这个case中，加一句
 if (message && message.status === '[resp:true]') {
	            return;
	        }
 否则会进入死循环
 *
 * @param {Object} options - 想要订阅的联系人信息
 * @param {String} options.to - 想要订阅的联系人ID
 * @param {String} options.message - 发送给想要订阅的联系人的验证消息
 */
connection.prototype.subscribe = function (options) {
    var jid = _getJid(options, this);
    var pres = $pres({ to: jid, type: 'subscribe' });
    if (options.message) {
        pres.c('status').t(options.message).up();
    }
    if (options.nick) {
        pres.c('nick', { 'xmlns': 'http://jabber.org/protocol/nick' }).t(options.nick);
    }
    this.sendCommand(pres.tree());
};

/**
 * 被订阅后确认同意被订阅
 * @param {Object} options - 订阅人的信息
 * @param {String} options.to - 订阅人的ID
 * @param {String} options.message=[resp:true] - 默认为[resp:true]，后续将去掉该参数
 */
connection.prototype.subscribed = function (options) {
    var message = '[resp:true]';
    var jid = _getJid(options, this);
    var pres = $pres({ to: jid, type: 'subscribed' });

    if (options.message) {
        pres.c('status').t(options.message).up();
    }
    this.sendCommand(pres.tree());
};

/**
 * 拒绝对方的订阅请求
 * @function connection#event:unsubscribed
 * @param {Object} options -
 * @param {String} options.to - 订阅人的ID
 */
connection.prototype.unsubscribed = function (options) {
    var jid = _getJid(options, this);
    var pres = $pres({ to: jid, type: 'unsubscribed' });

    if (options.message) {
        pres.c('status').t(options.message).up();
    }
    this.sendCommand(pres.tree());
};

/**
 * 通过RestFul API根据groupId获取群组详情
 * @param {Object} opt -
 * @param {string} opt.groupId - 群组ID
 * @since 1.4.11
 */
connection.prototype.getGroupInfo = function (opt) {
    var options = {
        url: this.apiUrl + '/' + this.orgName + '/' + this.appName + '/chatgroups/' + opt.groupId,
        type: 'GET',
        dataType: 'json',
        headers: {
            'Authorization': 'Bearer ' + this.token,
            'Content-Type': 'application/json'
        }
    };
    options.success = opt.success || _utils.emptyfn;
    options.error = opt.error || _utils.emptyfn;
    WebIM.utils.ajax(options);
};

/**
 * 创建群组
 * @param {Object} options -
 * @deprecated
 * @example
 * 1. 创建申请 -> 得到房主身份
 * 2. 获取房主信息 -> 得到房间form
 * 3. 完善房间form -> 创建成功
 * 4. 添加房间成员
 * 5. 消息通知成员
 */
connection.prototype.createGroup = function (options) {
    this.groupOption = options;
    var roomId = +new Date();
    var toRoom = this._getGroupJid(roomId);
    var to = toRoom + '/' + this.context.userId;

    var pres = $pres({ to: to }).c('x', { xmlns: 'http://jabber.org/protocol/muc' }).up().c('create', { xmlns: 'http://jabber.org/protocol/muc' }).up();

    // createGroupACK
    this.sendCommand(pres.tree());
};

/**
 * send message
 * @param {Object} messageSource - 由 Class Message 生成
 */
connection.prototype.send = function (messageSource) {
    var self = this;
    var message = messageSource;
    if (message.type === 'txt') {
        if (this.encrypt.type === 'base64') {
            message = _.clone(messageSource);
            message.msg = btoa(message.msg);
        } else if (this.encrypt.type === 'aes') {
            message = _.clone(messageSource);
            var key = CryptoJS.enc.Utf8.parse(this.encrypt.key);
            var iv = CryptoJS.enc.Utf8.parse(this.encrypt.iv);
            var mode = this.encrypt.mode.toLowerCase();
            var option = {};
            if (mode === 'cbc') {
                option = {
                    iv: iv,
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.Pkcs7
                };
            } else if (mode === 'ebc') {
                option = {
                    mode: CryptoJS.mode.ECB,
                    padding: CryptoJS.pad.Pkcs7
                };
            }
            var encryptedData = CryptoJS.AES.encrypt(message.msg, key, option);

            message.msg = encryptedData.toString();
        }
    }
    if (this.isWindowSDK) {
        WebIM.doQuery('{"type":"sendMessage","to":"' + message.to + '","message_type":"' + message.type + '","msg":"' + encodeURI(message.msg) + '","chatType":"' + message.chatType + '"}', function (response) {}, function (code, msg) {
            var message = {
                data: {
                    data: "send"
                },
                type: _code.WEBIM_MESSAGE_SED_ERROR
            };
            self.onError(message);
        });
    } else {
        if (Object.prototype.toString.call(message) === '[object Object]') {
            var appKey = this.context.appKey || '';
            var toJid = appKey + '_' + message.to + '@' + this.domain;

            if (message.group) {
                toJid = appKey + '_' + message.to + '@conference.' + this.domain;
            }
            if (message.resource) {
                toJid = toJid + '/' + message.resource;
            }

            message.toJid = toJid;
            message.id = message.id || this.getUniqueId();
            _msgHash[message.id] = new _message(message);
            _msgHash[message.id].send(this);
        } else if (typeof message === 'string') {
            _msgHash[message] && _msgHash[message].send(this);
        }
    }
};

/**
 * 通过RestFul API发出入群申请
 * @param {Object} opt -
 * @param {string} opt.groupId - 群组ID
 * @since 1.4.11
 */
connection.prototype.joinGroup = function (opt) {
    var options = {
        url: this.apiUrl + '/' + this.orgName + '/' + this.appName + '/' + 'chatgroups' + '/' + opt.groupId + '/' + 'apply',
        type: 'POST',
        dataType: 'json',
        headers: {
            'Authorization': 'Bearer ' + this.token,
            'Content-Type': 'application/json'
        }
    };
    options.success = opt.success || _utils.emptyfn;
    options.error = opt.error || _utils.emptyfn;
    WebIM.utils.ajax(options);
};

/**
 * 通过RestFul API同意用户加入群
 * @param {Object} opt -
 * @param {string} opt.applicant - 申请加群的用户名
 * @param {Object} opt.groupId - 群组ID
 */
connection.prototype.agreeJoinGroup = function (opt) {
    var groupId = opt.groupId,
        requestData = {
            "applicant": opt.applicant,
            "verifyResult": true,
            "reason": "no clue"
        },
        options = {
            url: this.apiUrl + '/' + this.orgName + '/' + this.appName + '/' + 'chatgroups' + '/' + groupId + '/' + 'apply_verify',
            type: 'POST',
            dataType: "json",
            data: JSON.stringify(requestData),
            headers: {
                'Authorization': 'Bearer ' + this.token,
                'Content-Type': 'application/json'
            }
        };
    options.success = opt.success || _utils.emptyfn;
    options.error = opt.error || _utils.emptyfn;
    WebIM.utils.ajax(options);
};

/**
 * 通过RestFul API接口创建群组
 * @param opt {Object} - 群组信息
 * @param opt.data.groupname {string} - 群组名
 * @param opt.data.desc {string} - 群组描述
 * @param opt.data.members {string[]} - 群好友列表
 * @param opt.data.public {Boolean} - true: 公开群，false: 私有群
 * @param opt.data.approval {Boolean} - 前提：opt.data.public=true, true: 加群需要审批，false: 加群无需审批
 * @param opt.data.allowinvites {Boolean} - 前提：opt.data.public=false, true: 允许成员邀请入群，false: 不允许成员邀请入群
 * @since 1.4.11
 */
connection.prototype.createGroupNew = function (opt) {
    opt.data.owner = ""+this.user+"";
    opt.data.maxusers = parseInt(opt.data.maxusers);
    var options = {
        url: this.apiUrl + '/' + this.orgName + '/' + this.appName + '/chatgroups',
        dataType: 'json',
        type: 'POST',
        data: JSON.stringify(opt.data),
        headers: {
            'Authorization': 'Bearer ' + this.token,
            'Content-Type': 'application/json'
        }
    };
    options.success = function (respData) {
        opt.success(respData);
        this.onCreateGroup(respData);
    }.bind(this);
    options.error = opt.error || _utils.emptyfn;
    WebIM.utils.ajax(options);
};

var Message = function Message(type) {
    if (!this instanceof Message) {
        return new Message(type);
    }

    this._msg = {};

    if (typeof Message[type] === 'function') {
        Message[type].prototype.setGroup = this.setGroup;
        this._msg = new Message[type](0);
    }
    return this._msg;
};
Message.prototype.setGroup = function (group) {
    this.body.group = group;
};

/*
 * Read Message
 */
Message.read = function () {
    // this.id = id;
    this.type = 'read';
};

Message.read.prototype.set = function (opt) {
    this.body = {
        ackId: opt.id,
        to: opt.to
    };
};

/*
 * deliver message
 */
Message.delivery = function () {
    // this.id = id;
    this.type = 'delivery';
};

Message.delivery.prototype.set = function (opt) {
    this.body = {
        bodyId: opt.id,
        to: opt.to
    };
};

/*
 * text message
 */
Message.txt = function () {
    // this.id = id;
    this.type = 'txt';
    this.body = {};
};
Message.txt.prototype.set = function (opt) {
    this.value = opt.msg;
    this.body = {
        id: this.id,
        to: opt.to,
        msg: this.value,
        type: this.type,
        roomType: opt.roomType,
        ext: opt.ext || {},
        success: opt.success,
        fail: opt.fail
    };

    !opt.roomType && delete this.body.roomType;
};

/*
 * cmd message
 */
Message.cmd = function () {
    // this.id = id;
    this.type = 'cmd';
    this.body = {};
};
Message.cmd.prototype.set = function (opt) {
    this.value = '';

    this.body = {
        to: opt.to,
        action: opt.action,
        msg: this.value,
        type: this.type,
        roomType: opt.roomType,
        ext: opt.ext || {},
        success: opt.success
    };
    !opt.roomType && delete this.body.roomType;
};

/*
 * loc message
 */
Message.location = function () {
    // this.id = id;
    this.type = 'loc';
    this.body = {};
};
Message.location.prototype.set = function (opt) {
    this.body = {
        to: opt.to,
        type: this.type,
        roomType: opt.roomType,
        addr: opt.addr,
        lat: opt.lat,
        lng: opt.lng,
        ext: opt.ext || {}
    };
};

/*
 * img message
 */
Message.img = function () {
    // this.id = id;
    this.type = 'img';
    this.body = {};
};
Message.img.prototype.set = function (opt) {
    opt.file = opt.file || _utils.getFileUrl(opt.fileInputId);

    this.value = opt.file;

    this.body = {
        // id: this.id,
        file: this.value,
        apiUrl: opt.apiUrl,
        to: opt.to,
        type: this.type,
        ext: opt.ext || {},
        roomType: opt.roomType,
        onFileUploadError: opt.onFileUploadError,
        onFileUploadComplete: opt.onFileUploadComplete,
        success: opt.success,
        fail: opt.fail,
        flashUpload: opt.flashUpload,
        width: opt.width,
        height: opt.height,
        body: opt.body,
        uploadError: opt.uploadError,
        uploadComplete: opt.uploadComplete
    };

    !opt.roomType && delete this.body.roomType;
};

/*
 * audio message
 */
Message.audio = function () {
    // this.id = id;
    this.type = 'audio';
    this.body = {};
};
Message.audio.prototype.set = function (opt) {
    opt.file = opt.file || _utils.getFileUrl(opt.fileInputId);

    this.value = opt.file;
    this.filename = opt.filename || this.value.filename;

    this.body = {
        // id: this.id,
        file: this.value,
        filename: this.filename,
        apiUrl: opt.apiUrl,
        to: opt.to,
        type: this.type,
        ext: opt.ext || {},
        length: opt.length || 0,
        roomType: opt.roomType,
        file_length: opt.file_length,
        onFileUploadError: opt.onFileUploadError,
        onFileUploadComplete: opt.onFileUploadComplete,
        success: opt.success,
        fail: opt.fail,
        flashUpload: opt.flashUpload,
        body: opt.body
    };
    !opt.roomType && delete this.body.roomType;
};

/*
 * file message
 */
Message.file = function () {
    // this.id = id;
    this.type = 'file';
    this.body = {};
};
Message.file.prototype.set = function (opt) {
    opt.file = opt.file || _utils.getFileUrl(opt.fileInputId);

    this.value = opt.file;
    this.filename = opt.filename || this.value.filename;

    this.body = {
        // id: this.id,
        file: this.value,
        filename: this.filename,
        apiUrl: opt.apiUrl,
        to: opt.to,
        type: this.type,
        ext: opt.ext || {},
        roomType: opt.roomType,
        onFileUploadError: opt.onFileUploadError,
        onFileUploadComplete: opt.onFileUploadComplete,
        success: opt.success,
        fail: opt.fail,
        flashUpload: opt.flashUpload,
        body: opt.body
    };
    !opt.roomType && delete this.body.roomType;
};

/*
 * video message
 */
Message.video = function () {};
Message.video.prototype.set = function (opt) {};

var _Message = function _Message(message) {

    if (!this instanceof _Message) {
        return new _Message(message, conn);
    }

    this.msg = message;
};

_Message.prototype.send = function (conn) {
    var me = this;

    var _send = function _send(message) {

        message.ext = message.ext || {};
        message.ext.weichat = message.ext.weichat || {};
        message.ext.weichat.originType = message.ext.weichat.originType || 'webim';

        var dom;
        var json = {
            from: conn.context.userId || '',
            to: message.to,
            bodies: [message.body],
            ext: message.ext || {}
        };
        var jsonstr = _utils.stringify(json);
        dom = $msg({
            type: message.group || 'chat',
            to: message.toJid,
            id: message.id,
            xmlns: 'jabber:client'
        }).c('body').t(jsonstr);

        if (message.roomType) {
            dom.up().c('roomtype', { xmlns: 'easemob:x:roomtype', type: 'chatroom' });
        }
        if (message.bodyId) {
            dom = $msg({
                from: conn.context.jid || '',
                to: message.toJid,
                id: message.id,
                xmlns: 'jabber:client'
            }).c('body').t(message.bodyId);
            var delivery = {
                xmlns: 'urn:xmpp:receipts',
                id: message.bodyId
            };
            dom.up().c('delivery', delivery);
        }
        if (message.ackId) {

            if (conn.context.jid.indexOf(message.toJid) >= 0) {
                return;
            }
            dom = $msg({
                from: conn.context.jid || '',
                to: message.toJid,
                id: message.id,
                xmlns: 'jabber:client'
            }).c('body').t(message.ackId);
            var read = {
                xmlns: 'urn:xmpp:receipts',
                id: message.ackId
            };
            dom.up().c('acked', read);
        }

        setTimeout(function () {
            if (typeof _msgHash !== 'undefined' && _msgHash[message.id]) {
                _msgHash[message.id].msg.fail instanceof Function && _msgHash[message.id].msg.fail(message.id);
            }
        }, 60000);
        conn.sendCommand(dom.tree(), message.id);
    };

    if (me.msg.file) {
        if (me.msg.body && me.msg.body.url) {
            // Only send msg
            _send(me.msg);
            return;
        }
        var _tmpComplete = me.msg.onFileUploadComplete;
        var _complete = function _complete(data) {
            if (data.entities[0]['file-metadata']) {
                var file_len = data.entities[0]['file-metadata']['content-length'];
                // me.msg.file_length = file_len;
                me.msg.filetype = data.entities[0]['file-metadata']['content-type'];
                if (file_len > 204800) {
                    me.msg.thumbnail = true;
                }
            }

            me.msg.body = {
                type: me.msg.type || 'file',

                url: (location.protocol != 'https:' && conn.isHttpDNS ? conn.apiUrl + data.uri.substr(data.uri.indexOf("/", 9)) : data.uri) + '/' + data.entities[0]['uuid'],
                secret: data.entities[0]['share-secret'],
                filename: me.msg.file.filename || me.msg.filename,
                size: {
                    width: me.msg.width || 0,
                    height: me.msg.height || 0
                },
                length: me.msg.length || 0,
                file_length: me.msg.ext.file_length || 0,
                filetype: me.msg.filetype
            };
            _send(me.msg);
            _tmpComplete instanceof Function && _tmpComplete(data, me.msg.id);
        };

        me.msg.onFileUploadComplete = _complete;
        _utils.uploadFile.call(conn, me.msg);
    } else {
        me.msg.body = {
            type: me.msg.type === 'chat' ? 'txt' : me.msg.type,
            msg: me.msg.msg
        };
        if (me.msg.type === 'cmd') {
            me.msg.body.action = me.msg.action;
        } else if (me.msg.type === 'loc') {
            me.msg.body.addr = me.msg.addr;
            me.msg.body.lat = me.msg.lat;
            me.msg.body.lng = me.msg.lng;
        }

        _send(me.msg);
    }
};