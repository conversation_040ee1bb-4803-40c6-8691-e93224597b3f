JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {comp jSplitPane1::t::400:x}"
			"$verticalGroup": "par l {comp jSplitPane1::t::300:x}"
		} ) {
			name: "this"
			"preferredSize": new java.awt.Dimension( 500, 291 )
			"minimumSize": new java.awt.Dimension( 200, 150 )
			add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
				name: "jSplitPane1"
				"dividerLocation": 150
				"orientation": 0
				add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
					name: "winnersPane"
					add( new FormComponent( "javax.swing.JTable" ) {
						name: "winnersTable"
						auxiliary() {
							"JavaCodeGenerator.preInitCode": "${field}.setModel(new SelectedBehaviorsTableModel());"
						}
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "right"
				} )
				add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
					name: "coalitionsPane1"
					add( new FormComponent( "javax.swing.JTable" ) {
						name: "behaviorsTable"
						auxiliary() {
							"JavaCodeGenerator.preInitCode": "${field}.setModel(new BehaviorTableModel());"
						}
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "left"
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 400, 300 )
		} )
	}
}
