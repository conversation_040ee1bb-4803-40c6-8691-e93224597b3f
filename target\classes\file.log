[framework] 2022-10-14 00:54:36,247 - io.netty.util.internal.logging.InternalLoggerFactory -0    [main] DEBUG io.netty.util.internal.logging.InternalLoggerFactory  - Using Log4J as the default logging framework
[framework] 2022-10-14 00:54:36,248 - io.netty.util.ResourceLeakDetector -1    [main] DEBUG io.netty.util.ResourceLeakDetector  - -Dio.netty.leakDetection.level: simple
[framework] 2022-10-14 00:54:36,248 - io.netty.util.ResourceLeakDetector -1    [main] DEBUG io.netty.util.ResourceLeakDetector  - -Dio.netty.leakDetection.targetRecords: 4
[framework] 2022-10-14 00:54:36,253 - io.netty.util.internal.PlatformDependent0 -6    [main] DEBUG io.netty.util.internal.PlatformDependent0  - -Dio.netty.noUnsafe: false
[framework] 2022-10-14 00:54:36,253 - io.netty.util.internal.PlatformDependent0 -6    [main] DEBUG io.netty.util.internal.PlatformDependent0  - Java version: 14
[framework] 2022-10-14 00:54:36,254 - io.netty.util.internal.PlatformDependent0 -7    [main] DEBUG io.netty.util.internal.PlatformDependent0  - sun.misc.Unsafe.theUnsafe: available
[framework] 2022-10-14 00:54:36,254 - io.netty.util.internal.PlatformDependent0 -7    [main] DEBUG io.netty.util.internal.PlatformDependent0  - sun.misc.Unsafe.copyMemory: available
[framework] 2022-10-14 00:54:36,254 - io.netty.util.internal.PlatformDependent0 -7    [main] DEBUG io.netty.util.internal.PlatformDependent0  - java.nio.Buffer.address: available
[framework] 2022-10-14 00:54:36,256 - io.netty.util.internal.PlatformDependent0 -9    [main] DEBUG io.netty.util.internal.PlatformDependent0  - direct buffer constructor: available
[framework] 2022-10-14 00:54:36,256 - io.netty.util.internal.PlatformDependent0 -9    [main] DEBUG io.netty.util.internal.PlatformDependent0  - java.nio.Bits.unaligned: available, true
[framework] 2022-10-14 00:54:36,257 - io.netty.util.internal.PlatformDependent0 -10   [main] DEBUG io.netty.util.internal.PlatformDependent0  - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): available
[framework] 2022-10-14 00:54:36,257 - io.netty.util.internal.PlatformDependent0 -10   [main] DEBUG io.netty.util.internal.PlatformDependent0  - java.nio.DirectByteBuffer.<init>(long, int): available
[framework] 2022-10-14 00:54:36,257 - io.netty.util.internal.PlatformDependent -10   [main] DEBUG io.netty.util.internal.PlatformDependent  - sun.misc.Unsafe: available
[framework] 2022-10-14 00:54:36,258 - io.netty.util.internal.PlatformDependent -11   [main] DEBUG io.netty.util.internal.PlatformDependent  - maxDirectMemory: 14998831104 bytes (maybe)
[framework] 2022-10-14 00:54:36,258 - io.netty.util.internal.PlatformDependent -11   [main] DEBUG io.netty.util.internal.PlatformDependent  - -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir)
[framework] 2022-10-14 00:54:36,258 - io.netty.util.internal.PlatformDependent -11   [main] DEBUG io.netty.util.internal.PlatformDependent  - -Dio.netty.bitMode: 64 (sun.arch.data.model)
[framework] 2022-10-14 00:54:36,258 - io.netty.util.internal.PlatformDependent -11   [main] DEBUG io.netty.util.internal.PlatformDependent  - Platform: Windows
[framework] 2022-10-14 00:54:36,259 - io.netty.util.internal.PlatformDependent -12   [main] DEBUG io.netty.util.internal.PlatformDependent  - -Dio.netty.maxDirectMemory: 14998831104 bytes
[framework] 2022-10-14 00:54:36,259 - io.netty.util.internal.PlatformDependent -12   [main] DEBUG io.netty.util.internal.PlatformDependent  - -Dio.netty.uninitializedArrayAllocationThreshold: 1024
[framework] 2022-10-14 00:54:36,260 - io.netty.util.internal.CleanerJava9 -13   [main] DEBUG io.netty.util.internal.CleanerJava9  - java.nio.ByteBuffer.cleaner(): available
[framework] 2022-10-14 00:54:36,260 - io.netty.util.internal.PlatformDependent -13   [main] DEBUG io.netty.util.internal.PlatformDependent  - -Dio.netty.noPreferDirect: false
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.numHeapArenas: 32
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.numDirectArenas: 32
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.pageSize: 8192
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.maxOrder: 11
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.chunkSize: 16777216
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.smallCacheSize: 256
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.normalCacheSize: 64
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.cacheTrimInterval: 8192
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.useCacheForAllThreads: true
[framework] 2022-10-14 00:54:36,260 - io.netty.buffer.PooledByteBufAllocator -13   [main] DEBUG io.netty.buffer.PooledByteBufAllocator  - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
[framework] 2022-10-14 00:54:36,263 - io.netty.util.internal.InternalThreadLocalMap -16   [main] DEBUG io.netty.util.internal.InternalThreadLocalMap  - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
[framework] 2022-10-14 00:54:36,263 - io.netty.util.internal.InternalThreadLocalMap -16   [main] DEBUG io.netty.util.internal.InternalThreadLocalMap  - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
