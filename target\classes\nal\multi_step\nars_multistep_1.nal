'****** Mixed inference
<swan --> bird>.
<swan --> swimmer>.
50
<bird --> swimmer>?
80
<gull --> bird>.
<gull --> swimmer>.
80
<bird --> [feathered]>.
<robin --> [feathered]>.
80
<robin --> bird>?
100
<robin --> swimmer>?
7000
''outputMustContain('<bird --> swimmer>. %1.00;0.45%')
//''outputMustContain('<bird --> swimmer>. %1.00;0.62%')
''outputMustContain('<robin --> bird>. %1.00;0.45%')
''outputMustContain('<robin --> swimmer>. %1.00;0.28%')
