{"id": "graphxr@0.1.0", "nodes": {"6": {"id": 6, "data": {"category": "*", "properties": ["category_group_health"], "property_constraint": {"category_group_health": [{"operator": "greater_than", "value": ""}]}}, "inputs": {"gxr_label": {"connections": []}, "category_group_health": {"connections": [{"node": 7, "output": "property_constraint", "data": {}}]}}, "outputs": {"gxr_label": {"connections": []}}, "position": [353, 312], "name": "Category"}, "7": {"id": 7, "data": {"property": "category_group_health", "operator": "greater_than", "value": ""}, "inputs": {}, "outputs": {"property_constraint": {"connections": [{"node": 6, "input": "category_group_health", "data": {}}]}}, "position": [100, 280], "name": "PropertyFilter"}, "8": {"id": 8, "data": {"category": "Category", "properties": [], "property_constraint": {}}, "inputs": {"gxr_label": {"connections": []}}, "outputs": {"gxr_label": {"connections": [{"node": 9, "input": "gxr_label", "data": {}}]}}, "position": [30, 112], "name": "Category"}, "9": {"id": 9, "data": {"relationship": "link_to", "properties": [], "property_constraint": {}}, "inputs": {"gxr_label": {"connections": [{"node": 8, "output": "gxr_label", "data": {}}]}}, "outputs": {"gxr_label": {"connections": [{"node": 10, "input": "gxr_label", "data": {}}]}}, "position": [198, 197], "name": "Relationship"}, "10": {"id": 10, "data": {"category": "Category", "properties": [], "property_constraint": {}}, "inputs": {"gxr_label": {"connections": [{"node": 9, "output": "gxr_label", "data": {}}]}}, "outputs": {"gxr_label": {"connections": []}}, "position": [218, 40], "name": "Category"}, "11": {"id": 11, "data": {"category": "Category", "properties": [], "property_constraint": {}}, "inputs": {"gxr_label": {"connections": []}}, "outputs": {"gxr_label": {"connections": []}}, "position": [20, 25], "name": "Category"}}}