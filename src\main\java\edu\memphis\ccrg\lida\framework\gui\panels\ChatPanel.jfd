JFDML JFormDesigner: "6.0.6.2.195" Java: "11.0.3" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
			"$horizontalGroup": "par l {comp jSplitPane1::t::400:x}"
			"$verticalGroup": "par l {seq l {space :0:0:p, comp jSplitPane1::::300:x}}"
		} ) {
			name: "this"
			"preferredSize": new java.awt.Dimension( 350, 300 )
			add( new FormContainer( "javax.swing.JSplitPane", new FormLayoutManager( class javax.swing.JSplitPane ) ) {
				name: "jSplitPane1"
				"dividerLocation": 255
				"orientation": 0
				"dividerSize": 2
				add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
					name: "textPane"
					"minimumSize": new java.awt.Dimension( 150, 100 )
					"preferredSize": new java.awt.Dimension( 150, 100 )
					add( new FormComponent( "javax.swing.JTextPane" ) {
						name: "textPane1"
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "left"
				} )
				add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class javax.swing.GroupLayout ) {
					"$horizontalGroup": "par l {seq {space :p:7:p, comp scrollPane1:::p:296:p, space u:::p, comp button1:::p::p, space :::p}}"
					"$verticalGroup": "par l {seq {space :::x, par l {comp scrollPane1:::::x, seq {comp button1:::p::p, space :0:0:x}}, space :::x}}"
				} ) {
					name: "panel1"
					"preferredSize": new java.awt.Dimension( 400, 44 )
					"minimumSize": new java.awt.Dimension( 150, 44 )
					add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
						name: "scrollPane1"
						add( new FormComponent( "javax.swing.JTextPane" ) {
							name: "textPane2"
							"minimumSize": new java.awt.Dimension( 700, 30 )
						} )
					} )
					add( new FormComponent( "javax.swing.JButton" ) {
						name: "button1"
						"text": "text"
					} )
				}, new FormLayoutConstraints( class java.lang.String ) {
					"value": "right"
				} )
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 400, 300 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
