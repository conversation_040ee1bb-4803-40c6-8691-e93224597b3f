'********** [03 + 28 -> 30]

'If the robot hold t002, then arrive t001 and open it, t001 will be opened. 
<(&/,<(*,SELF,{t002}) --> hold>,<(*,SELF,{t001}) --> at>,(^open,{SELF},{t001})) =/> <{t001} --> [opened]>>. %1.00;0.90%

'The robot is holding t002 now.
<(*,SELF,{t002}) --> hold>. :|:

200

'If the robot arrive t001 and open it, t001 may be opened. 
''outputMustContain('<(&/,<(*,SELF,{t001}) --> at>,(^open,{SELF},{t001})) =/> <{t001} --> [opened]>>. %1.00;0.43%')
